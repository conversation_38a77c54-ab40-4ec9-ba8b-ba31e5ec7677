package router

import (
	v1 "code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/api/v1"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/api/v1/rev"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/api/v1/sender"
	"code.ixdev.cn/liush/gin/wrapper"
	"net/http"
)

// MountRoutes 挂载路由
func MountRoutes(router *wrapper.RouterGroup) {
	router.RawHandle(http.MethodPost, "/api/v1/platform/audit_notify", new(v1.Platform).AuditNotify)
}

// MountServices 挂载服务
func MountServices() []any {
	return []any{
		new(sender.File),
		new(rev.File),
		new(rev.MockSenderProvider),
		new(rev.Sender),
		new(v1.Auth),
		new(v1.Sys),
		new(v1.SysConfig),
		new(v1.User),
	}
}
