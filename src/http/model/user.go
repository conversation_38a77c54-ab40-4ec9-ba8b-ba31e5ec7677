package model

import (
	"database/sql"
	"time"
)

type AuditResult string

const (
	AuditResultUnspecified AuditResult = ""
	AuditResultChecking    AuditResult = "待审核"
	AuditResultPass        AuditResult = "审核通过"
	AuditResultReject      AuditResult = "审核不通过"
)

type UserStatus int8

const (
	UserStatusDisabled    UserStatus = -1
	UserStatusUnspecified UserStatus = 0
	UserStatusNormal      UserStatus = 1
)

var (
	UserStatusText = map[UserStatus]string{
		UserStatusDisabled:    "禁用",
		UserStatusUnspecified: "未指定",
		UserStatusNormal:      "正常",
	}
)

const (
	AdminUsername = "admin"
)

type User struct {
	Id              int64          `json:"id" gorm:"primaryKey"`
	Username        string         `json:"username"`
	Password        string         `json:"password"`
	CompanyId       sql.NullString `json:"company_id"`
	Institution     string         `json:"institution"`
	Industry        string         `json:"industry"`
	AccessAddr      string         `json:"access_addr"`
	BelongingRegion string         `json:"belonging_region"`
	RealName        string         `json:"real_name"`
	IdNumber        string         `json:"id_number"`
	Email           sql.NullString `json:"email"`
	Phone           sql.NullString `json:"phone"`
	AuditResult     AuditResult    `json:"audit_result"`
	AuditComment    string         `json:"audit_comment"`
	IsAdmin         bool           `json:"is_admin"`
	Status          UserStatus     `json:"status"`
	CreatedAt       time.Time      `json:"created_at"`
	UpdatedAt       time.Time      `json:"updated_at"`
}

func (User) TableName() string {
	return "users"
}
