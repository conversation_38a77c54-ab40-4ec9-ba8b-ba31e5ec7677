package model

import (
	"database/sql"
	"gorm.io/gorm"
	"time"
)

type RevPackageCertDataFileDetail struct {
	FileHash          string `json:"file_hash"`
	FileWatermarkHash string `json:"file_watermark_hash"`
	FileName          string `json:"file_name"`
	FileSize          int64  `json:"file_size"`
	PersonId          string `json:"person_id"`
}

type RevPackageCertData struct {
	FileSendCode       string                          `json:"file_send_code"`
	SendAction         int                             `json:"send_action"`
	FileZipHash        string                          `json:"file_zip_hash"`
	SenderId           string                          `json:"sender_id"`
	DataProviderId     string                          `json:"data_provider_id"`
	SenderIpv4         string                          `json:"sender_ipv4"`
	SenderIpv6         string                          `json:"sender_ipv6"`
	SenderType         int                             `json:"sender_type"`
	DataReceiverIpv4   string                          `json:"data_receiver_ipv4"`
	DataReceiverIpv6   string                          `json:"data_receiver_ipv6"`
	DataReceiverDomain string                          `json:"data_receiver_domain"`
	DataUserId         string                          `json:"data_user_id"`
	Timestamp          int64                           `json:"timestamp"`
	ZipMima            string                          `json:"zip_mima"`
	FileDetail         []*RevPackageCertDataFileDetail `json:"file_detail"`
	SftpSend           []string                        `json:"sftp_send"`
	DataSenderInfo     string                          `json:"data_sender_info"`   // 发送方名称
	DataReceiverInfo   string                          `json:"data_receiver_info"` // 接收方名称
}

type RevPackageCert struct {
	Filename string              `json:"filename"`
	Filepath string              `json:"filepath"`
	Data     *RevPackageCertData `json:"data"`
}

type RevPackageZipFile struct {
	Filename    string `json:"filename"`
	Filepath    string `json:"filepath"`
	Filesize    int64  `json:"filesize"`
	IsEncrypted bool   `json:"is_encrypted,omitempty"`
}

type RevPackageZip struct {
	Filename string               `json:"filename"`
	Filepath string               `json:"filepath"`
	Filesize int64                `json:"filesize"`
	Files    []*RevPackageZipFile `json:"files"`
}

type RevPackageFileSftpSendFile struct {
	Filesize     int64  `json:"filesize"`
	FileRelPath  string `json:"file_rel_path"`
	FileBasename string `json:"file_basename"`
}

type RevPackage struct {
	Id              string                        `json:"id,omitempty" gorm:"primaryKey"`
	Filename        string                        `json:"filename"`
	Filepath        string                        `json:"filepath"`
	Filesize        int64                         `json:"filesize"`
	SendCode        string                        `json:"send_code"`
	SenderId        string                        `json:"sender_id"`
	Timestamp       int64                         `json:"timestamp"`
	TarHashVerified bool                          `json:"tar_hash_verified"`
	ZipHashVerified bool                          `json:"zip_hash_verified"`
	Cert            *RevPackageCert               `json:"cert" gorm:"serializer:json"`
	SftpSend        []*RevPackageFileSftpSendFile `json:"sftp_send" gorm:"serializer:json"`
	DcmPath         string                        `json:"dcm_path"`
	DcmPaths        []string                      `json:"dcm_paths" gorm:"serializer:json"`
	ZipControlPath  string                        `json:"zip_control_path"`
	Zips            []*RevPackageZip              `json:"zips" gorm:"serializer:json"`
	RecordReserved  bool                          `json:"record_reserved"`
	ArchivedAt      sql.NullTime                  `json:"archived_at"`
	CreatedAt       time.Time                     `json:"created_at,omitempty"`
	UpdatedAt       time.Time                     `json:"updated_at,omitempty"`
	DeletedAt       gorm.DeletedAt                `json:"deleted_at,omitempty"`
}

func (p *RevPackage) TableName() string {
	return "rev_packages"
}

//func (p *RevPackage) EsIndexName() string {
//	return "hk-box-rev-packages"
//}
//
//func (p *RevPackage) AfterCreate(_ *gorm.DB) error {
//	es, err := core.GetElasticsearch()
//	if err != nil {
//		return err
//	}
//	if _, err = es.Index(p.EsIndexName()).Id(p.Id).Document(p).Do(context.TODO()); err != nil {
//		return err
//	}
//	return nil
//}
//
//func (p *RevPackage) AfterUpdate(_ *gorm.DB) error {
//	es, err := core.GetElasticsearch()
//	if err != nil {
//		return err
//	}
//	if _, err = es.Update(p.EsIndexName(), p.Id).Doc(p).Do(context.TODO()); err != nil {
//		return err
//	}
//	return nil
//}
//
//func (p *RevPackage) AfterDelete(_ *gorm.DB) error {
//	es, err := core.GetElasticsearch()
//	if err != nil {
//		return err
//	}
//	//if _, err = es.Delete(p.EsIndexName(), p.Id).Do(context.TODO()); err != nil {
//	//	return err
//	//}
//	if _, err = es.Update(p.EsIndexName(), p.Id).Doc(p).Do(context.TODO()); err != nil {
//		return err
//	}
//	return nil
//}
