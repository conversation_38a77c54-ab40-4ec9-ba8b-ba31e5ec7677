package model

import (
	"gorm.io/gorm"
	"time"
)

const (
	FileSendStatusWaitSend int8 = iota
	FileSendStatusOk
	FileSendStatusFailed
)

type FileSendRecord struct {
	Id              int64          `json:"id" gorm:"primaryKey"`
	FileId          int64          `json:"file_id"`
	SendFilename    string         `json:"send_filename"`
	SendFilepath    string         `json:"send_filepath"`
	SendFileSize    int64          `json:"send_file_size"`
	SendFileHash    string         `json:"send_file_hash"`
	SendArchiveData any            `json:"send_archive_data" gorm:"serializer:json"`
	SendAt          time.Time      `json:"send_at"`
	TakeUpTime      int64          `json:"take_up_time"`
	Status          int8           `json:"status"`
	StatusDetail    any            `json:"status_detail" gorm:"serializer:json"`
	WmEnabled       bool           `json:"wm_enabled"`
	WmData          map[string]any `json:"wm_data" gorm:"serializer:json"`
	CreatedAt       time.Time      `json:"created_at"`
	UpdatedAt       time.Time      `json:"updated_at"`
	DeletedAt       gorm.DeletedAt `json:"deleted_at"`
}

func (FileSendRecord) TableName() string {
	return "file_send_record"
}
