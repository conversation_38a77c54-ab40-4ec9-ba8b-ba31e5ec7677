package model

import "time"

const (
	RevPackageRemoteUploadMethod_UNKNOWN = iota
	RevPackageRemoteUploadMethod_SFTP
	RevPackageRemoteUploadMethod_DICOM
)

type RevPackageRemoteUploadRecord struct {
	Id             int64     `json:"id" gorm:"primaryKey"`
	SendCode       string    `json:"send_code"`
	UploadMethod   int32     `json:"upload_method"`
	UploadFile     string    `json:"upload_file"`
	UploadDetail   any       `json:"upload_detail" gorm:"serializer:json"`
	UploadResult   int32     `json:"upload_result"`
	ResponseDetail any       `json:"response_detail" gorm:"serializer:json"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}

func (RevPackageRemoteUploadRecord) TableName() string {
	return "rev_package_remote_upload_record"
}
