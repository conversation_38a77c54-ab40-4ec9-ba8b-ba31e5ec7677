package model

import "time"

type RevPackageDownloadResult int8

const (
	RevPackageDownloadResultFailed      RevPackageDownloadResult = -1
	RevPackageDownloadResultUnspecified RevPackageDownloadResult = 0
	RevPackageDownloadResultSucceed     RevPackageDownloadResult = 1
)

type DownloadConsistencyCompareResult int8

const (
	DownloadConsistencyCompareResultFailed      DownloadConsistencyCompareResult = -1
	DownloadConsistencyCompareResultUnspecified DownloadConsistencyCompareResult = 0
	DownloadConsistencyCompareResultSucceed     DownloadConsistencyCompareResult = 1
)

// RevPackageDownloadRecord 下载记录
type RevPackageDownloadRecord struct {
	Id                               string                           `json:"id" gorm:"primaryKey"`
	FileId                           string                           `json:"file_id"`
	PackageId                        string                           `json:"package_id"`
	PackageFilepath                  string                           `json:"package_filepath"`
	PackageFilename                  string                           `json:"package_filename"`
	ZipFilepath                      string                           `json:"zip_filepath"`
	ZipFilename                      string                           `json:"zip_filename"`
	ChildFilepath                    string                           `json:"child_filepath"`
	ChildFilename                    string                           `json:"child_filename"`
	Username                         string                           `json:"username"`
	DownloadIp                       string                           `json:"download_ip"`
	DownloadUseragent                string                           `json:"download_useragent"`
	DownloadResult                   RevPackageDownloadResult         `json:"download_result"`
	DownloadDetail                   any                              `json:"download_detail" gorm:"serializer:json"`
	DownloadConsistencyCompareResult DownloadConsistencyCompareResult `json:"download_consistency_compare_result"`
	DownloadConsistencyCompareDetail any                              `json:"download_consistency_compare_detail" gorm:"serializer:json"`
	WmEnabled                        bool                             `json:"wm_enabled"`
	WmData                           map[string]any                   `json:"wm_data" gorm:"serializer:json"`
	DownloadAt                       time.Time                        `json:"download_at"`
	CreatedAt                        time.Time                        `json:"created_at"`
	UpdatedAt                        time.Time                        `json:"updated_at"`
}

func (r *RevPackageDownloadRecord) TableName() string {
	return "rev_package_download_records"
}
