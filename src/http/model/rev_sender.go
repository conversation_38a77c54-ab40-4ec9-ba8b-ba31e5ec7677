package model

import (
	"gorm.io/gorm"
	"time"
)

type RevSender struct {
	Id           string         `json:"id" gorm:"primaryKey"`
	SenderId     string         `json:"sender_id"`
	SenderName   string         `json:"sender_name"`
	SenderNamePy string         `json:"sender_name_py"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"deleted_at"`
}

func (r *RevSender) TableName() string {
	return "rev_senders"
}
