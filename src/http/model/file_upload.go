package model

import (
	"gorm.io/gorm"
	"time"
)

type FileUpload struct {
	Id            int64          `json:"id" gorm:"primaryKey"`
	Filename      string         `json:"filename"`
	LocalFilename string         `json:"local_filename"`
	Filepath      string         `json:"filepath"`
	FileSize      int64          `json:"file_size"`
	FileHash      string         `json:"file_hash"`
	Username      string         `json:"username"`
	CreatedAt     time.Time      `json:"created_at"`
	UpdatedAt     time.Time      `json:"updated_at"`
	DeletedAt     gorm.DeletedAt `json:"deleted_at"`
}

func (p *FileUpload) TableName() string {
	return "file_upload"
}

//func (p *FileUpload) EsIndexName() string {
//	return "hk-box-file-upload"
//}
//
//func (p *FileUpload) AfterCreate(_ *gorm.DB) error {
//	es, err := core.GetElasticsearch()
//	if err != nil {
//		return err
//	}
//	if _, err = es.Index(p.EsIndexName()).Id(strconv.FormatInt(p.Id, 10)).Document(p).Do(context.TODO()); err != nil {
//		return err
//	}
//	return nil
//}
//
//func (p *FileUpload) AfterUpdate(_ *gorm.DB) error {
//	es, err := core.GetElasticsearch()
//	if err != nil {
//		return err
//	}
//	if _, err = es.Update(p.EsIndexName(), strconv.FormatInt(p.Id, 10)).Doc(p).Do(context.TODO()); err != nil {
//		return err
//	}
//	return nil
//}
//
//func (p *FileUpload) AfterDelete(_ *gorm.DB) error {
//	es, err := core.GetElasticsearch()
//	if err != nil {
//		return err
//	}
//	if _, err = es.Delete(p.EsIndexName(), strconv.FormatInt(p.Id, 10)).Do(context.TODO()); err != nil {
//		return err
//	}
//	//if _, err = es.Update(p.EsIndexName(), p.Id).Doc(p).Do(context.TODO()); err != nil {
//	//	return err
//	//}
//
//	return nil
//}
