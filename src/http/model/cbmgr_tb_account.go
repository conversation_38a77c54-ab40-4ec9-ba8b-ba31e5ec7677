package model

//const (
//	CbMgrTbAccountStatusUnspecified = iota
//	CbMgrTbAccountStatusUnUpdatePwd
//	CbMgrTbAccountStatusNormal
//	CbMgrTbAccountStatusBanned
//	CbMgrTbAccountStatusRevoke
//)
//
//type CbMgrTbAccount struct {
//	Account       string `json:"account" xml:"account" yaml:"account" gorm:"primaryKey"`
//	AccountPwd    string `json:"account_pwd" xml:"account_pwd" yaml:"account_pwd"`
//	AccountStatus int    `json:"account_status" xml:"account_status" yaml:"account_status"`
//}
//
//func (CbMgrTbAccount) TableName() string {
//	return "tb_account_info"
//}
