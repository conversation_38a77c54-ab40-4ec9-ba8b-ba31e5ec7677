package validator

import (
	"regexp"
	"strings"
)

// MobileIsValid 是否是合法手机号
func MobileIsValid(mobile string) bool {
	p := regexp.MustCompile(`^1(3\d|4[5-9]|5[0-35-9]|6[2567]|7[0-8]|8\d|9[0-35-9])\d{8}$`)
	return p.MatchString(mobile)
}

// EmailIsValid 是否是合法邮箱
func EmailIsValid(email string) bool {
	p := regexp.MustCompile(`\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*`)
	return p.MatchString(email)
}

// UsernameIsValid 用户名是否合法：仅允许大小写中英文、数字，4-20个字符，中文算作两个字符
func UsernameIsValid(username string) bool {
	matchedAll := regexp.MustCompile("^[a-zA-Z0-9\u4e00-\u9fa5]+$").FindStringSubmatch(username)
	if len(matchedAll) == 0 {
		return false
	}
	allLen := len(strings.Split(matchedAll[0], ""))

	chineseNum := 0 // 中文个数
	matchedChinese := regexp.MustCompile("[\u4e00-\u9fa5]+").FindAllStringSubmatch(username, -1)
	for _, chinese := range matchedChinese {
		for _, s := range chinese {
			chineseNum += len(strings.Split(s, ""))
		}
	}
	chineseLen := chineseNum * 2 // 跟前端js保持一致，每个中文算作两个字符

	strLen := allLen - chineseNum + chineseLen

	return 4 <= strLen && strLen <= 20
}

// PasswordIsValid 密码是否合法：由字母、数字、特殊字符，任意2种组成，5-20位
func PasswordIsValid(password string) bool {
	fullMatchP := regexp.MustCompile("^[a-zA-Z0-9!@#$%^&*()_+{}|:\"<>?\\-=\\[\\]\\\\;',./]+$")
	enMatchP := regexp.MustCompile("[a-zA-Z]+")
	numMatchP := regexp.MustCompile("[0-9]+")
	specialMatchP := regexp.MustCompile("[!@#$%^&*()_+{}|:\"<>?\\-=\\[\\]\\\\;',./]+")
	if !fullMatchP.MatchString(password) {
		return false
	}
	typNum := 0
	if enMatchP.MatchString(password) {
		typNum += 1
	}
	if numMatchP.MatchString(password) {
		typNum += 1
	}
	if specialMatchP.MatchString(password) {
		typNum += 1
	}
	l := len(password)
	return typNum >= 2 && 5 <= l && l <= 20
}

// IdCardNumIsValid 是否是合法的身份证号码
func IdCardNumIsValid(value string) bool {
	return regexp.MustCompile("^[1-9]\\d{5}(19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[Xx\\d]$").MatchString(value)
}
