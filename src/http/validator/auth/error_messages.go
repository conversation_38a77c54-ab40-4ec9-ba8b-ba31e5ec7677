package auth

import "code.ixdev.cn/liush/xpb/errors"

var (
	ErrAuthUserNotFound         = errors.New("", "User not found", ErrCodeAuthUserNotFound)
	ErrAuthUserPasswordMismatch = errors.New("", "Password mismatch", ErrCodeAuthUserPasswordMismatch)
	ErrAuthUserSmsCodeExpired   = errors.New("", "SMS code expired", ErrCodeAuthUserSmsCodeExpired)
	ErrAuthTokenRequired        = errors.New("", "Token required", ErrCodeAuthTokenRequired)
	ErrAuthTokenIllegal         = errors.New("", "Token illegal", ErrCodeAuthTokenIllegal)
	ErrAuthTokenExpired         = errors.New("", "Token expired", ErrCodeAuthTokenExpired)
)
