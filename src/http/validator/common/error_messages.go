package common

import "code.ixdev.cn/liush/xpb/errors"

var (
	ErrInternalServerError = errors.New("", "Internal server error", ErrCodeInternalServerError)
	ErrInternalServerBusy  = errors.New("", "Internal server busy", ErrCodeInternalServerBusy)
	ErrRequestIllegal      = errors.New("", "Request illegal", ErrCodeRequestIllegal)
	ErrUploadFailed        = errors.New("", "Upload failed", ErrCodeUploadFailed)
	ErrFileTypeDisallowed  = errors.New("", "File type disallowed", ErrCodeFileTypeDisallowed)
	ErrFileTooSmall        = errors.New("", "File too small", ErrCodeFileTooSmall)
	ErrFileTooBig          = errors.New("", "File too big", ErrCodeFileTooBig)
)
