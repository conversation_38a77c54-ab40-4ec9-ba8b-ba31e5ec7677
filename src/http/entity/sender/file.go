package sender

import "code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/model"

type RemoteUploadFileInfo struct {
	FileHash  []map[string]any `json:"file_hash" xml:"file_hash" yaml:"file_hash"`
	OtherInfo any              `json:"other_info,omitempty" xml:"other_info" yaml:"other_info"`
}

type RemoteUploadSuccessResponse struct {
	Message  string `json:"message" xml:"message" yaml:"message"`
	FileName string `json:"file_name" xml:"file_name" yaml:"file_name"`
}

type FileListDatum struct {
	model.FileUpload
	SendStatus int8 `json:"send_status"`
}
