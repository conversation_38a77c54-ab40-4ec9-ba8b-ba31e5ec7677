package v1

import (
	"bytes"
	"database/sql"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"strings"
	"time"

	"code.ixdev.cn/cnix/cbdv/hk-box-be/core"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/global"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/global/metadata"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/model"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/request"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/response"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/validator"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/validator/common"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/utils/authutil"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/utils/cryptoutil"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/utils/tokenutil"
	"github.com/avast/retry-go/v4"
	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Auth
// @Router /api/v1/auth []
type Auth struct {
}

// Login
// @Router /login [post]
func (a *Auth) Login(ctx *gin.Context, args *request.AuthLoginRequest) (*response.AuthLoginResponse, error) {
	var err error
	if args == nil {
		args = &request.AuthLoginRequest{}
	}
	logger, err := core.GetLogger()
	if err != nil {
		log.Println(err)
		return nil, common.ErrInternalServerError
	}
	orm, err := core.GetDatabase(global.DatabaseConnectCbMgr)
	if err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}
	if args.Username = strings.TrimSpace(args.Username); len(args.Username) == 0 {
		return nil, fmt.Errorf("account cannot empty")
	}
	if args.Password = strings.TrimSpace(args.Password); len(args.Password) == 0 {
		return nil, fmt.Errorf("password cannot empty")
	}
	user := new(model.User)
	if err = orm.Where("username=?", args.Username).Limit(1).Find(user).Error; err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}
	if user.Username == "" {
		return nil, fmt.Errorf("account not exist")
	}
	if !cryptoutil.PasswordStringIsValid(user.Password, args.Password) {
		return nil, fmt.Errorf("account or password error")
	}
	if !user.IsAdmin {
		if user.Status != model.UserStatusNormal {
			return nil, fmt.Errorf("your password has been locked")
		}
	}

	sessionKey, err := tokenutil.GetNewSessionKey()
	if err != nil {
		logger.Error(err)
		return nil, common.ErrInternalServerError
	}

	now := time.Now()
	expiresAt := now.AddDate(0, 0, 1)
	t := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.RegisteredClaims{
		Issuer:    "cnix",
		Subject:   sessionKey,
		ExpiresAt: jwt.NewNumericDate(expiresAt),
		NotBefore: jwt.NewNumericDate(now),
		IssuedAt:  jwt.NewNumericDate(now),
		ID:        user.Username,
	})
	s, err := t.SignedString([]byte(metadata.AuthJwtKey))
	if err != nil {
		logger.Error(err)
		return nil, common.ErrInternalServerError
	}

	redisClient, err := core.GetRedis()
	if err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}

	if err = redisClient.Set(ctx, sessionKey, s, expiresAt.Sub(now)).Err(); err != nil {
		logger.Error(err)
		return nil, common.ErrInternalServerError
	}

	membersKey := tokenutil.GetSessionMembersKey(user.Username)
	if members := redisClient.SMembers(ctx, membersKey).Val(); members != nil && len(members) > 0 {
		newMembers := make([]interface{}, 0)
		for _, member := range members {
			newMembers = append(newMembers, member)
		}
		if err = redisClient.SRem(ctx, membersKey, newMembers...).Err(); err != nil {
			logger.Error(err)
			return nil, common.ErrInternalServerError
		}
		if err = redisClient.Del(ctx, members...).Err(); err != nil {
			logger.Error(err)
			return nil, common.ErrInternalServerError
		}
	}

	if err = redisClient.SAdd(ctx, membersKey, sessionKey).Err(); err != nil {
		logger.Error(err)
		return nil, common.ErrInternalServerError
	}

	cookieMaxAge := int(expiresAt.Sub(now).Seconds())
	authutil.SetCookie(ctx.Writer, "m_auth_token", s, cookieMaxAge)
	authutil.SetCookie(ctx.Writer, "m_auth_uid", user.Username, cookieMaxAge)

	return nil, nil
}

// Logout
// @Router /logout [post]
func (a *Auth) Logout(ctx *gin.Context) error {
	err := authutil.LogoutByUid(ctx, authutil.GetUsername(ctx))
	if err != nil {
		return common.ErrInternalServerError
	}
	cookieMaxAge := -1
	authutil.SetCookie(ctx.Writer, "m_auth_token", "", cookieMaxAge)
	authutil.SetCookie(ctx.Writer, "m_auth_uid", "", cookieMaxAge)
	return nil
}

// Me
// @Router /me [post]
func (a *Auth) Me(ctx *gin.Context) (any, error) {
	var err error
	logger, err := core.GetLogger()
	if err != nil {
		log.Println(err)
		return nil, common.ErrInternalServerError
	}
	orm, err := core.GetDatabase()
	if err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}
	orm = orm.WithContext(ctx)

	username := authutil.GetUsername(ctx)
	user := new(model.User)
	if err = orm.Where("username=?", username).Limit(1).Find(user).Error; err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}
	if user.Username == "" {
		return nil, fmt.Errorf("account not exist")
	}

	return &response.AuthMe{
		Id:              user.Id,
		Username:        user.Username,
		CompanyId:       user.CompanyId.String,
		Institution:     user.Institution,
		Industry:        user.Industry,
		AccessAddr:      user.AccessAddr,
		BelongingRegion: user.BelongingRegion,
		RealName:        user.RealName,
		IdNumber:        user.IdNumber,
		Email:           user.Email.String,
		Phone:           user.Phone.String,
		AuditResult:     user.AuditResult,
		AuditComment:    user.AuditComment,
		IsAdmin:         user.IsAdmin,
		Status:          user.Status,
		CreatedAt:       user.CreatedAt.Format(time.DateTime),
		UpdatedAt:       user.UpdatedAt.Format(time.DateTime),
	}, nil
}

// Init
// @Router /init [post]
func (a *Auth) Init(ctx *gin.Context, args *request.AuthInitRequest) error {
	var err error
	if args == nil {
		args = &request.AuthInitRequest{}
	}
	logger, err := core.GetLogger()
	if err != nil {
		log.Println(err)
		return common.ErrInternalServerError
	}
	orm, err := core.GetDatabase()
	if err != nil {
		logger.Errorln(err)
		return common.ErrInternalServerError
	}
	orm = orm.WithContext(ctx)

	pool, err := core.GetRedisPool()
	if err != nil {
		logger.Errorln(err)
		return common.ErrInternalServerError
	}
	mutex := pool.NewMutex("AUTH:INIT")
	if err = mutex.Lock(); err != nil {
		logger.Errorln(err)
		return common.ErrInternalServerBusy
	}
	defer mutex.Unlock()

	username := authutil.GetUsername(ctx)
	if username != model.AdminUsername {
		return fmt.Errorf("非法操作")
	}

	adminUser := new(model.User)
	err = orm.Model(new(model.User)).Where("username=?", username).Where("is_admin", true).Limit(1).Find(adminUser).Error
	if err != nil {
		logger.Errorln(err)
		return common.ErrInternalServerError
	}
	if adminUser.Username == "" {
		return fmt.Errorf("系统数据不完善，请联系管理员")
	}

	switch adminUser.AuditResult {
	case model.AuditResultChecking:
		return fmt.Errorf("信息审核中，请稍后再试")
	case model.AuditResultPass:
		return fmt.Errorf("禁止重复初始化")
	default:
	}

	//if args.CompanyId = strings.TrimSpace(args.CompanyId); len(args.CompanyId) == 0 {
	//	return fmt.Errorf("企业id不能为空")
	//}
	if args.Institution = strings.TrimSpace(args.Institution); len(args.Institution) == 0 {
		return fmt.Errorf("机构名称不能为空")
	}
	if args.Industry = strings.TrimSpace(args.Industry); len(args.Industry) == 0 {
		return fmt.Errorf("所属行业不能为空")
	}
	if args.AccessAddr = strings.TrimSpace(args.AccessAddr); len(args.AccessAddr) == 0 {
		return fmt.Errorf("接入地点不能为空")
	}
	if args.BelongingRegion = strings.TrimSpace(args.BelongingRegion); len(args.BelongingRegion) == 0 {
		return fmt.Errorf("所属区域不能为空")
	}
	if args.RealName = strings.TrimSpace(args.RealName); len(args.RealName) == 0 {
		return fmt.Errorf("联系人姓名不能为空")
	}
	if args.IdNumber = strings.TrimSpace(args.IdNumber); len(args.IdNumber) == 0 {
		return fmt.Errorf("联系人身份证号不能为空")
	} else if !validator.IdCardNumIsValid(args.IdNumber) {
		return fmt.Errorf("联系人身份证号格式不正确")
	}
	if args.Email = strings.TrimSpace(args.Email); len(args.Email) == 0 {
		return fmt.Errorf("联系人邮箱不能为空")
	} else if !validator.EmailIsValid(args.Email) {
		return fmt.Errorf("联系人邮箱格式不正确")
	}
	if args.Phone = strings.TrimSpace(args.Phone); len(args.Phone) == 0 {
		return fmt.Errorf("联系人手机号码不能为空")
	} else if !validator.MobileIsValid(args.Phone) {
		return fmt.Errorf("联系人手机号码格式不正确")
	}

	cfg := core.GetConf().App
	platformCfg := cfg.Platform

	requestPayload := map[string]any{
		"scheme": "https",
		"domain": cfg.Domain,
		"report_data": map[string]any{
			"company_id":       adminUser.CompanyId.String,
			"institution":      args.Institution,
			"industry":         args.Industry,
			"access_addr":      args.AccessAddr,
			"belonging_region": args.BelongingRegion,
			"name":             args.RealName,
			"id_number":        args.IdNumber,
			"email":            args.Email,
			"phone":            args.Phone,
			"ip":               platformCfg.LocalIp,
			"port":             platformCfg.LocalPort,
		},
	}
	reportBytes, err := json.Marshal(requestPayload)
	if err != nil {
		logger.Errorln(err)
		return common.ErrInternalServerError
	}

	requestUUID, err := uuid.NewUUID()
	if err != nil {
		logger.Errorln(err)
		return common.ErrInternalServerError
	}
	requestId := requestUUID.String()

	err = orm.Transaction(func(tx *gorm.DB) error {
		//adminUser.CompanyId = sql.NullString{
		//	String: args.CompanyId,
		//	Valid:  args.CompanyId != "",
		//}
		adminUser.Institution = args.Institution
		adminUser.Industry = args.Industry
		adminUser.AccessAddr = args.AccessAddr
		adminUser.BelongingRegion = args.BelongingRegion
		adminUser.RealName = args.RealName
		adminUser.IdNumber = args.IdNumber
		adminUser.Email = sql.NullString{
			String: args.Email,
			Valid:  args.Email != "",
		}
		adminUser.Phone = sql.NullString{
			String: args.Phone,
			Valid:  args.Phone != "",
		}
		adminUser.AuditResult = model.AuditResultChecking
		adminUser.Status = model.UserStatusUnspecified

		updateFields := []string{
			"Institution",
			"Industry",
			"AccessAddr",
			"BelongingRegion",
			"RealName",
			"IdNumber",
			"Email",
			"Phone",
			"AuditResult",
			"Status",
		}

		if err = tx.Model(adminUser).Select(updateFields).Updates(adminUser).Error; err != nil {
			logger.Errorln(err)
			return common.ErrInternalServerError
		}
		if platformCfg.EnabledReport {
			logger.Debugw("report platform", "requestId", requestId, "data", string(reportBytes))

			respBody, err := retry.DoWithData(func() ([]byte, error) {
				resp, err := http.Post(platformCfg.ReportUrl, "application/json", bytes.NewReader(reportBytes))
				if err != nil {
					logger.Errorln(err)
					return nil, err
				}
				defer resp.Body.Close()
				return io.ReadAll(resp.Body)
			}, retry.Attempts(3))
			if err != nil {
				return common.ErrInternalServerError
			}
			logger.Debugw("report platform", "requestId", requestId, "data", requestPayload, "resp", string(respBody))
		}

		return nil
	})

	return err
}

// InitHistory
// @Router /init/history [get]
func (a *Auth) InitHistory(ctx *gin.Context) (any, error) {
	var err error
	logger, err := core.GetLogger()
	if err != nil {
		log.Println(err)
		return nil, common.ErrInternalServerError
	}
	orm, err := core.GetDatabase()
	if err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}
	orm = orm.WithContext(ctx)

	username := authutil.GetUsername(ctx)
	if username != model.AdminUsername {
		return nil, fmt.Errorf("非法操作")
	}
	user := new(model.User)
	if err = orm.Where("username=?", username).Limit(1).Find(user).Error; err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}
	if user.Username == model.AdminUsername {
		return nil, fmt.Errorf("用户不存在")
	}

	return &response.AuthInitHistory{
		CompanyId:       user.CompanyId.String,
		Institution:     user.Institution,
		Industry:        user.Industry,
		AccessAddr:      user.AccessAddr,
		BelongingRegion: user.BelongingRegion,
		RealName:        user.RealName,
		IdNumber:        user.IdNumber,
		Email:           user.Email.String,
		Phone:           user.Phone.String,
		AuditResult:     user.AuditResult,
		AuditComment:    user.AuditComment,
		IsAdmin:         user.IsAdmin,
		Status:          user.Status,
		CreatedAt:       user.CreatedAt.Format(time.DateTime),
		UpdatedAt:       user.UpdatedAt.Format(time.DateTime),
	}, nil
}

// InitResetPwd
// @Router /init/reset-pwd [post]
func (a *Auth) InitResetPwd(ctx *gin.Context, args *request.AuthInitResetPwd) error {
	var err error
	if args == nil {
		args = &request.AuthInitResetPwd{}
	}
	logger, err := core.GetLogger()
	if err != nil {
		log.Println(err)
		return common.ErrInternalServerError
	}
	orm, err := core.GetDatabase()
	if err != nil {
		logger.Errorln(err)
		return common.ErrInternalServerError
	}
	orm = orm.WithContext(ctx)

	username := authutil.GetUsername(ctx)
	if username != model.AdminUsername {
		return fmt.Errorf("非法操作")
	}

	adminUser := new(model.User)
	if err = orm.Where("username=?", username).Limit(1).Find(adminUser).Error; err != nil {
		logger.Errorln(err)
		return common.ErrInternalServerError
	}
	if adminUser.Username == "" {
		return fmt.Errorf("系统数据不完善，请联系管理员")
	}
	if adminUser.AuditResult != model.AuditResultPass {
		return fmt.Errorf("非法操作")
	}

	updateFields := make([]string, 0)
	if args.Pwd = strings.TrimSpace(args.Pwd); len(args.Pwd) != 0 {
		args.Pwd, err = cryptoutil.HashPasswordFromString(args.Pwd)
		if err != nil {
			logger.Errorln(err)
			return common.ErrInternalServerError
		}
		adminUser.Password = args.Pwd
		updateFields = append(updateFields, "Password")
	}
	adminUser.Status = model.UserStatusNormal
	updateFields = append(updateFields, "Status")

	if err = orm.Model(adminUser).Select(updateFields).Updates(adminUser).Error; err != nil {
		logger.Errorln(err)
		return common.ErrInternalServerError
	}
	return nil
}

// InitCancel
// @Router /init-cancel [post]
func (a *Auth) InitCancel(ctx *gin.Context) error {
	var err error
	logger, err := core.GetLogger()
	if err != nil {
		log.Println(err)
		return common.ErrInternalServerError
	}
	orm, err := core.GetDatabase()
	if err != nil {
		logger.Errorln(err)
		return common.ErrInternalServerError
	}
	orm = orm.WithContext(ctx)

	username := authutil.GetUsername(ctx)
	if username != model.AdminUsername {
		return fmt.Errorf("非法操作")
	}

	adminUser := new(model.User)
	if err = orm.Where("username=?", username).Limit(1).Find(adminUser).Error; err != nil {
		logger.Errorln(err)
		return common.ErrInternalServerError
	}
	if adminUser.Username == "" {
		return fmt.Errorf("系统数据不完善，请联系管理员")
	}
	if adminUser.AuditResult != model.AuditResultReject {
		return fmt.Errorf("非法操作")
	}

	//adminUser.CompanyId = sql.NullString{}
	adminUser.Institution = ""
	adminUser.Industry = ""
	adminUser.AccessAddr = ""
	adminUser.BelongingRegion = ""
	adminUser.RealName = ""
	adminUser.IdNumber = ""
	adminUser.Email = sql.NullString{}
	adminUser.Phone = sql.NullString{}
	adminUser.AuditResult = model.AuditResultUnspecified
	adminUser.AuditComment = ""
	adminUser.Status = model.UserStatusUnspecified

	updateFields := []string{
		"Institution",
		"Industry",
		"AccessAddr",
		"BelongingRegion",
		"RealName",
		"IdNumber",
		"Email",
		"Phone",
		"AuditResult",
		"AuditComment",
		"Status",
	}

	if err = orm.Model(adminUser).Select(updateFields).Updates(adminUser).Error; err != nil {
		logger.Errorln(err)
		return common.ErrInternalServerError
	}
	return nil
}
