package v1

import (
	"code.ixdev.cn/cnix/cbdv/hk-box-be/core"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/model"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/request"
	"database/sql"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"io"
	"log"
	"net/http"
)

// Platform
// @Router /api/v1/platform []
type Platform struct {
}

func (p *Platform) AuditNotify(ctx *gin.Context) {
	var err error
	logger, err := core.GetLogger()
	if err != nil {
		log.Println(err)
		ctx.AbortWithStatusJSON(http.StatusOK, gin.H{
			"code": http.StatusInternalServerError,
			"msg":  http.StatusText(http.StatusInternalServerError),
		})
		return
	}
	orm, err := core.GetDatabase()
	if err != nil {
		logger.Errorln(err)
		ctx.AbortWithStatusJSON(http.StatusOK, gin.H{
			"code": http.StatusInternalServerError,
			"msg":  http.StatusText(http.StatusInternalServerError),
		})
		return
	}
	orm = orm.WithContext(ctx)

	pool, err := core.GetRedisPool()
	if err != nil {
		logger.Errorln(err)
		logger.Errorln(err)
		ctx.AbortWithStatusJSON(http.StatusOK, gin.H{
			"code": http.StatusInternalServerError,
			"msg":  http.StatusText(http.StatusInternalServerError),
		})
		return
	}
	mutex := pool.NewMutex("PLATFORM:AUDIT_NOTIFY")
	if err = mutex.Lock(); err != nil {
		logger.Errorln(err)
		ctx.AbortWithStatusJSON(http.StatusOK, gin.H{
			"code": http.StatusTooManyRequests,
			"msg":  http.StatusText(http.StatusTooManyRequests),
		})
		return
	}
	defer mutex.Unlock()

	reqBodyBytes, err := io.ReadAll(ctx.Request.Body)
	if err != nil {
		logger.Errorln(err)
		ctx.AbortWithStatusJSON(http.StatusOK, gin.H{
			"code": http.StatusInternalServerError,
			"msg":  http.StatusText(http.StatusInternalServerError),
		})
		return
	}
	logger.Debugw("platform notify", "data", string(reqBodyBytes))

	params := new(request.PlatformAuditNotify)
	if err = json.Unmarshal(reqBodyBytes, params); err != nil {
		logger.Errorln(err)
		ctx.AbortWithStatusJSON(http.StatusOK, gin.H{
			"code": http.StatusInternalServerError,
			"msg":  http.StatusText(http.StatusInternalServerError),
		})
		return
	}

	adminUser := new(model.User)
	if err = orm.Where("username=?", model.AdminUsername).Where("is_admin", true).Limit(1).Find(adminUser).Error; err != nil {
		logger.Errorln(err)
		ctx.AbortWithStatusJSON(http.StatusOK, gin.H{
			"code": http.StatusInternalServerError,
			"msg":  http.StatusText(http.StatusInternalServerError),
		})
		return
	}
	if adminUser.Username == "" {
		ctx.AbortWithStatusJSON(http.StatusOK, gin.H{
			"code": http.StatusInternalServerError,
			"msg":  "admin user not found",
		})
		return
	}
	//if params.CompanyId != adminUser.CompanyId.String {
	//	logger.Errorln("admin user company id not match")
	//	ctx.AbortWithStatusJSON(http.StatusOK, gin.H{
	//		"code": http.StatusBadRequest,
	//		"msg":  "admin user company id not match",
	//	})
	//	return
	//}
	if params.AuditResult != string(model.AuditResultChecking) &&
		params.AuditResult != string(model.AuditResultPass) &&
		params.AuditResult != string(model.AuditResultReject) {
		logger.Errorln("audit result illegal")
		ctx.AbortWithStatusJSON(http.StatusOK, gin.H{
			"code": http.StatusBadRequest,
			"msg":  "audit result illegal",
		})
		return
	}

	adminUser.CompanyId = sql.NullString{
		String: params.CompanyId,
		Valid:  params.CompanyId != "",
	}
	adminUser.Institution = params.Institution
	adminUser.Industry = params.Industry
	adminUser.AccessAddr = params.AccessAddr
	adminUser.BelongingRegion = params.BelongingRegion
	adminUser.RealName = params.Name
	adminUser.IdNumber = params.IdNumber
	adminUser.Email = sql.NullString{
		String: params.Email,
		Valid:  params.Email != "",
	}
	adminUser.Phone = sql.NullString{
		String: params.Phone,
		Valid:  params.Phone != "",
	}
	adminUser.AuditResult = model.AuditResult(params.AuditResult)
	adminUser.AuditComment = params.AuditComment

	updateFields := []string{
		"CompanyId",
		"Institution",
		"Industry",
		"AccessAddr",
		"BelongingRegion",
		"RealName",
		"IdNumber",
		"Email",
		"Phone",
		"AuditResult",
		"AuditComment",
	}

	if err = orm.Model(adminUser).Select(updateFields).Save(adminUser).Error; err != nil {
		logger.Errorln(err)
		ctx.AbortWithStatusJSON(http.StatusOK, gin.H{
			"code": http.StatusInternalServerError,
			"msg":  http.StatusText(http.StatusInternalServerError),
		})
		return
	}
	ctx.JSON(http.StatusOK, gin.H{
		"code": 0,
		"msg":  http.StatusText(http.StatusOK),
	})
}
