package v1

import (
	"code.ixdev.cn/cnix/cbdv/hk-box-be/core"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/model"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/request"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/response"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/validator"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/validator/common"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/utils/authutil"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/utils/cryptoutil"
	"code.ixdev.cn/liush/xpb/pagination"
	"database/sql"
	"fmt"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"log"
	"strings"
	"time"
)

// User
// @Router /api/v1/user []
type User struct {
}

// List
// @Router /list [post]
func (u *User) List(ctx *gin.Context, args *request.UserList) (*response.UserList, error) {
	var err error
	if args == nil {
		args = &request.UserList{}
	}
	logger, err := core.GetLogger()
	if err != nil {
		log.Println(err)
		return nil, common.ErrInternalServerError
	}
	orm, err := core.GetDatabase()
	if err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}
	orm = orm.WithContext(ctx)

	total, data := int64(0), make([]*response.UserListDatum, 0)

	baseSql := `select %s
from users u`
	condition := make([]string, 0)
	bindings := make([]any, 0)

	if args.Account = strings.TrimSpace(args.Account); len(args.Account) != 0 {
		condition = append(condition, "(u.username like ? OR u.phone like ? OR u.email like ?)")
		kwd := fmt.Sprintf("%%%s%%", args.Account)
		bindings = append(bindings, kwd, kwd, kwd)
	}
	if args.CreatedAtStart = strings.TrimSpace(args.CreatedAtStart); len(args.CreatedAtStart) != 0 {
		condition = append(condition, "u.created_at >= ?")
		bindings = append(bindings, args.CreatedAtStart)
	}
	if args.CreatedAtEnd = strings.TrimSpace(args.CreatedAtEnd); len(args.CreatedAtEnd) != 0 {
		condition = append(condition, "u.created_at < ?")
		bindings = append(bindings, args.CreatedAtEnd)
	}

	if len(condition) > 0 {
		baseSql += ` where ` + strings.Join(condition, " and ")
	}

	countSql := fmt.Sprintf(baseSql, `count(1) as total`)
	if err := orm.Raw(countSql, bindings...).Scan(&total).Error; err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}

	dataSql := fmt.Sprintf(baseSql, `u.id,u.username,u.real_name,u.id_number,u.email,u.phone,u.status,u.is_admin,u.created_at,u.updated_at`)
	dataSql += fmt.Sprintf(" ORDER BY u.id DESC")
	dataSql += fmt.Sprintf(" LIMIT %d,%d", pagination.ParseOffset(args.Pagination), pagination.ParseLimit(args.Pagination))

	iter, err := orm.Raw(dataSql, bindings...).Rows()
	if err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}
	defer iter.Close()
	for iter.Next() {
		n := new(model.User)
		if err = orm.ScanRows(iter, n); err != nil {
			logger.Errorln(err)
			return nil, common.ErrInternalServerError
		}
		datum := &response.UserListDatum{
			Id:        n.Id,
			Username:  n.Username,
			RealName:  n.RealName,
			IdNumber:  n.IdNumber,
			Email:     n.Email.String,
			Phone:     n.Phone.String,
			Status:    n.Status,
			IsAdmin:   n.IsAdmin,
			CreatedAt: n.CreatedAt.Format(time.DateTime),
			UpdatedAt: n.UpdatedAt.Format(time.DateTime),
		}
		data = append(data, datum)
	}

	return &response.UserList{
		Total: total,
		Data:  data,
	}, nil
}

// Detail
// @Router /detail [post]
func (u *User) Detail(ctx *gin.Context, args *request.UserDetail) (*response.UserDetail, error) {
	var err error
	if args == nil {
		args = &request.UserDetail{}
	}
	logger, err := core.GetLogger()
	if err != nil {
		log.Println(err)
		return nil, common.ErrInternalServerError
	}
	orm, err := core.GetDatabase()
	if err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}
	orm = orm.WithContext(ctx)

	if args.Username = strings.TrimSpace(args.Username); len(args.Username) == 0 {
		return nil, fmt.Errorf("账号不能为空")
	}

	item := new(model.User)
	if err = orm.Where("username=?", args.Username).Limit(1).Find(item).Error; err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}
	if item.Username == "" {
		return nil, fmt.Errorf("账号不存在")
	}
	return &response.UserDetail{
		Id:              item.Id,
		Username:        item.Username,
		CompanyId:       item.CompanyId.String,
		Institution:     item.Institution,
		Industry:        item.Industry,
		AccessAddr:      item.AccessAddr,
		BelongingRegion: item.BelongingRegion,
		RealName:        item.RealName,
		IdNumber:        item.IdNumber,
		Email:           item.Email.String,
		Phone:           item.Phone.String,
		AuditResult:     item.AuditResult,
		AuditComment:    item.AuditComment,
		IsAdmin:         item.IsAdmin,
		Status:          item.Status,
		CreatedAt:       item.CreatedAt.Format(time.DateTime),
		UpdatedAt:       item.UpdatedAt.Format(time.DateTime),
	}, nil
}

// Create
// @Router /create [post]
func (u *User) Create(ctx *gin.Context, args *request.UserCreate) (any, error) {
	var err error
	if args == nil {
		args = &request.UserCreate{}
	}
	logger, err := core.GetLogger()
	if err != nil {
		log.Println(err)
		return nil, common.ErrInternalServerError
	}
	orm, err := core.GetDatabase()
	if err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}
	orm = orm.WithContext(ctx)

	if args.Username = strings.TrimSpace(args.Username); len(args.Username) == 0 {
		return nil, fmt.Errorf("账号不能为空")
	}
	if args.Password = strings.TrimSpace(args.Password); len(args.Password) == 0 {
		return nil, fmt.Errorf("密码不能为空")
	}
	if args.IdNumber = strings.TrimSpace(args.IdNumber); len(args.IdNumber) != 0 {
		if !validator.IdCardNumIsValid(args.IdNumber) {
			return nil, fmt.Errorf("身份证号码不合法")
		}
	}
	if args.Email = strings.TrimSpace(args.Email); len(args.Email) != 0 {
		if !validator.EmailIsValid(args.Email) {
			return nil, fmt.Errorf("邮箱不合法")
		}
	}
	if args.Phone = strings.TrimSpace(args.Phone); len(args.Phone) != 0 {
		if !validator.MobileIsValid(args.Phone) {
			return nil, fmt.Errorf("手机号码不合法")
		}
	}

	pool, err := core.GetRedisPool()
	if err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}
	mutex := pool.NewMutex("USER:CREATE")
	if err := mutex.Lock(); err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerBusy
	}
	defer mutex.Unlock()

	tryUsername := new(model.User)
	if err = orm.Where("username=?", args.Username).Limit(1).Find(tryUsername).Error; err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}
	if tryUsername.Username != "" {
		return nil, fmt.Errorf("账号已存在")
	}

	if args.Email != "" {
		tryEmail := new(model.User)
		if err = orm.Where("email=?", args.Email).Limit(1).Find(tryEmail).Error; err != nil {
			logger.Errorln(err)
			return nil, common.ErrInternalServerError
		}
		if tryEmail.Email.String != "" {
			return nil, fmt.Errorf("邮箱已存在")
		}
	}
	if args.Phone != "" {
		tryPhone := new(model.User)
		if err = orm.Where("phone=?", args.Phone).Limit(1).Find(tryPhone).Error; err != nil {
			logger.Errorln(err)
			return nil, common.ErrInternalServerError
		}
		if tryPhone.Phone.String != "" {
			return nil, fmt.Errorf("手机号码已存在")
		}
	}

	args.Password, err = cryptoutil.HashPasswordFromString(args.Password)
	if err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}

	data := &model.User{
		Id:              core.GetNextId(),
		Username:        args.Username,
		Password:        args.Password,
		CompanyId:       sql.NullString{},
		Institution:     "",
		Industry:        "",
		AccessAddr:      "",
		BelongingRegion: "",
		RealName:        args.RealName,
		IdNumber:        args.IdNumber,
		Email: sql.NullString{
			String: args.Email,
			Valid:  args.Email != "",
		},
		Phone: sql.NullString{
			String: args.Phone,
			Valid:  args.Phone != "",
		},
		AuditResult:  "",
		AuditComment: "",
		IsAdmin:      false,
		Status:       model.UserStatusDisabled,
	}
	if args.Enabled {
		data.Status = model.UserStatusNormal
	}

	err = orm.Transaction(func(tx *gorm.DB) error {
		if err = tx.Create(data).Error; err != nil {
			logger.Errorln(err)
			return common.ErrInternalServerError
		}
		return nil
	})

	if err != nil {
		return nil, err
	}

	return data.Id, nil
}

// Update
// @Router /update [post]
func (u *User) Update(ctx *gin.Context, args *request.UserUpdate) error {
	var err error
	if args == nil {
		args = &request.UserUpdate{}
	}
	logger, err := core.GetLogger()
	if err != nil {
		log.Println(err)
		return common.ErrInternalServerError
	}
	orm, err := core.GetDatabase()
	if err != nil {
		logger.Errorln(err)
		return common.ErrInternalServerError
	}
	orm = orm.WithContext(ctx)

	if args.Username = strings.TrimSpace(args.Username); len(args.Username) == 0 {
		return fmt.Errorf("账号不能为空")
	}
	if args.Password = strings.TrimSpace(args.Password); len(args.Password) != 0 {
		args.Password, err = cryptoutil.HashPasswordFromString(args.Password)
		if err != nil {
			logger.Errorln(err)
			return common.ErrInternalServerError
		}
	}
	if args.IdNumber = strings.TrimSpace(args.IdNumber); len(args.IdNumber) != 0 {
		if !validator.IdCardNumIsValid(args.IdNumber) {
			return fmt.Errorf("身份证号码不合法")
		}
	}
	if args.Email = strings.TrimSpace(args.Email); len(args.Email) != 0 {
		if !validator.EmailIsValid(args.Email) {
			return fmt.Errorf("邮箱不合法")
		}
	}
	if args.Phone = strings.TrimSpace(args.Phone); len(args.Phone) != 0 {
		if !validator.MobileIsValid(args.Phone) {
			return fmt.Errorf("手机号码不合法")
		}
	}

	pool, err := core.GetRedisPool()
	if err != nil {
		logger.Errorln(err)
		return common.ErrInternalServerError
	}
	mutex := pool.NewMutex("USER:UPDATE:" + args.Username)
	if err := mutex.Lock(); err != nil {
		logger.Errorln(err)
		return common.ErrInternalServerBusy
	}
	defer mutex.Unlock()

	item := new(model.User)
	if err = orm.Where("username=?", args.Username).Limit(1).Find(item).Error; err != nil {
		logger.Errorln(err)
		return common.ErrInternalServerError
	}
	if item.Username == "" {
		return fmt.Errorf("账号不存在")
	}
	if args.Email != "" {
		tryEmail := new(model.User)
		if err = orm.Where("email=?", args.Email).Limit(1).Find(tryEmail).Error; err != nil {
			logger.Errorln(err)
			return common.ErrInternalServerError
		}
		if tryEmail.Username != "" && tryEmail.Username != args.Username {
			return fmt.Errorf("邮箱已存在")
		}
	}
	if args.Phone != "" {
		tryPhone := new(model.User)
		if err = orm.Where("phone=?", args.Phone).Limit(1).Find(tryPhone).Error; err != nil {
			logger.Errorln(err)
			return common.ErrInternalServerError
		}
		if tryPhone.Username != "" && tryPhone.Username != args.Username {
			return fmt.Errorf("手机号码已存在")
		}
	}

	logoutUsername := ""
	updateFields := make([]string, 0)
	if args.Password != "" {
		item.Password = args.Password
		updateFields = append(updateFields, "Password")
	}
	if !item.IsAdmin {
		if args.RealName != item.RealName {
			item.RealName = args.RealName
			updateFields = append(updateFields, "RealName")
		}
		if args.IdNumber != item.IdNumber {
			item.IdNumber = args.IdNumber
			updateFields = append(updateFields, "IdNumber")
		}
		if args.Email != item.Email.String {
			item.Email = sql.NullString{
				String: args.Email,
				Valid:  args.Email != "",
			}
			updateFields = append(updateFields, "Email")
		}
		if args.Phone != item.Phone.String {
			item.Phone = sql.NullString{
				String: args.Phone,
				Valid:  args.Phone != "",
			}
			updateFields = append(updateFields, "Phone")
		}
		if args.Enabled != nil {
			if *args.Enabled {
				item.Status = model.UserStatusNormal
			} else {
				item.Status = model.UserStatusDisabled
				logoutUsername = item.Username
			}
			updateFields = append(updateFields, "Status")
		}
	}

	if len(updateFields) > 0 {
		err = orm.Transaction(func(tx *gorm.DB) error {
			if err = tx.Model(item).Select(updateFields).Updates(item).Error; err != nil {
				logger.Errorln(err)
				return common.ErrInternalServerError
			}
			if logoutUsername != "" {
				if err = authutil.LogoutByUid(ctx, logoutUsername); err != nil {
					logger.Errorln(err)
					return common.ErrInternalServerError
				}
			}
			return nil
		})
		if err != nil {
			return err
		}
	}

	return nil
}

// ResetPwd
// @Router /reset-pwd [post]
func (u *User) ResetPwd(ctx *gin.Context, args *request.UserResetPassword) error {
	var err error
	if args == nil {
		args = &request.UserResetPassword{}
	}
	logger, err := core.GetLogger()
	if err != nil {
		log.Println(err)
		return common.ErrInternalServerError
	}
	orm, err := core.GetDatabase()
	if err != nil {
		logger.Errorln(err)
		return common.ErrInternalServerError
	}
	orm = orm.WithContext(ctx)

	if args.Username = strings.TrimSpace(args.Username); len(args.Username) == 0 {
		return fmt.Errorf("账号不能为空")
	}
	if args.Password = strings.TrimSpace(args.Password); len(args.Password) == 0 {
		return fmt.Errorf("密码不能为空")
	}

	pool, err := core.GetRedisPool()
	if err != nil {
		logger.Errorln(err)
		return common.ErrInternalServerError
	}
	mutex := pool.NewMutex("USER:UPDATE:" + args.Username)
	if err := mutex.Lock(); err != nil {
		logger.Errorln(err)
		return common.ErrInternalServerBusy
	}
	defer mutex.Unlock()

	item := new(model.User)
	if err = orm.Where("username=?", args.Username).Limit(1).Find(item).Error; err != nil {
		logger.Errorln(err)
		return common.ErrInternalServerError
	}
	if item.Username == "" {
		return fmt.Errorf("账号不存在")
	}
	args.Password, err = cryptoutil.HashPasswordFromString(args.Password)
	if err != nil {
		logger.Errorln(err)
		return common.ErrInternalServerError
	}
	if err = orm.Transaction(func(tx *gorm.DB) error {
		if err = tx.Model(item).Update("password", args.Password).Error; err != nil {
			logger.Errorln(err)
			return common.ErrInternalServerError
		}
		return nil
	}); err != nil {
		return err
	}

	return nil
}

// ToggleStatus
// @Router /toggle-status [post]
func (u *User) ToggleStatus(ctx *gin.Context, args *request.UserStatusToggle) error {
	var err error
	if args == nil {
		args = &request.UserStatusToggle{}
	}
	logger, err := core.GetLogger()
	if err != nil {
		log.Println(err)
		return common.ErrInternalServerError
	}
	orm, err := core.GetDatabase()
	if err != nil {
		logger.Errorln(err)
		return common.ErrInternalServerError
	}
	orm = orm.WithContext(ctx)

	if args.Username = strings.TrimSpace(args.Username); len(args.Username) == 0 {
		return fmt.Errorf("账号不能为空")
	}
	if _, ok := model.UserStatusText[args.Status]; !ok {
		return fmt.Errorf("状态不存在")
	} else if args.Status == model.UserStatusUnspecified {
		return fmt.Errorf("状态非法")
	}

	pool, err := core.GetRedisPool()
	if err != nil {
		logger.Errorln(err)
		return common.ErrInternalServerError
	}
	mutex := pool.NewMutex("USER:UPDATE:" + args.Username)
	if err := mutex.Lock(); err != nil {
		logger.Errorln(err)
		return common.ErrInternalServerBusy
	}
	defer mutex.Unlock()

	item := new(model.User)
	if err = orm.Where("username=?", args.Username).Limit(1).Find(item).Error; err != nil {
		logger.Errorln(err)
		return common.ErrInternalServerError
	}
	if item.Username == "" {
		return fmt.Errorf("账号不存在")
	}
	if item.IsAdmin {
		return fmt.Errorf("禁止修改超级管理员状态")
	}
	if err = orm.Transaction(func(tx *gorm.DB) error {
		if err = tx.Model(item).Update("status", args.Status).Error; err != nil {
			logger.Errorln(err)
			return common.ErrInternalServerError
		}
		if args.Status == model.UserStatusDisabled {
			if err = authutil.LogoutByUid(ctx, item.Username); err != nil {
				logger.Errorln(err)
				return common.ErrInternalServerError
			}
		}
		return nil
	}); err != nil {
		return err
	}

	return nil
}

// Delete
// @Router /delete [post]
func (u *User) Delete(ctx *gin.Context, args *request.UserDelete) error {
	var err error
	if args == nil {
		args = &request.UserDelete{}
	}
	logger, err := core.GetLogger()
	if err != nil {
		log.Println(err)
		return common.ErrInternalServerError
	}
	orm, err := core.GetDatabase()
	if err != nil {
		logger.Errorln(err)
		return common.ErrInternalServerError
	}
	orm = orm.WithContext(ctx)

	if args.Username = strings.TrimSpace(args.Username); len(args.Username) == 0 {
		return fmt.Errorf("账号不能为空")
	}

	pool, err := core.GetRedisPool()
	if err != nil {
		logger.Errorln(err)
		return common.ErrInternalServerError
	}
	mutex := pool.NewMutex("USER:UPDATE:" + args.Username)
	if err := mutex.Lock(); err != nil {
		logger.Errorln(err)
		return common.ErrInternalServerBusy
	}
	defer mutex.Unlock()

	item := new(model.User)
	if err = orm.Where("username=?", args.Username).Limit(1).Find(item).Error; err != nil {
		logger.Errorln(err)
		return common.ErrInternalServerError
	}
	if item.Username == "" {
		return fmt.Errorf("账号不存在")
	}
	if item.IsAdmin {
		return fmt.Errorf("禁止删除")
	}
	if err = orm.Transaction(func(tx *gorm.DB) error {
		if err = tx.Delete(item).Error; err != nil {
			logger.Errorln(err)
			return common.ErrInternalServerError
		}
		if err = authutil.LogoutByUid(ctx, item.Username); err != nil {
			logger.Errorln(err)
			return common.ErrInternalServerError
		}
		return nil
	}); err != nil {
		return err
	}
	return nil
}
