package rev

import (
	"code.ixdev.cn/cnix/cbdv/hk-box-be/utils/fiscoutil"
	"github.com/gin-gonic/gin"
)

// MockSenderProvider
// @Router /api/v1/rev/mock/sender-provider []
type MockSenderProvider struct {
}

// Get
// @Router /get [post]
func (m *MockSenderProvider) Get(ctx *gin.Context, req fiscoutil.HaSenderRequest) ([]*fiscoutil.HaSenderResponseData, error) {
	rst := make([]*fiscoutil.HaSenderResponseData, 0)
	rst = append(rst, &fiscoutil.HaSenderResponseData{
		CompanyId:     "123",
		SenderCompany: "公司123",
	}, &fiscoutil.HaSenderResponseData{
		CompanyId:     "CS173578540705070",
		SenderCompany: "公司456",
	}, &fiscoutil.HaSenderResponseData{
		CompanyId:     "789",
		SenderCompany: "公司789",
	})
	return rst, nil
}
