package rev

import (
	"compress/flate"
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"os"
	"path"
	"path/filepath"
	"reflect"
	"strconv"
	"strings"
	"time"

	"code.ixdev.cn/cnix/cbdv/hk-box-be/core"
	utils2 "code.ixdev.cn/cnix/cbdv/hk-box-be/services/filewatch/utils"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/model"
	request "code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/request/rev"
	response "code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/response/rev"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/validator/common"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/utils/authutil"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/utils/fileutil"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/utils/fiscoutil"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/utils/special"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/utils/watermarkutil"
	"code.ixdev.cn/liush/x/message"
	errors2 "code.ixdev.cn/liush/xpb/errors"
	"code.ixdev.cn/liush/xpb/pagination"
	"github.com/gin-gonic/gin"
	"github.com/gookit/goutil/strutil"
	"github.com/yeka/zip"
	"gorm.io/gorm"
)

// File 文件管理
// @Router /api/v1/rev/file []
type File struct {
}

// List 获取文件列表
// @Router /list [post]
func (f *File) List(ctx *gin.Context, args *request.FileListRequest) (any, error) {
	var err error
	if args == nil {
		args = &request.FileListRequest{}
	}

	logger, err := core.GetLogger()
	if err != nil {
		log.Fatalln(err)
		return nil, common.ErrInternalServerError
	}
	orm, err := core.GetDatabase()
	if err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}
	orm = orm.WithContext(ctx)

	conditionSql := make([]string, 0)
	bindings := make([]any, 0)

	if args.Archived {
		conditionSql = append(conditionSql, "(r.archived_at IS NOT NULL AND r.deleted_at IS NOT NULL)")
	} else {
		conditionSql = append(conditionSql, "(r.archived_at IS NULL) AND (r.deleted_at IS NULL OR (r.deleted_at IS NOT NULL AND r.record_reserved=true))")
	}
	if args.Filename = strings.TrimSpace(args.Filename); len(args.Filename) != 0 {
		conditionSql = append(conditionSql, "(r.filename LIKE ? OR JSON_SEARCH(r.zips, 'one', ?, NULL, '$[*].files[*].filename') IS NOT NULL)"+
			" OR JSON_SEARCH(r.dcm_paths, 'one', ?, NULL, '$') IS NOT NULL")
		kwd := "%" + args.Filename + "%"
		bindings = append(bindings, kwd, kwd, kwd)
	}
	if args.FileHash = strings.TrimSpace(args.FileHash); len(args.FileHash) != 0 {
		conditionSql = append(conditionSql, "(JSON_UNQUOTE(JSON_EXTRACT(r.cert, '$.data.file_zip_hash')) LIKE ? OR JSON_UNQUOTE(JSON_EXTRACT(r.cert, '$.data.file_detail.file_hash')) LIKE ? )")
		kwd := "%" + args.FileHash + "%"
		bindings = append(bindings, kwd, kwd)
	}
	if args.Sender = strings.TrimSpace(args.Sender); len(args.Sender) != 0 {
		conditionSql = append(conditionSql, "r.sender_id=?")
		bindings = append(bindings, args.Sender)
	}
	if args.SendCode = strings.TrimSpace(args.SendCode); len(args.SendCode) != 0 {
		conditionSql = append(conditionSql, "r.send_code LIKE ?")
		bindings = append(bindings, "%"+args.SendCode+"%")
	}

	args.SendAtStart = strings.TrimSpace(args.SendAtStart)
	args.SendAtEnd = strings.TrimSpace(args.SendAtEnd)
	if len(args.SendAtStart) != 0 && len(args.SendAtEnd) != 0 {
		sendAtStart, err := time.Parse(time.DateTime, args.SendAtStart)
		if err != nil {
			return nil, common.ErrRequestIllegal
		}
		sendAtEnd, err := time.Parse(time.DateTime, args.SendAtEnd)
		if err != nil {
			return nil, common.ErrRequestIllegal
		}
		conditionSql = append(conditionSql, "(r.timestamp>=? and r.timestamp<?)")
		bindings = append(bindings, sendAtStart.Unix(), sendAtEnd.Unix())
	}

	orderSql := make([]string, 0)
	if args.Sorter = strings.TrimSpace(args.Sorter); len(args.Sorter) != 0 { // 排序参数不多，这里就先硬编码即可。后续可以考虑封装个函数
		sorter := strings.SplitN(args.Sorter, ":", 2)
		if len(sorter) != 2 {
			return nil, fmt.Errorf("排序参数格式错误")
		}

		sortField, sortOrder := "", "DESC"
		if sorter[1] == "ascend" {
			sortOrder = "ASC"
		} else if sorter[1] == "descend" {
			sortOrder = "DESC"
		} else {
			return nil, fmt.Errorf("排序参数格式错误")
		}
		if sorter[0] == "send_at" {
			sortField = "r.timestamp"
		} else if sorter[0] == "filesize" {
			sortField = "r.filesize"
		} else if sorter[0] == "archived_at" && args.Archived {
			sortField = "r.archived_at"
		} else {
			return nil, fmt.Errorf("排序参数格式错误")
		}
		orderSql = append(orderSql, fmt.Sprintf("%s %s", sortField, sortOrder))
	}

	if args.Archived && len(orderSql) == 0 {
		orderSql = append(orderSql, "r.archived_at DESC")
	}
	orderSql = append(orderSql, "r.id DESC")

	partWhere := " WHERE " + strings.Join(conditionSql, " AND ")
	partOrder := " ORDER BY " + strings.Join(orderSql, ",")
	partPage := fmt.Sprintf(" LIMIT %d,%d", pagination.ParseOffset(args.Pagination), pagination.ParseLimit(args.Pagination))

	total, data := int64(0), make([]*response.FileListResponseDatum, 0)
	countSql := `SELECT COUNT(*) FROM rev_packages r` + partWhere
	if err = orm.Raw(countSql, bindings...).Scan(&total).Error; err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}
	if total > 0 {
		dataSql := `SELECT * FROM rev_packages r` + partWhere + partOrder + partPage
		iter, err := orm.Raw(dataSql, bindings...).Rows()
		if err != nil {
			logger.Errorln(err)
			return nil, common.ErrInternalServerError
		}
		defer iter.Close()
		for iter.Next() {
			doc := new(model.RevPackage)
			if err = orm.ScanRows(iter, doc); err != nil {
				logger.Errorln(err)
				return nil, common.ErrInternalServerError
			}
			filename := doc.Filename[:len(doc.Filename)-len(".tar.gz")]
			trimSuffix := strings.Split(doc.SendCode, "-")[0]
			filename = strings.TrimSuffix(filename, "_"+trimSuffix)
			datum := &response.FileListResponseDatum{
				Id:               doc.Id,
				SendCode:         doc.SendCode,
				SenderId:         doc.SenderId,
				SenderName:       doc.Cert.Data.DataSenderInfo,
				SendAt:           time.Unix(doc.Timestamp, 0).Format(time.DateTime),
				Filename:         filename,
				Filesize:         doc.Filesize,
				ZipFileCount:     int64(len(doc.Cert.Data.FileDetail)),
				ZipFileHash:      doc.Cert.Data.FileZipHash,
				TarHashVerified:  doc.TarHashVerified,
				ZipHashVerified:  doc.ZipHashVerified,
				DownloadDisabled: doc.RecordReserved,
			}
			if doc.ArchivedAt.Valid {
				datum.ArchivedAt = doc.ArchivedAt.Time.Format(time.DateTime)
			}
			data = append(data, datum)
		}
	}

	return &response.FileListResponse{
		Total: total,
		Data:  data,
	}, nil
}

// ListBak 获取文件列表
//func (f *File) ListBak(ctx *gin.Context, args *request.FileListRequest) (any, error) {
//	var err error
//	logger, err := core.GetLogger()
//	if err != nil {
//		log.Fatalln(err)
//		return nil, common.ErrInternalServerError
//	}
//	orm, err := core.GetDatabase()
//	if err != nil {
//		logger.Errorln(err)
//		return nil, common.ErrInternalServerError
//	}
//	orm = orm.WithContext(ctx)
//	if args == nil {
//		args = &request.FileListRequest{}
//	}
//
//	searchQuery := &types.Query{
//		//MatchAll: &types.MatchAllQuery{},
//	}
//
//	queryList := make([]types.Query, 0)
//
//	//queryList = append(queryList, types.Query{
//	//	Bool: &types.BoolQuery{
//	//		Must: []types.Query{
//	//			{
//	//				Term: map[string]types.TermQuery{
//	//					"tar_hash_verified": {
//	//						Value: true,
//	//					},
//	//				},
//	//			},
//	//			{
//	//				Term: map[string]types.TermQuery{
//	//					"zip_hash_verified": {
//	//						Value: true,
//	//					},
//	//				},
//	//			},
//	//		},
//	//	},
//	//})
//
//	if args.Archived {
//		queryList = append(queryList, types.Query{
//			Bool: &types.BoolQuery{
//				Must: []types.Query{
//					{
//						Term: map[string]types.TermQuery{
//							"archived_at.Valid": {
//								Value: args.Archived,
//							},
//						},
//					},
//					{
//						Exists: &types.ExistsQuery{
//							Field: "deleted_at",
//						},
//					},
//				},
//			},
//		})
//	} else {
//		queryList = append(queryList, types.Query{
//			Bool: &types.BoolQuery{
//				Must: []types.Query{
//					{
//						Bool: &types.BoolQuery{
//							Should: []types.Query{
//								{
//									Term: map[string]types.TermQuery{
//										"archived_at.Valid": {
//											Value: args.Archived,
//										},
//									},
//								},
//								{
//									Bool: &types.BoolQuery{
//										MustNot: []types.Query{
//											{
//												Exists: &types.ExistsQuery{
//													Field: "archived_at",
//												},
//											},
//										},
//									},
//								},
//							},
//						},
//					},
//					{
//						Bool: &types.BoolQuery{
//							Should: []types.Query{
//								{
//									Bool: &types.BoolQuery{
//										Must: []types.Query{
//											{
//												Exists: &types.ExistsQuery{
//													Field: "deleted_at",
//												},
//											},
//											{
//												Term: map[string]types.TermQuery{
//													"record_reserved": {
//														Value: true,
//													},
//												},
//											},
//										},
//									},
//								},
//								{
//									Bool: &types.BoolQuery{
//										MustNot: []types.Query{
//											{
//												Exists: &types.ExistsQuery{
//													Field: "deleted_at",
//												},
//											},
//										},
//									},
//								},
//							},
//						},
//					},
//				},
//			},
//		})
//	}
//	if args.Filename = strings.TrimSpace(args.Filename); len(args.Filename) != 0 {
//		queryList = append(queryList, types.Query{
//			Bool: &types.BoolQuery{
//				Should: []types.Query{
//					{
//						Wildcard: map[string]types.WildcardQuery{
//							"filename": {
//								Value: pointer2.String(fmt.Sprintf("*%s*", args.Filename)),
//							},
//						},
//					},
//					{
//						Wildcard: map[string]types.WildcardQuery{
//							"zips.files.filename.keyword": {
//								Value: pointer2.String(fmt.Sprintf("*%s*", args.Filename)),
//							},
//						},
//					},
//					{
//						Wildcard: map[string]types.WildcardQuery{
//							"dcm_paths.keyword": {
//								Value: pointer2.String(fmt.Sprintf("*%s*", args.Filename)),
//							},
//						},
//					},
//				},
//			},
//		})
//	}
//	if args.FileHash = strings.TrimSpace(args.FileHash); len(args.FileHash) != 0 {
//		queryList = append(queryList, types.Query{
//			Bool: &types.BoolQuery{
//				Should: []types.Query{
//					{
//						Wildcard: map[string]types.WildcardQuery{
//							"cert.data.file_zip_hash": {
//								Value: pointer2.String(fmt.Sprintf("*%s*", args.FileHash)),
//							},
//						},
//					},
//					{
//						Wildcard: map[string]types.WildcardQuery{
//							"cert.data.file_detail.file_hash": {
//								Value: pointer2.String(fmt.Sprintf("*%s*", args.FileHash)),
//							},
//						},
//					},
//				},
//			},
//		})
//	}
//	if args.Sender = strings.TrimSpace(args.Sender); len(args.Sender) != 0 {
//		// 从恒安获取 sender_id
//		//senderIdList, err := fiscoutil.WildcardGetSenderIdListBySenderName(args.Sender)
//		//if err != nil {
//		//	logger.Errorln(err)
//		//	return nil, common.ErrInternalServerError
//		//}
//		//if len(senderIdList) == 0 {
//		//	return nil, nil
//		//}
//		queryList = append(queryList, types.Query{
//			//Terms: &types.TermsQuery{
//			//	TermsQuery: map[string]types.TermsQueryField{
//			//		"sender_id": senderIdList,
//			//	},
//			//},
//			Term: map[string]types.TermQuery{
//				"sender_id": {
//					Value: args.Sender,
//				},
//			},
//		})
//	}
//	if args.SendCode = strings.TrimSpace(args.SendCode); len(args.SendCode) != 0 {
//		queryList = append(queryList, types.Query{
//			Wildcard: map[string]types.WildcardQuery{
//				"send_code": {
//					Value: pointer2.String(fmt.Sprintf("*%s*", args.SendCode)),
//				},
//			},
//		})
//	}
//
//	args.SendAtStart = strings.TrimSpace(args.SendAtStart)
//	args.SendAtEnd = strings.TrimSpace(args.SendAtEnd)
//	if len(args.SendAtStart) != 0 && len(args.SendAtEnd) != 0 {
//		sendAtStart, err := time.Parse(time.DateTime, args.SendAtStart)
//		if err != nil {
//			return nil, common.ErrRequestIllegal
//		}
//		sendAtEnd, err := time.Parse(time.DateTime, args.SendAtEnd)
//		if err != nil {
//			return nil, common.ErrRequestIllegal
//		}
//		sendAtStartV := types.Float64(sendAtStart.Unix())
//		sendAtEndV := types.Float64(sendAtEnd.Unix())
//		queryList = append(queryList, types.Query{
//			Range: map[string]types.RangeQuery{
//				"timestamp": types.NumberRangeQuery{
//					Gte:        &sendAtStartV,
//					Lt:         &sendAtEndV,
//					Lte:        nil,
//					QueryName_: nil,
//					Relation:   nil,
//					To:         nil,
//				},
//			},
//		})
//	}
//
//	if len(queryList) == 0 {
//		searchQuery.MatchAll = &types.MatchAllQuery{}
//	} else {
//		searchQuery.Bool = &types.BoolQuery{
//			Must: queryList,
//		}
//	}
//
//	sorts := make([]types.SortCombinations, 0)
//	if args.Sorter = strings.TrimSpace(args.Sorter); len(args.Sorter) != 0 { // 排序参数不多，这里就先硬编码即可。后续可以考虑封装个函数
//		sorter := strings.SplitN(args.Sorter, ":", 2)
//		if len(sorter) != 2 {
//			return nil, fmt.Errorf("排序参数格式错误")
//		}
//
//		sortField, sortOrder := "", sortorder.Desc
//		if sorter[1] == "ascend" {
//			sortOrder = sortorder.Asc
//		} else if sorter[1] == "descend" {
//			sortOrder = sortorder.Desc
//		} else {
//			return nil, fmt.Errorf("排序参数格式错误")
//		}
//		if sorter[0] == "send_at" {
//			sortField = "timestamp"
//		} else if sorter[0] == "filesize" {
//			sortField = "filesize"
//		} else if sorter[0] == "archived_at" && args.Archived {
//			sortField = "archived_at.Time"
//		} else {
//			return nil, fmt.Errorf("排序参数格式错误")
//		}
//		sortOpt := types.SortOptions{
//			SortOptions: map[string]types.FieldSort{
//				sortField: {
//					Order: &sortOrder,
//				},
//			},
//		}
//		sorts = append(sorts, sortOpt)
//	}
//	if args.Archived && len(sorts) == 0 {
//		sorts = append(sorts, types.SortOptions{
//			SortOptions: map[string]types.FieldSort{
//				"archived_at.Time": {
//					Order: &sortorder.Desc,
//				},
//			},
//		})
//	}
//
//	es, err := core.GetElasticsearch()
//	if err != nil {
//		return nil, err
//	}
//	searchResult, err := es.Search().
//		Index((&model.RevPackage{}).EsIndexName()).
//		Request(&search.Request{
//			Query: searchQuery,
//			Sort: append(sorts, types.SortOptions{
//				SortOptions: map[string]types.FieldSort{
//					"id.keyword": {
//						Order: &sortorder.Desc,
//					},
//				},
//			}),
//			From: pointer2.Int(pagination.ParseOffset(args.Pagination)),
//			Size: pointer2.Int(pagination.ParseLimit(args.Pagination)),
//		}).
//		Do(ctx)
//	if err != nil {
//		var terr *types.ElasticsearchError
//		if errors.As(err, &terr) {
//			if terr.Status == http.StatusNotFound && terr.ErrorCause.Type == "index_not_found_exception" { // 索引不存在
//				return nil, nil
//			}
//		}
//		logger.Errorln(err)
//		return nil, common.ErrInternalServerError
//	}
//
//	total, data := int64(0), make([]*response.FileListResponseDatum, 0)
//	if searchResult.Hits.Total != nil {
//		total = (*searchResult.Hits.Total).Value
//	}
//	if total > 0 && searchResult.Hits.Hits != nil {
//		//senderNameMap := sync.Map{}
//		//
//		////// 从数据库获取sender_id => sender_name，考虑到领导会随时要求将发送方修改为可管理的列表，若从数据库直接获取，将会存在某条记录被删除而无法获取到，则列表发送方则会显示为空
//		//senderIdMap, senderIdList := make(map[string]struct{}), make([]string, 0)
//		//for _, hit := range searchResult.Hits.Hits {
//		//	doc := new(model.RevPackage)
//		//	if err := json.Unmarshal(hit.Source_, doc); err != nil {
//		//		logger.Errorln(err)
//		//		continue
//		//	}
//		//	senderIdMap[doc.SenderId] = struct{}{}
//		//}
//		//for senderId := range senderIdMap {
//		//	senderIdList = append(senderIdList, senderId)
//		//}
//		//if len(senderIdList) > 0 {
//		//	iter, err := orm.Model(new(model.RevSender)).Where("sender_id in ?", senderIdList).Rows()
//		//	if err != nil {
//		//		logger.Errorln(err)
//		//		return nil, common.ErrInternalServerError
//		//	}
//		//	defer iter.Close()
//		//	for iter.Next() {
//		//		n := new(model.RevSender)
//		//		if err = orm.ScanRows(iter, n); err != nil {
//		//			logger.Errorln(err)
//		//			return nil, common.ErrInternalServerError
//		//		}
//		//		senderNameMap.Store(n.SenderId, n.SenderName)
//		//	}
//		//}
//
//		// 从恒安并发获取 sender_id => sender_name
//		//wg, _ := errgroup.WithContext(ctx)
//		//for _, hit := range searchResult.Hits.Hits {
//		//	doc := new(model.RevPackage)
//		//	if err := json.Unmarshal(hit.Source_, doc); err != nil {
//		//		logger.Errorln(err)
//		//		continue
//		//	}
//		//	wg.Go(func() error {
//		//		// 从恒安获取 sender_id
//		//		senderMapFromHA, err := fiscoutil.WildcardGetSender("", doc.SenderId)
//		//		if err != nil {
//		//			logger.Errorln(err)
//		//			return common.ErrInternalServerError
//		//		}
//		//		if senderMapFromHA != nil {
//		//			for s, responseData := range senderMapFromHA {
//		//				senderNameMap.Store(s, responseData.SenderCompany)
//		//			}
//		//		}
//		//		return nil
//		//	})
//		//}
//		//if err = wg.Wait(); err != nil {
//		//	return nil, err
//		//}
//
//		for _, hit := range searchResult.Hits.Hits {
//			doc := new(model.RevPackage)
//			if err := json.Unmarshal(hit.Source_, doc); err != nil {
//				logger.Errorln(err)
//				continue
//			}
//			//senderName := ""
//			//if senderNameV, ok := senderNameMap.Load(doc.SenderId); ok {
//			//	senderName = senderNameV.(string)
//			//}
//			filename := doc.Filename[:len(doc.Filename)-len(".tar.gz")]
//			trimSuffix := strings.Split(doc.SendCode, "-")[0]
//			filename = strings.TrimSuffix(filename, "_"+trimSuffix)
//			datum := &response.FileListResponseDatum{
//				Id:               doc.Id,
//				SendCode:         doc.SendCode,
//				SenderId:         doc.SenderId,
//				SenderName:       doc.Cert.Data.DataSenderInfo,
//				SendAt:           time.Unix(doc.Timestamp, 0).Format(time.DateTime),
//				Filename:         filename,
//				Filesize:         doc.Filesize,
//				ZipFileCount:     int64(len(doc.Cert.Data.FileDetail)),
//				ZipFileHash:      doc.Cert.Data.FileZipHash,
//				TarHashVerified:  doc.TarHashVerified,
//				ZipHashVerified:  doc.ZipHashVerified,
//				DownloadDisabled: doc.RecordReserved,
//			}
//			if doc.ArchivedAt.Valid {
//				datum.ArchivedAt = doc.ArchivedAt.Time.Format(time.DateTime)
//			}
//			data = append(data, datum)
//		}
//	}
//
//	return &response.FileListResponse{
//		Total: total,
//		Data:  data,
//	}, nil
//}

func (f *File) getZipChildFileId(zipFile *model.RevPackageZip, deepFile *model.RevPackageZipFile) string {
	fileId := zipFile.Filepath + "|" + zipFile.Filename[:len(zipFile.Filename)-len(filepath.Ext(zipFile.Filename))]
	fileId += "|" + deepFile.Filepath + "|" + deepFile.Filename
	return base64.StdEncoding.EncodeToString([]byte(fileId))
}

// ChildFiles 获取子文件列表
// @Router /child-files [post]
func (f *File) ChildFiles(ctx *gin.Context, args *request.FileChildFilesRequest) (any, error) {
	var err error
	if args == nil {
		args = &request.FileChildFilesRequest{}
	}
	logger, err := core.GetLogger()
	if err != nil {
		log.Fatalln(err)
		return nil, common.ErrInternalServerError
	}
	orm, err := core.GetDatabase()
	if err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}
	if args.PackageId = strings.TrimSpace(args.PackageId); len(args.PackageId) == 0 {
		return nil, fmt.Errorf("包文件id不能为空")
	}
	username := authutil.GetUsername(ctx)
	doc := new(model.RevPackage)

	if err = orm.Unscoped().Where("id=?", args.PackageId).Limit(1).Find(doc).Error; err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}
	if doc.Id == "" {
		return nil, fmt.Errorf("包不存在")
	}

	result := make([]*response.FileChildFilesResponseDatum, 0)

	certDataMap := make(map[string]*model.RevPackageCertDataFileDetail)
	for _, datum := range doc.Cert.Data.FileDetail {
		certDataMap[datum.FileName] = datum
	}

	fileIdList := make([]string, 0)
	for _, zipFile := range doc.Zips {
		if len(zipFile.Files) == 0 {
			continue
		}
		for _, deepFile := range zipFile.Files {
			fileId := f.getZipChildFileId(zipFile, deepFile)
			fileIdList = append(fileIdList, fileId)
		}
	}
	if doc.DcmPaths != nil {
		for _, dcmPath := range doc.DcmPaths {
			fileIdList = append(fileIdList, base64.StdEncoding.EncodeToString([]byte(dcmPath)))
		}
	}
	if doc.SftpSend != nil {
		for _, sftpSendFile := range doc.SftpSend {
			fileIdList = append(fileIdList, base64.StdEncoding.EncodeToString([]byte(sftpSendFile.FileRelPath)))
		}
	}
	iter, err := orm.Raw(`SELECT
  rpdr2.id,
  rpdr2.file_id,
  rpdr2.download_at,
  rpdr2.download_ip,
  rpdr2.download_result,
  rpdr2.download_consistency_compare_result,
  rpdr2.wm_enabled,
  rpdr2.wm_data
FROM
  rev_package_download_records rpdr2
  JOIN (
    SELECT
      rpdr.file_id,
      MAX(rpdr.id) AS record_id 
    FROM
      rev_package_download_records rpdr 
    WHERE
      rpdr.file_id IN ? and rpdr.package_id = ? and rpdr.username=?
    GROUP BY
  rpdr.file_id) rpdr1 ON rpdr2.id = rpdr1.record_id 
WHERE
  rpdr2.file_id IN ? and rpdr2.package_id = ? and rpdr2.username=?`, fileIdList, args.PackageId, username, fileIdList, args.PackageId, username).Rows()
	if err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}
	defer iter.Close()

	latestDownloadRecordMap := make(map[string]*model.RevPackageDownloadRecord)
	for iter.Next() {
		n := new(model.RevPackageDownloadRecord)
		if err = orm.ScanRows(iter, n); err != nil {
			logger.Errorln(err)
			return nil, common.ErrInternalServerError
		}
		latestDownloadRecordMap[n.FileId] = n
	}

	for _, zipFile := range doc.Zips {
		if len(zipFile.Files) == 0 {
			continue
		}

		for _, deepFile := range zipFile.Files {
			fileId := f.getZipChildFileId(zipFile, deepFile)
			datum := &response.FileChildFilesResponseDatum{
				Id:                fileId,
				Filename:          deepFile.Filename,
				Filesize:          deepFile.Filesize,
				IsEncrypted:       deepFile.IsEncrypted,
				FileHash:          "",
				FileWatermarkHash: "",
			}
			if fadInfo, ok := certDataMap[deepFile.Filename]; ok {
				datum.FileHash = fadInfo.FileHash
				datum.FileWatermarkHash = fadInfo.FileWatermarkHash
			}
			if downloadRecord, ok := latestDownloadRecordMap[fileId]; ok {
				datum.LatestDownloadAt = downloadRecord.DownloadAt.Format(time.DateTime)
				datum.LatestDownloadIp = downloadRecord.DownloadIp
				datum.LatestDownloadResult = downloadRecord.DownloadResult
				datum.LatestDownloadConsistencyCompareResult = downloadRecord.DownloadConsistencyCompareResult
				datum.LatestDownloadWmEnabled = downloadRecord.WmEnabled
				datum.LatestDownloadWmData = downloadRecord.WmData
			}
			if doc.RecordReserved {
				datum.DownloadDisabled = true
			}

			result = append(result, datum)
		}
	}
	if doc.DcmPaths != nil {
		for _, dcmPath := range doc.DcmPaths {
			fileBasename := filepath.Base(dcmPath)
			fileId := base64.StdEncoding.EncodeToString([]byte(dcmPath))
			datum := &response.FileChildFilesResponseDatum{
				Id:                                     fileId,
				Filename:                               fileBasename,
				Filesize:                               0,
				IsEncrypted:                            false,
				FileHash:                               "",
				FileWatermarkHash:                      "",
				LatestDownloadAt:                       "",
				LatestDownloadResult:                   0,
				LatestDownloadIp:                       "",
				LatestDownloadConsistencyCompareResult: 0,
				LatestDownloadWmEnabled:                false,
				LatestDownloadWmData:                   nil,
			}
			if fadInfo, ok := certDataMap[fileBasename]; ok {
				datum.FileHash = fadInfo.FileHash
				datum.FileWatermarkHash = fadInfo.FileWatermarkHash
				datum.Filesize = fadInfo.FileSize
			}
			if downloadRecord, ok := latestDownloadRecordMap[fileId]; ok {
				datum.LatestDownloadAt = downloadRecord.DownloadAt.Format(time.DateTime)
				datum.LatestDownloadIp = downloadRecord.DownloadIp
				datum.LatestDownloadResult = downloadRecord.DownloadResult
				datum.LatestDownloadConsistencyCompareResult = downloadRecord.DownloadConsistencyCompareResult
				datum.LatestDownloadWmEnabled = downloadRecord.WmEnabled
				datum.LatestDownloadWmData = downloadRecord.WmData
			}
			if doc.RecordReserved {
				datum.DownloadDisabled = true
			}
			result = append(result, datum)
		}
	}
	if doc.SftpSend != nil {
		for _, sftpSend := range doc.SftpSend {
			//fileBasename := filepath.Base(sftpSend)
			fileId := base64.StdEncoding.EncodeToString([]byte(sftpSend.FileRelPath))
			datum := &response.FileChildFilesResponseDatum{
				Id:                   fileId,
				Filename:             sftpSend.FileBasename,
				Filesize:             sftpSend.Filesize,
				IsEncrypted:          false,
				FileHash:             "",
				FileWatermarkHash:    "",
				LatestDownloadAt:     "",
				LatestDownloadResult: 0,
			}
			if downloadRecord, ok := latestDownloadRecordMap[fileId]; ok {
				datum.LatestDownloadAt = downloadRecord.DownloadAt.Format(time.DateTime)
				datum.LatestDownloadIp = downloadRecord.DownloadIp
				datum.LatestDownloadResult = downloadRecord.DownloadResult
				datum.LatestDownloadConsistencyCompareResult = downloadRecord.DownloadConsistencyCompareResult
				datum.LatestDownloadWmEnabled = downloadRecord.WmEnabled
				datum.LatestDownloadWmData = downloadRecord.WmData
			}
			if doc.RecordReserved {
				datum.DownloadDisabled = true
			}
			result = append(result, datum)
		}
	}

	return result, nil
}

type reportDcmDownloadRecordPass struct {
	ChildPath                        string                                 `json:"child_path"`
	ChildFilepath                    string                                 `json:"child_filepath"`
	ChildFilename                    string                                 `json:"child_filename"`
	DownloadResult                   model.RevPackageDownloadResult         `json:"download_result"`
	DownloadDetail                   any                                    `json:"download_detail"`
	DownloadConsistencyCompareResult model.DownloadConsistencyCompareResult `json:"download_consistency_compare_result"`
	DownloadConsistencyCompareDetail any                                    `json:"download_consistency_compare_detail"`
	WmEnabled                        bool                                   `json:"wm_enabled"`
	WmData                           map[string]any                         `json:"wm_data"`
}

func (f *File) reportDcmDownloadRecord(
	ctx *gin.Context,
	pkgM *model.RevPackage,
	pass reportDcmDownloadRecordPass,
) {
	var err error
	logger, err := core.GetLogger()
	if err != nil {
		log.Fatalln(err)
		return
	}
	orm, err := core.GetDatabase()
	if err != nil {
		logger.Errorln(err)
		return
	}
	record := &model.RevPackageDownloadRecord{
		Id:                               strconv.FormatInt(core.GetNextId(), 10),
		FileId:                           base64.StdEncoding.EncodeToString([]byte(pass.ChildPath)),
		PackageId:                        pkgM.Id,
		PackageFilepath:                  pkgM.Filepath,
		PackageFilename:                  pkgM.Filename,
		ChildFilepath:                    pass.ChildFilepath,
		ChildFilename:                    pass.ChildFilename,
		Username:                         authutil.GetUsername(ctx),
		DownloadIp:                       ctx.ClientIP(),
		DownloadUseragent:                ctx.Request.UserAgent(),
		DownloadResult:                   pass.DownloadResult,
		DownloadDetail:                   pass.DownloadDetail,
		DownloadConsistencyCompareResult: pass.DownloadConsistencyCompareResult,
		DownloadConsistencyCompareDetail: pass.DownloadConsistencyCompareDetail,
		WmEnabled:                        pass.WmEnabled,
		WmData:                           pass.WmData,
		DownloadAt:                       time.Now(),
	}
	if err = orm.Create(record).Error; err != nil {
		logger.Errorln(err)
		return
	}
}

type reportDownloadRecordPass struct {
	ZipFilepath                      string                                 `json:"zip_filepath"`
	ZipFilename                      string                                 `json:"zip_filename"`
	ChildFilepath                    string                                 `json:"child_filepath"`
	ChildFilename                    string                                 `json:"child_filename"`
	DownloadResult                   model.RevPackageDownloadResult         `json:"download_result"`
	DownloadDetail                   any                                    `json:"download_detail"`
	DownloadConsistencyCompareResult model.DownloadConsistencyCompareResult `json:"download_consistency_compare_result"`
	DownloadConsistencyCompareDetail any                                    `json:"download_consistency_compare_detail"`
	WmEnabled                        bool                                   `json:"wm_enabled"`
	WmData                           map[string]any                         `json:"wm_data"`
}

func (f *File) reportDownloadRecord(
	ctx *gin.Context,
	pkgM *model.RevPackage,
	pass reportDownloadRecordPass,
) {
	var err error
	logger, err := core.GetLogger()
	if err != nil {
		log.Fatalln(err)
		return
	}
	orm, err := core.GetDatabase()
	if err != nil {
		logger.Errorln(err)
		return
	}
	record := &model.RevPackageDownloadRecord{
		Id: strconv.FormatInt(core.GetNextId(), 10),
		FileId: f.getZipChildFileId(&model.RevPackageZip{
			Filename: pass.ZipFilename,
			Filepath: pass.ZipFilepath,
		}, &model.RevPackageZipFile{
			Filename: pass.ChildFilename,
			Filepath: pass.ChildFilepath,
		}),
		PackageId:                        pkgM.Id,
		PackageFilepath:                  pkgM.Filepath,
		PackageFilename:                  pkgM.Filename,
		ZipFilepath:                      pass.ZipFilepath,
		ZipFilename:                      pass.ZipFilename,
		ChildFilepath:                    pass.ChildFilepath,
		ChildFilename:                    pass.ChildFilename,
		Username:                         authutil.GetUsername(ctx),
		DownloadIp:                       ctx.ClientIP(),
		DownloadUseragent:                ctx.Request.UserAgent(),
		DownloadResult:                   pass.DownloadResult,
		DownloadDetail:                   pass.DownloadDetail,
		DownloadConsistencyCompareResult: pass.DownloadConsistencyCompareResult,
		DownloadConsistencyCompareDetail: pass.DownloadConsistencyCompareDetail,
		WmEnabled:                        pass.WmEnabled,
		WmData:                           pass.WmData,
		DownloadAt:                       time.Now(),
	}
	if err = orm.Create(record).Error; err != nil {
		logger.Errorln(err)
		return
	}
}

// PreviewChildFileWatermarkInfo 预览子文件下载水印信息
// @Router /preview-child-file-watermark-info [post]
func (f *File) PreviewChildFileWatermarkInfo(ctx *gin.Context, args *request.PreviewChildFileWatermarkInfoRequest) (any, error) {
	var err error
	if args == nil {
		args = &request.PreviewChildFileWatermarkInfoRequest{}
	}
	logger, err := core.GetLogger()
	if err != nil {
		log.Println(err)
		return nil, common.ErrInternalServerError
	}
	if args.PackageId = strings.TrimSpace(args.PackageId); len(args.PackageId) == 0 {
		return nil, fmt.Errorf("包文件id不能为空")
	}
	if args.FileId = strings.TrimSpace(args.FileId); len(args.FileId) == 0 {
		return nil, fmt.Errorf("子文件id不能为空")
	}
	fileIdB, err := base64.StdEncoding.DecodeString(args.FileId)
	if err != nil {
		logger.Errorln(err)
		return nil, fmt.Errorf("文件id非法")
	}
	fileId := string(fileIdB)

	orm, err := core.GetDatabase()
	if err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}
	packageM := new(model.RevPackage)
	if err = orm.Unscoped().Where("id=?", args.PackageId).Limit(1).Find(packageM).Error; err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}
	if packageM.Id == "" {
		return nil, fmt.Errorf("包%s不存在", args.PackageId)
	}
	//if !packageM.TarHashVerified || !packageM.ZipHashVerified {
	//	return nil, fmt.Errorf("文件已被损坏")
	//}

	adminUser := new(model.User)
	if err = orm.Where("username=?", model.AdminUsername).Where("is_admin", true).Limit(1).Find(adminUser).Error; err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}
	if adminUser.Username == "" {
		return nil, fmt.Errorf("adminUser not found")
	}

	personId := ""

	if !strings.Contains(fileId, "|") {
		targetFileBasename := filepath.Base(fileId)
		if len(packageM.Cert.Data.FileDetail) > 0 {
			for _, fileDetail := range packageM.Cert.Data.FileDetail {
				if fileDetail.FileName == targetFileBasename {
					personId = fileDetail.PersonId
					break
				}
			}
		}
	} else {
		fileIdArr := strings.SplitN(strings.ReplaceAll(fileId, "..", ""), "|", 4)
		if len(fileIdArr) != 4 {
			return nil, fmt.Errorf("文件id非法")
		}
		//zipFileDir, zipFileBasenameNoExt := fileIdArr[0], fileIdArr[1]
		_, targetFileBasename := fileIdArr[2], fileIdArr[3]

		//unzipDirPath := strings.TrimSpace(strings.Trim(zipFileDir+"/"+zipFileBasenameNoExt, "/"))
		//zipFilePath := unzipDirPath + ".zip"
		//targetFilePath := strings.TrimSpace(strings.Trim(zipFileDir+"/"+targetFileDir+"/"+targetFileBasename, "/"))

		if len(packageM.Cert.Data.FileDetail) > 0 {
			for _, fileDetail := range packageM.Cert.Data.FileDetail {
				fmt.Println("fileDetail.FileName:", fileDetail.FileName)
				fmt.Println("targetFileBasename:", targetFileBasename)
				if fileDetail.FileName == targetFileBasename {
					personId = fileDetail.PersonId
					break
				}
			}
		}

	}

	operator := authutil.GetUsername(ctx)

	m := new(model.SysConfig)
	if err = orm.First(m, "id=?", model.SysConfigKey).Error; err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}

	wmData := map[string]any{
		"rev_dl_wm_enabled":    m.RevDlWmEnabled,
		"sender_up_wm_enabled": m.SenderUpWmEnabled,
		"institution":          adminUser.Institution,
		"operator":             operator,
	}
	if personId != "" {
		wmData["person_id"] = personId
	}

	return wmData, nil
}

// ChildFileDownload 下载子文件
// @Router /child-file-download [post]
func (f *File) ChildFileDownload(ctx *gin.Context, args *request.FileChildFileDownloadRequest) {
	var err error
	if args == nil {
		args = &request.FileChildFileDownloadRequest{}
	}
	logger, err := core.GetLogger()
	if err != nil {
		log.Println(err)
		message.Err(ctx, common.ErrInternalServerError)
		return
	}
	if args.PackageId = strings.TrimSpace(args.PackageId); len(args.PackageId) == 0 {
		message.ErrBadRequest(ctx, fmt.Errorf("包文件id不能为空"))
		return
	}
	if args.FileId = strings.TrimSpace(args.FileId); len(args.FileId) == 0 {
		message.ErrBadRequest(ctx, fmt.Errorf("子文件id不能为空"))
		return
	}
	fileIdB, err := base64.StdEncoding.DecodeString(args.FileId)
	if err != nil {
		logger.Errorln(err)
		message.ErrBadRequest(ctx, fmt.Errorf("文件id非法"))
		return
	}
	fileId := string(fileIdB)

	orm, err := core.GetDatabase()
	if err != nil {
		logger.Errorln(err)
		message.Err(ctx, common.ErrInternalServerError)
		return
	}
	packageM := new(model.RevPackage)
	if err = orm.Unscoped().Where("id=?", args.PackageId).Limit(1).Find(packageM).Error; err != nil {
		logger.Errorln(err)
		message.Err(ctx, common.ErrInternalServerError)
		return
	}
	if packageM.Id == "" {
		message.Err(ctx, fmt.Errorf("包%s不存在", args.PackageId))
		return
	}
	//if !packageM.TarHashVerified || !packageM.ZipHashVerified {
	//	err = fmt.Errorf("the file has been damaged")
	//	message.Err(ctx, err)
	//	return
	//}

	if !strings.Contains(fileId, "|") {
		//fileIdArr := strings.SplitN(strings.ReplaceAll(fileId, "..", ""), "|", 4)
		//if len(fileIdArr) != 4 {
		//	message.ErrBadRequest(ctx, fmt.Errorf("文件id非法"))
		//	return
		//}
		//zipFileDir, zipFileBasenameNoExt := fileIdArr[0], fileIdArr[1]
		//targetFileDir, targetFileBasename := fileIdArr[2], fileIdArr[3]
		//
		//unzipDirPath := strings.TrimSpace(strings.Trim(zipFileDir+"/"+zipFileBasenameNoExt, "/"))
		//zipFilePath := unzipDirPath + ".zip"
		//targetFilePath := strings.TrimSpace(strings.Trim(zipFileDir+"/"+targetFileDir+"/"+targetFileBasename, "/"))

		packageDst := packageM.Filepath + "/" + packageM.Filename
		tempDir := utils2.GetUnGzipDir(packageDst) + strutil.RandomCharsV3(8)
		defer func() {
			_ = os.RemoveAll(tempDir)
		}()

		dcmFileDir, dcmFileBasename := filepath.Split(fileId)
		reportPass := reportDcmDownloadRecordPass{
			ChildPath:                        fileId,
			ChildFilepath:                    dcmFileDir,
			ChildFilename:                    dcmFileBasename,
			DownloadResult:                   model.RevPackageDownloadResultFailed,
			DownloadDetail:                   nil,
			DownloadConsistencyCompareResult: model.DownloadConsistencyCompareResultUnspecified,
			DownloadConsistencyCompareDetail: nil,
			WmEnabled:                        false,
			WmData:                           nil,
		}

		if _, err = utils2.TryParseGzip(logger, packageDst, tempDir, false); err != nil {
			logger.Errorln(err)
			message.Err(ctx, common.ErrInternalServerError)
			reportPass.DownloadDetail = map[string]any{"err": err}
			f.reportDcmDownloadRecord(ctx, packageM, reportPass)
			return
		}

		rPath := filepath.Join(tempDir, fileId)
		if _, err = os.Stat(rPath); err != nil {
			if errors.Is(err, os.ErrNotExist) {
				message.ErrNotFound(ctx, fmt.Errorf("file not exist"))
				reportPass.DownloadDetail = map[string]any{"reason": "文件不存在"}
				f.reportDcmDownloadRecord(ctx, packageM, reportPass)
				return
			}
			logger.Errorln(err)
			message.Err(ctx, common.ErrInternalServerError)
			reportPass.DownloadDetail = map[string]any{"err": err}
			f.reportDcmDownloadRecord(ctx, packageM, reportPass)
			return
		}

		fileExt := strings.ToLower(filepath.Ext(rPath))

		if fileExt == ".zip" {
			// --------------- verify hash ---------------
			//ctx.Set("__dataUserId__", packageM.Cert.Data.DataUserId)
			adminUser := new(model.User)
			if err = orm.Where("username=?", model.AdminUsername).Where("is_admin", true).Limit(1).Find(adminUser).Error; err != nil {
				logger.Errorln(err)
				message.Err(ctx, common.ErrInternalServerError)
				reportPass.DownloadDetail = map[string]any{"err": err}
				f.reportDcmDownloadRecord(ctx, packageM, reportPass)
				return
			}
			if adminUser.Username == "" {
				message.Err(ctx, fmt.Errorf("adminUser not found"))
				reportPass.DownloadDetail = map[string]any{"reason": "adminUser not found"}
				f.reportDcmDownloadRecord(ctx, packageM, reportPass)
				return
			}
			//ctx.Set("__institution__", adminUser.Institution)

			certFilepath := filepath.Join(tempDir, packageM.Cert.Filepath, packageM.Cert.Filename)
			logger.Debugf("certFilePath: %s\n", certFilepath)
			certFileBytes, err := os.ReadFile(certFilepath)
			if err != nil {
				logger.Errorln(err)
				message.Err(ctx, common.ErrInternalServerError)
				reportPass.DownloadDetail = map[string]any{"reason": err}
				reportPass.DownloadConsistencyCompareResult = model.DownloadConsistencyCompareResultFailed
				reportPass.DownloadConsistencyCompareDetail = map[string]any{"err": err}
				f.reportDcmDownloadRecord(ctx, packageM, reportPass)
				//f.doDownload(ctx, rPath)
				return
			}
			certFileData := new(model.RevPackageCertData)
			if err = json.Unmarshal(certFileBytes, certFileData); err != nil {
				logger.Errorln(err)
				message.Err(ctx, common.ErrInternalServerError)
				reportPass.DownloadDetail = map[string]any{"reason": err}
				reportPass.DownloadConsistencyCompareResult = model.DownloadConsistencyCompareResultFailed
				reportPass.DownloadConsistencyCompareDetail = map[string]any{"err": err}
				f.reportDcmDownloadRecord(ctx, packageM, reportPass)
				//f.doDownload(ctx, rPath)
				return
			}

			downloadPath := rPath

			watermarkCfg := core.GetConf().App.Rev.Fisco.WatermarkApi

			personId := ""
			if len(packageM.Cert.Data.FileDetail) > 0 {
				for _, fileDetail := range packageM.Cert.Data.FileDetail {
					if fileDetail.FileName == dcmFileBasename {
						personId = fileDetail.PersonId
						break
					}
				}
			}
			operator := authutil.GetUsername(ctx)

			err = special.HandleHL7Zip(packageDst, tempDir, packageM, special.WatermarkData{
				PersonId:    personId,
				Operator:    operator,
				Institution: adminUser.Institution,
			}, filepath.Join(tempDir, fileId), special.GetUnZipDir(fileId), args.WmEnabled && watermarkCfg.Enabled)
			if err != nil {
				logger.Errorln(err)
				message.Err(ctx, common.ErrInternalServerError)
				reportPass.DownloadDetail = map[string]any{"reason": err}
				reportPass.DownloadConsistencyCompareResult = model.DownloadConsistencyCompareResultFailed
				reportPass.DownloadConsistencyCompareDetail = map[string]any{"err": err}
				f.reportDcmDownloadRecord(ctx, packageM, reportPass)
				f.doDownload(ctx, downloadPath)
				return
			}

			// --------------- verify hash ---------------

			reportPass.DownloadResult = model.RevPackageDownloadResultSucceed
			reportPass.DownloadConsistencyCompareResult = model.DownloadConsistencyCompareResultSucceed
			reportPass.WmEnabled = args.WmEnabled
			reportPass.WmData = nil

			f.reportDcmDownloadRecord(ctx, packageM, reportPass)

			f.doDownload(ctx, downloadPath)
		} else {
			// --------------- verify hash ---------------
			//ctx.Set("__dataUserId__", packageM.Cert.Data.DataUserId)
			adminUser := new(model.User)
			if err = orm.Where("username=?", model.AdminUsername).Where("is_admin", true).Limit(1).Find(adminUser).Error; err != nil {
				logger.Errorln(err)
				message.Err(ctx, common.ErrInternalServerError)
				reportPass.DownloadDetail = map[string]any{"err": err}
				f.reportDcmDownloadRecord(ctx, packageM, reportPass)
				return
			}
			if adminUser.Username == "" {
				message.Err(ctx, fmt.Errorf("adminUser not found"))
				reportPass.DownloadDetail = map[string]any{"reason": "adminUser not found"}
				f.reportDcmDownloadRecord(ctx, packageM, reportPass)
				return
			}
			//ctx.Set("__institution__", adminUser.Institution)

			certFilepath := filepath.Join(tempDir, packageM.Cert.Filepath, packageM.Cert.Filename)
			logger.Debugf("certFilePath: %s\n", certFilepath)
			certFileBytes, err := os.ReadFile(certFilepath)
			if err != nil {
				logger.Errorln(err)
				message.Err(ctx, common.ErrInternalServerError)
				reportPass.DownloadDetail = map[string]any{"reason": err}
				reportPass.DownloadConsistencyCompareResult = model.DownloadConsistencyCompareResultFailed
				reportPass.DownloadConsistencyCompareDetail = map[string]any{"err": err}
				f.reportDcmDownloadRecord(ctx, packageM, reportPass)
				//f.doDownload(ctx, rPath)
				return
			}
			certFileData := new(model.RevPackageCertData)
			if err = json.Unmarshal(certFileBytes, certFileData); err != nil {
				logger.Errorln(err)
				message.Err(ctx, common.ErrInternalServerError)
				reportPass.DownloadDetail = map[string]any{"reason": err}
				reportPass.DownloadConsistencyCompareResult = model.DownloadConsistencyCompareResultFailed
				reportPass.DownloadConsistencyCompareDetail = map[string]any{"err": err}
				f.reportDcmDownloadRecord(ctx, packageM, reportPass)
				//f.doDownload(ctx, rPath)
				return
			}

			downloadPath := rPath

			watermarkCfg := core.GetConf().App.Rev.Fisco.WatermarkApi
			watermarkFilepath := ""
			var wmData map[string]any
			if args.WmEnabled && watermarkCfg.Enabled {
				watermarkFilepath = filepath.Join(watermarkCfg.TempDir, filepath.Base(rPath))
				if err := fileutil.CopyFile(rPath, watermarkFilepath, 0775); err != nil {
					logger.Errorln(err)
					message.Err(ctx, common.ErrInternalServerError)
					reportPass.DownloadDetail = map[string]any{"err": err}
					f.reportDcmDownloadRecord(ctx, packageM, reportPass)
					return
				}

				defer os.RemoveAll(filepath.Dir(watermarkFilepath))

				operator := authutil.GetUsername(ctx)

				personId := ""
				if len(packageM.Cert.Data.FileDetail) > 0 {
					for _, fileDetail := range packageM.Cert.Data.FileDetail {
						if fileDetail.FileName == dcmFileBasename {
							personId = fileDetail.PersonId
							break
						}
					}
				}

				_wmData, err := watermarkutil.DoWatermark(watermarkCfg.Addr, watermarkFilepath, personId, operator, adminUser.Institution)
				if err != nil {
					logger.Errorln(err)
					message.Err(ctx, common.ErrInternalServerError)
					reportPass.DownloadDetail = map[string]any{"err": err}
					f.reportDcmDownloadRecord(ctx, packageM, reportPass)
					f.doDownload(ctx, downloadPath)
					return
				}
				downloadPath = watermarkFilepath
				wmData = _wmData
			} else {
				args.WmEnabled = false
			}

			isVerified, err := fiscoutil.NewFiscoUtil().IsDcmFileVerified(packageDst, rPath, certFileData, string(certFileBytes), packageM.SendCode, watermarkFilepath)

			if err != nil {
				logger.Errorln(err)
				verr := errors2.FromError(err)
				if verr.Code != 0 {
					//message.Err(ctx, fmt.Errorf("文件验证失败"))
					reportPass.DownloadResult = model.RevPackageDownloadResultSucceed
					reportPass.DownloadDetail = map[string]any{"reason": "文件验证失败"}
					reportPass.DownloadConsistencyCompareResult = model.DownloadConsistencyCompareResultFailed
					if packageM.TarHashVerified {
						reportPass.DownloadConsistencyCompareResult = model.DownloadConsistencyCompareResultSucceed // 恒安未上链，所以比对一定失败，但这里强制显示为成功
					}
					reportPass.DownloadConsistencyCompareDetail = map[string]any{"err": err}
					f.reportDcmDownloadRecord(ctx, packageM, reportPass)
					f.doDownload(ctx, downloadPath)
					return
				}
				//message.Err(ctx, common.ErrInternalServerError)
				reportPass.DownloadResult = model.RevPackageDownloadResultSucceed
				reportPass.DownloadDetail = map[string]any{"err": err}
				reportPass.DownloadConsistencyCompareResult = model.DownloadConsistencyCompareResultFailed
				if packageM.TarHashVerified {
					reportPass.DownloadConsistencyCompareResult = model.DownloadConsistencyCompareResultSucceed // 恒安未上链，所以比对一定失败，但这里强制显示为成功
				}
				reportPass.DownloadConsistencyCompareDetail = map[string]any{"err": err}
				f.reportDcmDownloadRecord(ctx, packageM, reportPass)
				f.doDownload(ctx, downloadPath)
				return
			}
			if !isVerified {
				//message.Err(ctx, fmt.Errorf("文件无法验证"))
				reportPass.DownloadResult = model.RevPackageDownloadResultSucceed
				reportPass.DownloadDetail = map[string]any{"reason": "文件无法验证"}
				reportPass.DownloadConsistencyCompareResult = model.DownloadConsistencyCompareResultFailed
				if packageM.TarHashVerified {
					reportPass.DownloadConsistencyCompareResult = model.DownloadConsistencyCompareResultSucceed // 恒安未上链，所以比对一定失败，但这里强制显示为成功
				}
				reportPass.DownloadConsistencyCompareDetail = map[string]any{"reason": "文件已被篡改"}
				f.reportDcmDownloadRecord(ctx, packageM, reportPass)
				f.doDownload(ctx, downloadPath)
				return
			}
			// --------------- verify hash ---------------

			reportPass.DownloadResult = model.RevPackageDownloadResultSucceed
			reportPass.DownloadConsistencyCompareResult = model.DownloadConsistencyCompareResultSucceed
			reportPass.WmEnabled = args.WmEnabled
			reportPass.WmData = wmData

			f.reportDcmDownloadRecord(ctx, packageM, reportPass)

			f.doDownload(ctx, downloadPath)
		}
	} else {
		fileIdArr := strings.SplitN(strings.ReplaceAll(fileId, "..", ""), "|", 4)
		if len(fileIdArr) != 4 {
			message.ErrBadRequest(ctx, fmt.Errorf("文件id非法"))
			return
		}
		zipFileDir, zipFileBasenameNoExt := fileIdArr[0], fileIdArr[1]
		targetFileDir, targetFileBasename := fileIdArr[2], fileIdArr[3]

		unzipDirPath := strings.TrimSpace(strings.Trim(zipFileDir+"/"+zipFileBasenameNoExt, "/"))
		zipFilePath := unzipDirPath + ".zip"
		targetFilePath := strings.TrimSpace(strings.Trim(zipFileDir+"/"+targetFileDir+"/"+targetFileBasename, "/"))

		packageDst := packageM.Filepath + "/" + packageM.Filename
		tempDir := utils2.GetUnGzipDir(packageDst) + strutil.RandomCharsV3(8)
		defer func() {
			_ = os.RemoveAll(tempDir)
		}()

		reportPass := reportDownloadRecordPass{
			ZipFilepath:                      zipFileDir,
			ZipFilename:                      zipFileBasenameNoExt + ".zip",
			ChildFilepath:                    targetFileDir,
			ChildFilename:                    targetFileBasename,
			DownloadResult:                   model.RevPackageDownloadResultFailed,
			DownloadDetail:                   nil,
			DownloadConsistencyCompareResult: model.DownloadConsistencyCompareResultUnspecified,
			DownloadConsistencyCompareDetail: nil,
			WmEnabled:                        false,
			WmData:                           nil,
		}

		if _, err = utils2.TryParseGzip(logger, packageDst, tempDir, false); err != nil {
			logger.Errorln(err)
			message.Err(ctx, common.ErrInternalServerError)
			reportPass.DownloadDetail = map[string]any{"err": err}
			f.reportDownloadRecord(ctx, packageM, reportPass)
			return
		}

		r, err := zip.OpenReader(filepath.Join(tempDir, zipFilePath))
		if err != nil {
			logger.Errorln(err)
			message.Err(ctx, common.ErrInternalServerError)
			reportPass.DownloadDetail = map[string]any{"err": err}
			f.reportDownloadRecord(ctx, packageM, reportPass)
			return
		}
		defer r.Close()

		for _, ff := range r.File {
			targetPath := path.Join(tempDir, unzipDirPath, ff.Name)
			fInfo := ff.FileInfo()
			if fInfo.IsDir() {
				if err := os.MkdirAll(targetPath, os.ModePerm); err != nil {
					logger.Errorln(err)
					message.Err(ctx, common.ErrInternalServerError)
					reportPass.DownloadDetail = map[string]any{"err": err}
					f.reportDownloadRecord(ctx, packageM, reportPass)
					return
				}
				continue
			} else {
				if err := os.MkdirAll(filepath.Dir(targetPath), os.ModePerm); err != nil {
					logger.Errorln(err)
					message.Err(ctx, common.ErrInternalServerError)
					reportPass.DownloadDetail = map[string]any{"err": err}
					f.reportDownloadRecord(ctx, packageM, reportPass)
					return
				}
			}
			if filepath.Base(ff.Name) != targetFileBasename {
				continue
			}

			if ff.IsEncrypted() {
				if args.Password = strings.TrimSpace(args.Password); len(args.Password) == 0 {
					message.Err(ctx, fmt.Errorf("the file is encrypted, please enter the password"))
					reportPass.DownloadDetail = map[string]any{"reason": "缺少密码"}
					f.reportDownloadRecord(ctx, packageM, reportPass)
					return
				}
				ff.SetPassword(args.Password)
			}

			rf, err := ff.Open()
			if err != nil {
				logger.Warnln(fmt.Errorf("open file failed, pwd:[%s],err:%v", args.Password, err))
				if ff.IsEncrypted() {
					message.Err(ctx, fmt.Errorf("password error, file decryption failed"))
					reportPass.DownloadDetail = map[string]any{"reason": "密码错误"}
					f.reportDownloadRecord(ctx, packageM, reportPass)
				} else {
					message.Err(ctx, common.ErrInternalServerError)
					reportPass.DownloadDetail = map[string]any{"err": err}
					f.reportDownloadRecord(ctx, packageM, reportPass)
				}
				return
			}
			out, err := os.Create(targetPath)
			if err != nil {
				logger.Errorln(err)
				message.Err(ctx, common.ErrInternalServerError)
				_ = rf.Close()
				reportPass.DownloadDetail = map[string]any{"err": err}
				f.reportDownloadRecord(ctx, packageM, reportPass)
				return
			}
			if _, err = io.Copy(out, rf); err != nil {
				_ = rf.Close()
				_ = out.Close()
				if reflect.TypeOf(err) == reflect.TypeOf(flate.CorruptInputError(0)) {
					message.Err(ctx, fmt.Errorf("password error, file decryption failed"))
					reportPass.DownloadDetail = map[string]any{"reason": "密码错误"}
					f.reportDownloadRecord(ctx, packageM, reportPass)
				} else {
					logger.Errorln(err)
					message.Err(ctx, common.ErrInternalServerError)
					reportPass.DownloadDetail = map[string]any{"err": err}
					f.reportDownloadRecord(ctx, packageM, reportPass)
				}
				return
			}
			_ = rf.Close()
			_ = out.Close()
		}

		rPath := filepath.Join(tempDir, targetFilePath)
		if _, err = os.Stat(rPath); err != nil {
			if errors.Is(err, os.ErrNotExist) {
				message.ErrNotFound(ctx, fmt.Errorf("file not exist"))
				reportPass.DownloadDetail = map[string]any{"reason": "文件不存在"}
				f.reportDownloadRecord(ctx, packageM, reportPass)
				return
			}
			logger.Errorln(err)
			message.Err(ctx, common.ErrInternalServerError)
			reportPass.DownloadDetail = map[string]any{"err": err}
			f.reportDownloadRecord(ctx, packageM, reportPass)
			return
		}

		// --------------- verify hash ---------------
		//ctx.Set("__dataUserId__", packageM.Cert.Data.DataUserId)
		adminUser := new(model.User)
		if err = orm.Where("username=?", model.AdminUsername).Where("is_admin", true).Limit(1).Find(adminUser).Error; err != nil {
			logger.Errorln(err)
			message.Err(ctx, common.ErrInternalServerError)
			reportPass.DownloadDetail = map[string]any{"err": err}
			f.reportDownloadRecord(ctx, packageM, reportPass)
			return
		}
		if adminUser.Username == "" {
			message.Err(ctx, fmt.Errorf("adminUser not found"))
			reportPass.DownloadDetail = map[string]any{"reason": "adminUser not found"}
			f.reportDownloadRecord(ctx, packageM, reportPass)
			return
		}
		//ctx.Set("__institution__", adminUser.Institution)

		certFilepath := filepath.Join(tempDir, packageM.Cert.Filepath, packageM.Cert.Filename)
		logger.Debugf("certFilePath: %s\n", certFilepath)
		certFileBytes, err := os.ReadFile(certFilepath)
		if err != nil {
			logger.Errorln(err)
			message.Err(ctx, common.ErrInternalServerError)
			reportPass.DownloadDetail = map[string]any{"reason": err}
			reportPass.DownloadConsistencyCompareResult = model.DownloadConsistencyCompareResultFailed
			reportPass.DownloadConsistencyCompareDetail = map[string]any{"err": err}
			f.reportDownloadRecord(ctx, packageM, reportPass)
			//f.doDownload(ctx, rPath)
			return
		}
		certFileData := new(model.RevPackageCertData)
		if err = json.Unmarshal(certFileBytes, certFileData); err != nil {
			logger.Errorln(err)
			message.Err(ctx, common.ErrInternalServerError)
			reportPass.DownloadDetail = map[string]any{"reason": err}
			reportPass.DownloadConsistencyCompareResult = model.DownloadConsistencyCompareResultFailed
			reportPass.DownloadConsistencyCompareDetail = map[string]any{"err": err}
			f.reportDownloadRecord(ctx, packageM, reportPass)
			//f.doDownload(ctx, rPath)
			return
		}
		//fileHash := ""
		//if certFileData.FileDetail != nil {
		//	for _, fileDetail := range certFileData.FileDetail {
		//		if fileDetail.FileName == targetFileBasename {
		//			fileHash = fileDetail.FileHash
		//			break
		//		}
		//	}
		//}
		//if fileHash == "" {
		//	err = fmt.Errorf("文件已被损坏")
		//	message.Err(ctx, err)
		//	reportPass.DownloadDetail = map[string]any{"reason": "未从证书获取到文件信息"}
		//	reportPass.DownloadConsistencyCompareResult = model.DownloadConsistencyCompareResultFailed
		//	reportPass.DownloadConsistencyCompareDetail = map[string]any{"err": err}
		//	f.reportDownloadRecord(ctx, packageM, reportPass)
		//	return
		//}

		downloadPath := rPath

		watermarkCfg := core.GetConf().App.Rev.Fisco.WatermarkApi
		watermarkFilepath := ""
		var wmData map[string]any
		if args.WmEnabled && watermarkCfg.Enabled {
			watermarkFilepath = filepath.Join(watermarkCfg.TempDir, rPath)
			if err := fileutil.CopyFile(rPath, watermarkFilepath, 0775); err != nil {
				logger.Errorln(err)
				message.Err(ctx, common.ErrInternalServerError)
				reportPass.DownloadDetail = map[string]any{"err": err}
				f.reportDownloadRecord(ctx, packageM, reportPass)
				return
			}

			defer os.RemoveAll(filepath.Dir(watermarkFilepath))

			operator := authutil.GetUsername(ctx)

			personId := ""
			if len(packageM.Cert.Data.FileDetail) > 0 {
				for _, fileDetail := range packageM.Cert.Data.FileDetail {
					if fileDetail.FileName == targetFileBasename {
						personId = fileDetail.PersonId
						break
					}
				}
			}

			_wmData, err := watermarkutil.DoWatermark(watermarkCfg.Addr, watermarkFilepath, personId, operator, adminUser.Institution)
			if err != nil {
				logger.Errorln(err)
				message.Err(ctx, common.ErrInternalServerError)
				reportPass.DownloadDetail = map[string]any{"err": err}
				f.reportDownloadRecord(ctx, packageM, reportPass)
				return
			}
			downloadPath = watermarkFilepath
			wmData = _wmData
		} else {
			args.WmEnabled = false
		}

		isVerified, err := fiscoutil.NewFiscoUtil().IsZipChildFileVerified(packageDst, filepath.Join(tempDir, zipFilePath), rPath, certFileData, string(certFileBytes), packageM.SendCode, watermarkFilepath)

		if err != nil {
			logger.Errorln(err)
			verr := errors2.FromError(err)
			if verr.Code != 0 {
				//message.Err(ctx, fmt.Errorf("文件验证失败"))
				reportPass.DownloadResult = model.RevPackageDownloadResultSucceed
				reportPass.DownloadDetail = map[string]any{"reason": "文件验证失败"}
				reportPass.DownloadConsistencyCompareResult = model.DownloadConsistencyCompareResultFailed
				reportPass.DownloadConsistencyCompareDetail = map[string]any{"err": err}
				f.reportDownloadRecord(ctx, packageM, reportPass)
				f.doDownload(ctx, downloadPath)
				return
			}
			//message.Err(ctx, common.ErrInternalServerError)
			reportPass.DownloadResult = model.RevPackageDownloadResultSucceed
			reportPass.DownloadDetail = map[string]any{"err": err}
			reportPass.DownloadConsistencyCompareResult = model.DownloadConsistencyCompareResultFailed
			reportPass.DownloadConsistencyCompareDetail = map[string]any{"err": err}
			f.reportDownloadRecord(ctx, packageM, reportPass)
			f.doDownload(ctx, downloadPath)
			return
		}
		if !isVerified {
			//message.Err(ctx, fmt.Errorf("文件无法验证"))
			reportPass.DownloadResult = model.RevPackageDownloadResultSucceed
			reportPass.DownloadDetail = map[string]any{"reason": "文件无法验证"}
			reportPass.DownloadConsistencyCompareResult = model.DownloadConsistencyCompareResultFailed
			reportPass.DownloadConsistencyCompareDetail = map[string]any{"reason": "文件已被篡改"}
			f.reportDownloadRecord(ctx, packageM, reportPass)
			f.doDownload(ctx, downloadPath)
			return
		}
		// --------------- verify hash ---------------

		reportPass.DownloadResult = model.RevPackageDownloadResultSucceed
		reportPass.DownloadConsistencyCompareResult = model.DownloadConsistencyCompareResultSucceed
		reportPass.WmEnabled = args.WmEnabled
		reportPass.WmData = wmData

		f.reportDownloadRecord(ctx, packageM, reportPass)

		f.doDownload(ctx, downloadPath)
	}
}

func (f *File) doDownload(ctx *gin.Context, rPath string) {
	downloadFilepath := rPath
	ctx.Writer.Header().Set("Content-Type", "application/octet-stream")
	ctx.FileAttachment(downloadFilepath, filepath.Base(downloadFilepath))
}

// GetLatestChildDownloadRecord 获取子文件最新下载记录
// @Router /child-file-download/latest-record [post]
func (f *File) GetLatestChildDownloadRecord(ctx *gin.Context, args *request.FileChildFileDownloadRequest) (any, error) {
	var err error
	if args == nil {
		args = &request.FileChildFileDownloadRequest{}
	}
	logger, err := core.GetLogger()
	if err != nil {
		log.Fatalln(err)
		return nil, common.ErrInternalServerError
	}
	if args.PackageId = strings.TrimSpace(args.PackageId); len(args.PackageId) == 0 {
		return nil, fmt.Errorf("包文件id不能为空")
	}
	if args.FileId = strings.TrimSpace(args.FileId); len(args.FileId) == 0 {
		return nil, fmt.Errorf("子文件id不能为空")
	}
	orm, err := core.GetDatabase()
	if err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}

	latestRecord := &model.RevPackageDownloadRecord{}
	err = orm.Raw(`SELECT
  rpdr.id,
  rpdr.file_id,
  rpdr.download_at,
  rpdr.download_ip,
  rpdr.download_result,
  rpdr.download_consistency_compare_result,
  rpdr.wm_enabled,
  rpdr.wm_data
FROM
  rev_package_download_records rpdr 
WHERE
  rpdr.file_id =? 
  AND rpdr.package_id =?
  AND rpdr.username =?	
ORDER BY
  rpdr.id DESC 
  LIMIT 1`, args.FileId, args.PackageId, authutil.GetUsername(ctx)).Scan(latestRecord).Error
	if err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}
	if latestRecord.Id == "" {
		return nil, nil
	}
	return &response.FileChildFileLatestDownloadRecord{
		DownloadAt:                       latestRecord.DownloadAt.Format(time.DateTime),
		DownloadResult:                   latestRecord.DownloadResult,
		DownloadIp:                       latestRecord.DownloadIp,
		DownloadConsistencyCompareResult: latestRecord.DownloadConsistencyCompareResult,
		DownloadWmEnabled:                latestRecord.WmEnabled,
		DownloadWmData:                   latestRecord.WmData,
	}, nil
}

// ToggleArchived 切换文档的归档状态
// @Router /toggle-archived [post]
func (f *File) ToggleArchived(ctx *gin.Context, args *request.FileToggleArchivedRequest) error {
	var err error
	if args == nil {
		args = &request.FileToggleArchivedRequest{}
	}
	if args.PackageId = strings.TrimSpace(args.PackageId); len(args.PackageId) == 0 {
		return fmt.Errorf("包id不能为空")
	}
	logger, err := core.GetLogger()
	if err != nil {
		log.Fatalln(err)
		return common.ErrInternalServerError
	}
	orm, err := core.GetDatabase()
	if err != nil {
		logger.Errorln(err)
		return common.ErrInternalServerError
	}
	orm = orm.WithContext(ctx)

	redisPool, err := core.GetRedisPool()
	if err != nil {
		logger.Errorln(err)
		return common.ErrInternalServerError
	}
	mutex := redisPool.NewMutex("REV:FILE:TOGGLE:ARCHIVED:" + args.PackageId)
	if err = mutex.Lock(); err != nil {
		logger.Errorln(err)
		return common.ErrInternalServerBusy
	}
	defer mutex.Unlock()

	wd, err := os.Getwd()
	if err != nil {
		logger.Errorln(err)
		return common.ErrInternalServerError
	}

	if args.Archived {
		pkgM := new(model.RevPackage)
		if err = orm.Where("id = ?", args.PackageId).First(pkgM).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("文件不存在")
			}
			logger.Errorln(err)
			return common.ErrInternalServerError
		}
		if pkgM.ArchivedAt.Valid {
			return nil
		}
		tarFilepath := filepath.Join(wd, pkgM.Filepath, pkgM.Filename)
		if _, err := os.Stat(tarFilepath); err != nil {
			if errors.Is(err, os.ErrNotExist) {
				return fmt.Errorf("目标文件不存在")
			}
		}
		archiveDir := filepath.Join(wd, core.GetConf().App.Rev.Fisco.Tars.Archive)
		if err = os.MkdirAll(archiveDir, os.ModePerm); err != nil {
			logger.Errorln(err)
			return common.ErrInternalServerError
		}
		archiveFilepath := filepath.Join(archiveDir, pkgM.Filename)
		err = orm.Transaction(func(tx *gorm.DB) error {
			pkgM.ArchivedAt = sql.NullTime{
				Time:  time.Now(),
				Valid: true,
			}
			if err = tx.Model(pkgM).Select("archived_at").Updates(pkgM).Error; err != nil {
				logger.Errorln(err)
				return common.ErrInternalServerError
			}
			return nil
		})
		if err != nil {
			return err
		}
		if err = moveFile(tarFilepath, archiveFilepath); err != nil {
			logger.Errorln(err)
			return common.ErrInternalServerError
		}
	} else {
		pkgM := new(model.RevPackage)
		if err = orm.Unscoped().Where("id = ?", args.PackageId).First(pkgM).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("文件不存在")
			}
			logger.Errorln(err)
			return common.ErrInternalServerError
		}
		if !pkgM.ArchivedAt.Valid {
			return nil
		}
		archiveDir := filepath.Join(wd, core.GetConf().App.Rev.Fisco.Tars.Archive)
		archiveFilepath := filepath.Join(archiveDir, pkgM.Filename)
		if _, err := os.Stat(archiveFilepath); err != nil {
			if errors.Is(err, os.ErrNotExist) {
				return fmt.Errorf("目标文件不存在")
			}
		}

		tarFilepath := filepath.Join(wd, pkgM.Filepath, pkgM.Filename)
		if err = os.MkdirAll(filepath.Dir(tarFilepath), os.ModePerm); err != nil {
			logger.Errorln(err)
			return common.ErrInternalServerError
		}
		err = orm.Transaction(func(tx *gorm.DB) error {
			pkgM.ArchivedAt = sql.NullTime{}
			if err = tx.Unscoped().Model(pkgM).Select("archived_at").Updates(pkgM).Error; err != nil {
				logger.Errorln(err)
				return common.ErrInternalServerError
			}
			return nil
		})
		if err != nil {
			return err
		}
		if err = moveFile(archiveFilepath, tarFilepath); err != nil {
			logger.Errorln(err)
			return common.ErrInternalServerError
		}
	}
	return nil
}

func moveFile(src, dst string) error {
	// 打开源文件
	srcFile, err := os.Open(src)
	if err != nil {
		return fmt.Errorf("open source file: %w", err)
	}
	defer srcFile.Close()

	// 创建目标文件
	dstFile, err := os.Create(dst)
	if err != nil {
		return fmt.Errorf("create destination file: %w", err)
	}
	defer dstFile.Close()

	// 复制文件内容
	if _, err := io.Copy(dstFile, srcFile); err != nil {
		return fmt.Errorf("copy file: %w", err)
	}

	// 删除源文件
	if err := os.Remove(src); err != nil {
		return fmt.Errorf("remove source file: %w", err)
	}

	return nil
}
