package rev

import (
	"code.ixdev.cn/cnix/cbdv/hk-box-be/core"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/model"
	response "code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/response/rev"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/validator/common"
	"github.com/gin-gonic/gin"
	"log"
)

// Sender 发送方
// @Router /api/v1/rev/sender []
type Sender struct {
}

// ListAll 获取所有发送方
// @Router /list-all [post]
func (s *Sender) ListAll(ctx *gin.Context) (*response.SenderListAll, error) {
	var err error
	logger, err := core.GetLogger()
	if err != nil {
		log.Println(err)
		return nil, common.ErrInternalServerError
	}
	orm, err := core.GetDatabase()
	if err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}
	orm = orm.WithContext(ctx)

	data := make([]*response.SenderListAllDatum, 0)
	iter, err := orm.Model(new(model.RevSender)).Select("sender_id", "sender_name", "sender_name_py").Order("id DESC").Rows()
	if err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}
	defer iter.Close()
	for iter.Next() {
		n := new(model.RevSender)
		if err = orm.ScanRows(iter, n); err != nil {
			logger.Errorln(err)
			return nil, common.ErrInternalServerError
		}
		data = append(data, &response.SenderListAllDatum{
			SenderId:     n.SenderId,
			SenderName:   n.SenderName,
			SenderNamePy: n.SenderNamePy,
		})
	}
	return &response.SenderListAll{
		Data: data,
	}, nil
}
