package sender

import (
	"archive/tar"
	"bytes"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/core"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/global"
	sender3 "code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/entity/sender"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/model"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/request/sender"
	sender2 "code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/response/sender"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/validator/common"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/utils/authutil"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/utils/fileutil"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/utils/timeutil"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/utils/watermarkutil"
	"code.ixdev.cn/liush/xpb/pagination"
	"compress/gzip"
	"encoding/json"
	"fmt"
	"github.com/avast/retry-go/v4"
	"github.com/gin-gonic/gin"
	"github.com/gookit/goutil/strutil"
	"github.com/tjfoc/gmsm/sm3"
	"golang.org/x/sync/errgroup"
	"io"
	"log"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"
)

// File 文件管理
// @Router /api/v1/sender/file []
type File struct {
}

// List 获取文件列表
// @Router /list [post]
func (f *File) List(ctx *gin.Context, args *sender.FileListRequest) (*sender2.FileListResponse, error) {
	var err error
	if args == nil {
		args = &sender.FileListRequest{}
	}
	logger, err := core.GetLogger()
	if err != nil {
		log.Println(err)
		return nil, common.ErrInternalServerError
	}
	orm, err := core.GetDatabase()
	if err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}
	orm = orm.WithContext(ctx)
	username := authutil.GetUsername(ctx)

	total, data := int64(0), make([]*sender2.FileListDatum, 0)

	condition, bindings := make([]string, 0), make([]any, 0)

	condition, bindings = append(condition, "fu.username=?"), append(bindings, username)
	if args.Filename = strings.TrimSpace(args.Filename); len(args.Filename) != 0 {
		condition = append(condition, "fu.filename like ?")
		bindings = append(bindings, fmt.Sprintf("%%%s%%", args.Filename))
	}
	if args.FileHash = strings.TrimSpace(args.FileHash); len(args.FileHash) != 0 {
		condition = append(condition, "fu.file_hash like ?")
		bindings = append(bindings, fmt.Sprintf("%%%s%%", args.FileHash))
	}
	if args.SendAtStart = strings.TrimSpace(args.SendAtStart); len(args.SendAtStart) != 0 {
		if v := timeutil.ParseDatetime(args.SendAtStart); !v.IsZero() {
			condition = append(condition, "fu.created_at >= ?")
			bindings = append(bindings, v)
		}
	}
	if args.SendAtEnd = strings.TrimSpace(args.SendAtEnd); len(args.SendAtEnd) != 0 {
		if v := timeutil.ParseDatetime(args.SendAtEnd); !v.IsZero() {
			condition = append(condition, "fu.created_at<?")
			bindings = append(bindings, v)
		}
	}

	where := ""
	if len(condition) > 0 {
		where = " WHERE " + strings.Join(condition, " AND ")
	}
	countSql := `select count(fu.id)
		from file_upload fu` + where
	if err = orm.Raw(countSql, bindings...).Scan(&total).Error; err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}
	dataSql := `select fu.*,ifnull((select fsr.status from file_send_record fsr where fsr.file_id = fu.id and fsr.deleted_at is null order by fsr.id desc limit 1),
              0) as send_status from file_upload fu` + where
	dataSql += " ORDER BY fu.id DESC"
	dataSql += fmt.Sprintf(" LIMIT %d,%d", pagination.ParseOffset(args.Pagination), pagination.ParseLimit(args.Pagination))
	iter, err := orm.Raw(dataSql, bindings...).Rows()
	if err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}
	defer iter.Close()
	for iter.Next() {
		n := new(sender3.FileListDatum)
		if err = orm.ScanRows(iter, n); err != nil {
			logger.Errorln(err)
			return nil, common.ErrInternalServerError
		}
		datum := &sender2.FileListDatum{
			Id:         n.Id,
			Filename:   n.Filename,
			FileSize:   n.FileSize,
			FileHash:   n.FileHash,
			CreatedAt:  n.CreatedAt.Format(time.DateTime),
			SendStatus: n.SendStatus,
		}
		data = append(data, datum)
	}

	return &sender2.FileListResponse{
		Total: total,
		Data:  data,
	}, nil
}

// PreviewUploadWatermarkInfo 预览上传水印信息
// @Router /preview-upload-watermark-info [post]
func (f *File) PreviewUploadWatermarkInfo(ctx *gin.Context, args *sender.PreviewUploadWatermarkInfoRequest) (any, error) {
	var err error
	logger, err := core.GetLogger()
	if err != nil {
		log.Println(err)
		return nil, common.ErrInternalServerError
	}

	orm, err := core.GetDatabase()
	if err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}

	adminUser := new(model.User)
	if err = orm.Where("username=?", model.AdminUsername).Where("is_admin", true).Limit(1).Find(adminUser).Error; err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}
	if adminUser.Username == "" {
		err = fmt.Errorf("adminUser not found")
		logger.Errorln(err)
		return nil, err
	}

	operator := authutil.GetUsername(ctx)
	m := new(model.SysConfig)
	if err = orm.First(m, "id=?", model.SysConfigKey).Error; err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}

	wmData := map[string]any{
		"rev_dl_wm_enabled":    m.RevDlWmEnabled,
		"sender_up_wm_enabled": m.SenderUpWmEnabled,
		"institution":          adminUser.Institution,
		"operator":             operator,
	}

	return wmData, nil
}

// Upload 文件上传
// @Router /upload [post]
func (f *File) Upload(ctx *gin.Context) (*sender2.FileUploadResponse, error) {
	var err error
	logger, err := core.GetLogger()
	if err != nil {
		log.Println(err)
		return nil, common.ErrInternalServerError
	}

	orm, err := core.GetDatabase()
	if err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}

	adminUser := new(model.User)
	if err = orm.Where("username=?", model.AdminUsername).Where("is_admin", true).Limit(1).Find(adminUser).Error; err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}
	if adminUser.Username == "" {
		err = fmt.Errorf("adminUser not found")
		logger.Errorln(err)
		return nil, err
	}

	wmEnabled, _ := strconv.ParseBool(ctx.Query("wm_enabled"))

	form, err := ctx.MultipartForm()
	if err != nil {
		logger.Errorln(err)
		return nil, common.ErrInternalServerError
	}
	files := form.File["files[]"]

	filenamePrefix := strconv.FormatInt(time.Now().UnixMicro(), 10) + "_"
	wg, _ := errgroup.WithContext(ctx)
	for _, file := range files {
		file := file
		wg.Go(func() error {
			sum, err := f.doUpload(ctx, filenamePrefix, file, adminUser, wmEnabled)
			if err != nil {
				return err
			}
			logger.Infof("file[%s] sum: %s\n", file.Filename, sum)
			return nil
		})
	}
	if err = wg.Wait(); err != nil {
		return nil, err
	}

	return nil, nil
}

func (f *File) doUpload(ctx *gin.Context, filenamePrefix string, file *multipart.FileHeader, adminUser *model.User, wmEnabled bool) (string, error) {
	var err error
	logger, _ := core.GetLogger()

	username := authutil.GetUsername(ctx)
	nFilename := filenamePrefix + file.Filename
	dst := fmt.Sprintf("%s/%s/%s", strings.Trim(core.GetConf().App.Sender.Upload.Dst, "/"), username, nFilename)
	dstDir, dstFilename := filepath.Split(dst)
	if err = os.MkdirAll(dstDir, os.ModePerm); err != nil {
		logger.Errorln(err)
		return "", common.ErrInternalServerError
	}
	oriDst := dst
	filesize := file.Size

	var wmData map[string]any
	var src multipart.File
	// 水印处理
	if watermarkCfg := core.GetConf().App.Rev.Fisco.WatermarkApi; watermarkCfg.Enabled && wmEnabled {
		// 保存文件
		if err = ctx.SaveUploadedFile(file, dst); err != nil {
			logger.Errorln(err)
			return "", common.ErrInternalServerError
		}
		watermarkDir := filepath.Join(watermarkCfg.TempDir, "__upload__", strutil.RandomCharsV3(8)+strconv.FormatInt(core.GetNextId(), 10))
		if err = os.MkdirAll(watermarkDir, os.ModePerm); err != nil {
			logger.Errorln(err)
			return "", common.ErrInternalServerError
		}
		defer os.RemoveAll(watermarkDir)

		watermarkFilepath := filepath.Join(watermarkDir, dstFilename)
		if err := fileutil.CopyFile(dst, watermarkFilepath, 0775); err != nil {
			logger.Errorln(err)
			return "", common.ErrInternalServerError
		}
		operator := authutil.GetUsername(ctx)
		_wmData, err := watermarkutil.DoWatermark(watermarkCfg.Addr, watermarkFilepath, "", operator, adminUser.Institution)
		if err != nil {
			logger.Errorln(err)
			return "", common.ErrInternalServerError
		}
		wmData = _wmData
		_src, err := os.Open(watermarkFilepath)
		if err != nil {
			logger.Errorln(err)
			return "", common.ErrInternalServerError
		}
		_stat, err := _src.Stat()
		if err != nil {
			logger.Errorln(err)
			return "", common.ErrInternalServerError
		}
		filesize = _stat.Size()
		src = _src
		savedWatermarkFilepath := filepath.Join(dstDir, "watermark_"+dstFilename)
		dst = savedWatermarkFilepath
		dstDir, dstFilename = filepath.Split(dst)
	} else {
		wmEnabled = false
		_src, err := file.Open()
		if err != nil {
			logger.Errorln(err)
			return "", common.ErrInternalServerError
		}
		src = _src
	}
	if src == nil {
		logger.Errorln("file.Open() error")
		return "", common.ErrInternalServerError
	}
	defer src.Close()

	fileOut, err := os.Create(dst)
	if err != nil {
		logger.Errorln(err)
		return "", common.ErrInternalServerError
	}
	defer fileOut.Close()

	hm := sm3.New()

	gzipDistFilename := dstFilename + ".tar.gz"
	gzipDst := dstDir + gzipDistFilename

	gzipFile, err := os.OpenFile(gzipDst, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, os.ModePerm)
	if err != nil {
		logger.Errorln(err)
		return "", common.ErrInternalServerError
	}
	gzw := gzip.NewWriter(gzipFile)
	tw := tar.NewWriter(gzw)

	hdr := &tar.Header{
		Name: "data/" + file.Filename,
		Size: filesize,
		Mode: 0777,
	}
	logger.Debugf("file[%s] size[%d]\n", nFilename, filesize)
	if err = tw.WriteHeader(hdr); err != nil {
		logger.Errorln(err)
		return "", common.ErrInternalServerError
	}

	mWriter := new(MultiWriter)
	mWriter.AddWriter(fileOut)
	mWriter.AddWriter(tw)
	mWriter.AddWriter(hm)

	if _, err := io.Copy(mWriter, src); err != nil {
		logger.Errorln(err)
		return "", common.ErrInternalServerError
	}

	nFileSum := fmt.Sprintf("%x", hm.Sum(nil))

	remoteUploadFileInfo := sender3.RemoteUploadFileInfo{
		FileHash:  []map[string]any{},
		OtherInfo: nil,
	}
	remoteUploadFileInfo.FileHash = append(remoteUploadFileInfo.FileHash, map[string]any{
		file.Filename: nFileSum,
	})
	remoteUploadFileInfoB, err := json.Marshal(remoteUploadFileInfo)
	if err != nil {
		logger.Errorln(err)
		return "", common.ErrInternalServerError
	}

	if err = tw.WriteHeader(&tar.Header{
		Name: "file_info.json",
		Size: int64(len(remoteUploadFileInfoB)),
		Mode: 0777,
	}); err != nil {
		logger.Errorln(err)
		return "", common.ErrInternalServerError
	}
	if _, err := tw.Write(remoteUploadFileInfoB); err != nil {
		logger.Errorln(err)
		return "", common.ErrInternalServerError
	}

	if err = tw.Close(); err != nil {
		logger.Errorln(err)
		return "", common.ErrInternalServerError
	}
	if err = gzw.Close(); err != nil {
		logger.Errorln(err)
		return "", common.ErrInternalServerError
	}
	gzipFileInfo, err := gzipFile.Stat()
	if err != nil {
		logger.Errorln(err)
		return "", common.ErrInternalServerError
	}
	if err = gzipFile.Close(); err != nil {
		logger.Errorln(err)
		return "", common.ErrInternalServerError
	}

	gf, err := os.Open(gzipDst)
	if err != nil {
		logger.Errorln(err)
		return "", common.ErrInternalServerError
	}
	defer gf.Close()

	dataReader := bytes.NewBuffer(nil)
	w := multipart.NewWriter(dataReader)
	fw, err := w.CreateFormFile("file", gzipDistFilename)
	if err != nil {
		logger.Errorln(err)
		return "", common.ErrInternalServerError
	}
	hm.Reset()

	gWriter := new(MultiWriter)
	gWriter.AddWriter(fw)
	gWriter.AddWriter(hm)

	if _, err := io.Copy(gWriter, gf); err != nil {
		logger.Errorln(err)
		return "", common.ErrInternalServerError
	}
	if err = w.Close(); err != nil {
		logger.Errorln(err)
		return "", common.ErrInternalServerError
	}
	gzipFileSum := fmt.Sprintf("%x", hm.Sum(nil))
	sendSize := dataReader.Len()
	logger.Debugf("request send size: %d, file size: %d, sum:[%s]\n", sendSize, gzipFileInfo.Size(), gzipFileSum)

	orm, err := core.GetDatabase(global.DatabaseConnectDefault)
	if err != nil {
		logger.Errorln(err)
		return "", common.ErrInternalServerError
	}
	uploadModel := &model.FileUpload{
		Id:            core.GetNextId(),
		Filename:      file.Filename,
		LocalFilename: nFilename,
		Filepath:      oriDst,
		FileSize:      file.Size,
		FileHash:      nFileSum,
		Username:      username,
	}
	if err = orm.Create(uploadModel).Error; err != nil {
		logger.Errorln(err)
		return "", common.ErrInternalServerError
	}

	sendRecordModel := &model.FileSendRecord{
		Id:              core.GetNextId(),
		FileId:          uploadModel.Id,
		SendFilename:    gzipDistFilename,
		SendFilepath:    gzipDst,
		SendFileSize:    gzipFileInfo.Size(),
		SendFileHash:    gzipFileSum,
		SendArchiveData: remoteUploadFileInfo,
		SendAt:          time.Now(),
		TakeUpTime:      0,
		Status:          model.FileSendStatusFailed,
		StatusDetail:    nil,
		WmEnabled:       wmEnabled,
		WmData:          wmData,
	}

	req, err := http.NewRequest(http.MethodPost, core.Container.Conf.App.Sender.Upload.RemoteUrl, dataReader)
	if err != nil {
		logger.Errorln(err)
		return "", common.ErrInternalServerError
	}
	startSendTime := time.Now().UnixMilli()
	req.Header.Set("Content-Type", w.FormDataContentType())
	respBody, err := retry.DoWithData(func() ([]byte, error) {
		resp, err := http.DefaultClient.Do(req)
		if err != nil {
			return nil, err
		}
		defer resp.Body.Close()
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return nil, err
		}
		return body, nil
	}, retry.Attempts(3))
	sendTakeUpTime := time.Now().UnixMilli() - startSendTime
	if err != nil {
		logger.Errorln(err)
		sendRecordModel.StatusDetail = err.Error()
		sendRecordModel.TakeUpTime = sendTakeUpTime
		if verr := orm.Create(sendRecordModel).Error; verr != nil {
			logger.Errorln(verr)
		}
		return "", fmt.Errorf("服务错误")
	}
	if len(respBody) == 0 {
		sendRecordModel.StatusDetail = "resp empty"
		sendRecordModel.TakeUpTime = sendTakeUpTime
		if verr := orm.Create(sendRecordModel).Error; verr != nil {
			logger.Errorln(verr)
		}
		return "", fmt.Errorf("服务错误")
	}

	logger.Debugf("request upload api resp body: %v \n", string(respBody))
	err = os.WriteFile(gzipDst+"_up.log", respBody, os.ModePerm)
	if err != nil {
		logger.Errorln(err)
		sendRecordModel.StatusDetail = err
		sendRecordModel.TakeUpTime = sendTakeUpTime
		if verr := orm.Create(sendRecordModel).Error; verr != nil {
			logger.Errorln(verr)
		}
		return "", common.ErrUploadFailed
	}

	respV := new(sender3.RemoteUploadSuccessResponse)
	if err = json.Unmarshal(respBody, respV); err != nil {
		logger.Errorln(err)
		sendRecordModel.StatusDetail = err
		sendRecordModel.TakeUpTime = sendTakeUpTime
		if verr := orm.Create(sendRecordModel).Error; verr != nil {
			logger.Errorln(verr)
		}
		return "", common.ErrInternalServerError
	}

	if respV.Message != "Success" {
		verr := fmt.Errorf("file[%s] upload failed:[%s]\n", gzipDistFilename, string(respBody))
		logger.Errorln(verr)
		sendRecordModel.TakeUpTime = sendTakeUpTime
		sendRecordModel.StatusDetail = respV
		if verr := orm.Create(sendRecordModel).Error; verr != nil {
			logger.Errorln(verr)
		}
		return "", fmt.Errorf(respV.Message)
	}

	sendRecordModel.TakeUpTime = sendTakeUpTime
	sendRecordModel.Status = model.FileSendStatusOk
	sendRecordModel.StatusDetail = respV
	if err = orm.Create(sendRecordModel).Error; err != nil {
		logger.Errorln(err)
		return "", common.ErrInternalServerError
	}

	return gzipFileSum, nil
}

type MultiWriter struct {
	writers []io.Writer
}

func (m *MultiWriter) AddWriter(writer io.Writer) {
	m.writers = append(m.writers, writer)
}

func (m *MultiWriter) Write(p []byte) (n int, err error) {
	for _, writer := range m.writers {
		n, err = writer.Write(p)
		if err != nil {
			break
		}
	}
	return
}
