package response

import (
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/model"
)

type UserListDatum struct {
	Id        int64            `json:"id"`
	Username  string           `json:"username"`
	RealName  string           `json:"real_name"`
	IdNumber  string           `json:"id_number"`
	Email     string           `json:"email"`
	Phone     string           `json:"phone"`
	Status    model.UserStatus `json:"status"`
	IsAdmin   bool             `json:"is_admin"`
	CreatedAt string           `json:"created_at"`
	UpdatedAt string           `json:"updated_at"`
}

type UserList struct {
	Total int64            `json:"total"`
	Data  []*UserListDatum `json:"data"`
}

type UserDetail struct {
	Id              int64             `json:"id" gorm:"primaryKey"`
	Username        string            `json:"username"`
	CompanyId       string            `json:"company_id"`
	Institution     string            `json:"institution"`
	Industry        string            `json:"industry"`
	AccessAddr      string            `json:"access_addr"`
	BelongingRegion string            `json:"belonging_region"`
	RealName        string            `json:"real_name"`
	IdNumber        string            `json:"id_number"`
	Email           string            `json:"email"`
	Phone           string            `json:"phone"`
	AuditResult     model.AuditResult `json:"audit_result"`
	AuditComment    string            `json:"audit_comment"`
	IsAdmin         bool              `json:"is_admin"`
	Status          model.UserStatus  `json:"status"`
	CreatedAt       string            `json:"created_at"`
	UpdatedAt       string            `json:"updated_at"`
}
