package sender

type FileListDatum struct {
	Id         int64  `json:"id" gorm:"primaryKey"`
	Filename   string `json:"filename"`
	FileSize   int64  `json:"file_size"`
	FileHash   string `json:"file_hash"`
	CreatedAt  string `json:"created_at"`
	SendStatus int8   `json:"send_status"`
}

type FileListResponse struct {
	Total int64            `json:"total"`
	Data  []*FileListDatum `json:"data"`
}

type FileUploadResponse struct {
}
