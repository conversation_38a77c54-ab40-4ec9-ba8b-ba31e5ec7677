package rev

import "code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/model"

type FileListResponseDatum struct {
	Id               string `json:"id"`
	SendCode         string `json:"send_code"`
	SenderId         string `json:"sender_id"`
	SenderName       string `json:"sender_name"`
	SendAt           string `json:"send_at"`
	Filename         string `json:"filename"`
	Filesize         int64  `json:"filesize"`
	ZipFileHash      string `json:"zip_file_hash"`
	ZipFileCount     int64  `json:"zip_file_count"`
	TarHashVerified  bool   `json:"tar_hash_verified"`
	ZipHashVerified  bool   `json:"zip_hash_verified"`
	ArchivedAt       string `json:"archived_at,omitempty"`
	DownloadDisabled bool   `json:"download_disabled"`
}

type FileListResponse struct {
	Total int64                    `json:"total"`
	Data  []*FileListResponseDatum `json:"data"`
}

type FileChildFilesResponseDatum struct {
	Id                                     string                                 `json:"id"`
	Filename                               string                                 `json:"filename"`
	Filesize                               int64                                  `json:"filesize"`
	IsEncrypted                            bool                                   `json:"is_encrypted"`
	FileHash                               string                                 `json:"file_hash"`
	FileWatermarkHash                      string                                 `json:"file_watermark_hash"`
	LatestDownloadAt                       string                                 `json:"latest_download_at"`
	LatestDownloadResult                   model.RevPackageDownloadResult         `json:"latest_download_result"`
	LatestDownloadIp                       string                                 `json:"latest_download_ip"`
	LatestDownloadConsistencyCompareResult model.DownloadConsistencyCompareResult `json:"latest_download_consistency_compare_result"`
	LatestDownloadWmEnabled                bool                                   `json:"latest_download_wm_enabled"`
	LatestDownloadWmData                   map[string]any                         `json:"latest_download_wm_data"`
	DownloadDisabled                       bool                                   `json:"download_disabled"`
}

type FileChildFileLatestDownloadRecord struct {
	DownloadAt                       string                                 `json:"download_at"`
	DownloadResult                   model.RevPackageDownloadResult         `json:"download_result"`
	DownloadIp                       string                                 `json:"download_ip"`
	DownloadConsistencyCompareResult model.DownloadConsistencyCompareResult `json:"download_consistency_compare_result"`
	DownloadWmEnabled                bool                                   `json:"download_wm_enabled"`
	DownloadWmData                   map[string]any                         `json:"download_wm_data"`
}
