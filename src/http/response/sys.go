package response

type SysResourcesResponseDisk struct {
	Total       uint64  `json:"total"`
	Free        uint64  `json:"free"`
	Used        uint64  `json:"used"`
	UsedPercent float64 `json:"usedPercent"`
}

type SysResourcesResponseMemory struct {
	Total       uint64  `json:"total"`
	Available   uint64  `json:"available"`
	Used        uint64  `json:"used"`
	UsedPercent float64 `json:"usedPercent"`
	Free        uint64  `json:"free"`
}

type SysResourcesResponse struct {
	Memory      SysResourcesResponseMemory `json:"memory"`
	ProjectDisk SysResourcesResponseDisk   `json:"projectDisk"`
}
