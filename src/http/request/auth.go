package request

type AuthLoginRequest struct {
	Username string `json:"username" xml:"username" yaml:"username"`
	Password string `json:"password" xml:"password" yaml:"password"`
}

type AuthInitRequest struct {
	//CompanyId       string `json:"company_id"`
	Institution     string `json:"institution"`
	Industry        string `json:"industry"`
	AccessAddr      string `json:"access_addr"`
	BelongingRegion string `json:"belonging_region"`
	RealName        string `json:"real_name"`
	IdNumber        string `json:"id_number"`
	Email           string `json:"email"`
	Phone           string `json:"phone"`
}

type AuthInitResetPwd struct {
	Pwd string `json:"pwd"`
}
