package rev

import "code.ixdev.cn/liush/xpb/pagination"

type FileListRequest struct {
	Pagination  *pagination.Pagination `json:"pagination"`
	Sorter      string                 `json:"sorter"`
	Filename    string                 `json:"filename"`
	FileHash    string                 `json:"file_hash"`
	Sender      string                 `json:"sender"`
	SendCode    string                 `json:"send_code"`
	SendAtStart string                 `json:"send_at_start"`
	SendAtEnd   string                 `json:"send_at_end"`
	Archived    bool                   `json:"archived"`
}

type FileChildFilesRequest struct {
	PackageId string `json:"package_id"`
}

type PreviewChildFileWatermarkInfoRequest struct {
	PackageId string `json:"package_id"`
	FileId    string `json:"file_id"`
}

type FileChildFileDownloadRequest struct {
	PackageId string `json:"package_id"`
	FileId    string `json:"file_id"`
	Password  string `json:"password"`
	WmEnabled bool   `json:"wm_enabled"`
}

type FileToggleArchivedRequest struct {
	PackageId string `json:"package_id"`
	Archived  bool   `json:"archived"`
}
