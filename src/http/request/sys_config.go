package request

import "code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/model"

type UpdateSysConfigRequest struct {
	RevDlWmEnabled    *bool `json:"rev_dl_wm_enabled" xml:"rev_dl_wm_enabled" yaml:"rev_dl_wm_enabled"`
	SenderUpWmEnabled *bool `json:"sender_up_wm_enabled" xml:"sender_up_wm_enabled" yaml:"sender_up_wm_enabled"`
}

type UpdateSysConfigRemoteUploadConfig struct {
	SFTP  *model.SysConfigSftp  `json:"sftp" xml:"sftp" yaml:"sftp"`
	Dicom *model.SysConfigDicom `json:"dicom" xml:"dicom" yaml:"dicom"`
}
