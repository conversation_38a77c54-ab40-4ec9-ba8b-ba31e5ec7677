package request

type PlatformAuditNotify struct {
	CompanyId       string `json:"company_id" xml:"company_id" yaml:"company_id"`
	AuditResult     string `json:"audit_result" xml:"audit_result" yaml:"audit_result"`
	AuditComment    string `json:"audit_comment" xml:"audit_comment" yaml:"audit_comment"`
	Institution     string `json:"institution" xml:"institution" yaml:"institution"`
	Industry        string `json:"industry" xml:"industry" yaml:"industry"`
	AccessAddr      string `json:"access_addr" xml:"access_addr" yaml:"access_addr"`
	BelongingRegion string `json:"belonging_region" xml:"belonging_region" yaml:"belonging_region"`
	Name            string `json:"name" xml:"name" yaml:"name"`
	IdNumber        string `json:"id_number" xml:"id_number" yaml:"id_number"`
	Email           string `json:"email" xml:"email" yaml:"email"`
	Phone           string `json:"phone" xml:"phone" yaml:"phone"`
}
