package request

import (
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/model"
	"code.ixdev.cn/liush/xpb/pagination"
)

type UserList struct {
	Pagination     *pagination.Pagination `json:"pagination"`
	Account        string                 `json:"account"`
	CreatedAtStart string                 `json:"created_at_start"`
	CreatedAtEnd   string                 `json:"created_at_end"`
}

type UserDetail struct {
	Username string `json:"username" binding:"required"`
}

type UserCreate struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
	RealName string `json:"real_name"`
	IdNumber string `json:"id_number"`
	Email    string `json:"email"`
	Phone    string `json:"phone"`
	Enabled  bool   `json:"enabled"`
}

type UserUpdate struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password"`
	RealName string `json:"real_name"`
	IdNumber string `json:"id_number"`
	Email    string `json:"email"`
	Phone    string `json:"phone"`
	Enabled  *bool  `json:"enabled"`
}

type UserResetPassword struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

type UserStatusToggle struct {
	Username string           `json:"username" binding:"required"`
	Status   model.UserStatus `json:"status" binding:"required"`
}

type UserDelete struct {
	Username string `json:"username" binding:"required"`
}
