package sender

import "code.ixdev.cn/liush/xpb/pagination"

type FileListRequest struct {
	Pagination  *pagination.Pagination `json:"pagination" xml:"pagination" yaml:"pagination"`
	Filename    string                 `json:"filename" xml:"filename" yaml:"filename"`
	FileHash    string                 `json:"file_hash" xml:"file_hash" yaml:"file_hash"`
	Sorter      string                 `json:"sorter" xml:"sorter" yaml:"sorter"`
	SendAtStart string                 `json:"send_at_start" xml:"send_at_start" yaml:"send_at_start"`
	SendAtEnd   string                 `json:"send_at_end" xml:"send_at_end" yaml:"send_at_end"`
}

type PreviewUploadWatermarkInfoRequest struct {
}
