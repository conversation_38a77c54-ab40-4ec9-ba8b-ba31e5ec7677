package sftputil

import (
	"crypto/x509"
	"encoding/pem"
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"time"

	"github.com/pkg/sftp"
	"golang.org/x/crypto/ssh"
)

// AuthType 认证类型
type AuthType int

const (
	AuthPassword AuthType = iota
	AuthPrivateKey
)

// AuthConfig 认证配置
type AuthConfig struct {
	Type           AuthType      // 认证类型
	Host           string        // 服务器地址
	Port           int           // 服务器端口
	Username       string        // 用户名
	Password       string        // 密码（密码认证时使用）
	PrivateKeyPath string        // 私钥文件路径（公钥认证时使用）
	PrivateKey     []byte        // 私钥内容（公钥认证时使用，与PrivateKeyPath二选一）
	Timeout        time.Duration // 连接超时时间
}

// FileUploadConfig 文件上传配置
type FileUploadConfig struct {
	LocalFilePath  string // 本地文件路径
	RemoteFilePath string // 远程文件路径
}

// SftpClient SFTP客户端
type SftpClient struct {
	sshClient  *ssh.Client
	sftpClient *sftp.Client
}

// NewSftpClient 创建SFTP客户端
func NewSftpClient(config AuthConfig) (*SftpClient, error) {
	// 设置默认端口
	if config.Port == 0 {
		config.Port = 22
	}

	// 设置默认超时时间
	if config.Timeout == 0 {
		config.Timeout = 30 * time.Second
	}

	// 创建SSH客户端配置
	sshConfig := &ssh.ClientConfig{
		User:            config.Username,
		Timeout:         config.Timeout,
		HostKeyCallback: ssh.InsecureIgnoreHostKey(), // 注意：生产环境应该验证主机密钥
	}

	// 根据认证类型设置认证方法
	switch config.Type {
	case AuthPassword:
		sshConfig.Auth = []ssh.AuthMethod{
			ssh.Password(config.Password),
		}
	case AuthPrivateKey:
		var privateKey []byte
		var err error

		if len(config.PrivateKey) > 0 {
			privateKey = config.PrivateKey
		} else if config.PrivateKeyPath != "" {
			privateKey, err = os.ReadFile(config.PrivateKeyPath)
			if err != nil {
				return nil, fmt.Errorf("读取私钥文件失败: %v", err)
			}
		} else {
			return nil, fmt.Errorf("私钥认证需要提供私钥文件路径或私钥内容")
		}

		signer, err := parsePrivateKey(privateKey)
		if err != nil {
			return nil, fmt.Errorf("解析私钥失败: %v", err)
		}

		sshConfig.Auth = []ssh.AuthMethod{
			ssh.PublicKeys(signer),
		}
	default:
		return nil, fmt.Errorf("不支持的认证类型")
	}

	// 建立SSH连接
	addr := fmt.Sprintf("%s:%d", config.Host, config.Port)
	sshClient, err := ssh.Dial("tcp", addr, sshConfig)
	if err != nil {
		return nil, fmt.Errorf("SSH连接失败: %v", err)
	}

	// 建立SFTP连接
	sftpClient, err := sftp.NewClient(sshClient)
	if err != nil {
		sshClient.Close()
		return nil, fmt.Errorf("SFTP连接失败: %v", err)
	}

	return &SftpClient{
		sshClient:  sshClient,
		sftpClient: sftpClient,
	}, nil
}

// parsePrivateKey 解析私钥
func parsePrivateKey(privateKey []byte) (ssh.Signer, error) {
	// 尝试直接解析私钥
	signer, err := ssh.ParsePrivateKey(privateKey)
	if err == nil {
		return signer, nil
	}

	// 如果直接解析失败，尝试PEM解码
	block, _ := pem.Decode(privateKey)
	if block == nil {
		return nil, fmt.Errorf("无法解码PEM格式的私钥")
	}

	// 尝试解析不同类型的私钥
	switch block.Type {
	case "RSA PRIVATE KEY":
		key, err := x509.ParsePKCS1PrivateKey(block.Bytes)
		if err != nil {
			return nil, err
		}
		return ssh.NewSignerFromKey(key)
	case "PRIVATE KEY":
		key, err := x509.ParsePKCS8PrivateKey(block.Bytes)
		if err != nil {
			return nil, err
		}
		return ssh.NewSignerFromKey(key)
	default:
		return ssh.ParsePrivateKey(privateKey)
	}
}

// UploadFile 上传文件
func (c *SftpClient) UploadFile(config FileUploadConfig) error {
	// 检查本地文件是否存在
	localFile, err := os.Open(config.LocalFilePath)
	if err != nil {
		return fmt.Errorf("打开本地文件失败: %v", err)
	}
	defer localFile.Close()

	// 获取本地文件信息
	localFileInfo, err := localFile.Stat()
	if err != nil {
		return fmt.Errorf("获取本地文件信息失败: %v", err)
	}

	// 检查远程目录是否存在
	remotedir := filepath.Dir(config.RemoteFilePath)
	dirExists, err := c.checkServerDir(remotedir)
	if err != nil {
		return fmt.Errorf("检查接收目录失败: %v", err)
	}
	if !dirExists {
		return fmt.Errorf("接收目录不存在: %+v", remotedir)
	}

	log.Printf("开始上传文件: %s -> %s\n", config.LocalFilePath, config.RemoteFilePath)

	// 创建远程文件
	remoteFile, err := c.sftpClient.Create(config.RemoteFilePath)
	if err != nil {
		return fmt.Errorf("创建远程文件失败: %v", err)
	}
	defer remoteFile.Close()

	// 复制文件内容
	_, err = io.Copy(remoteFile, localFile)
	if err != nil {
		return fmt.Errorf("上传文件失败: %v", err)
	}

	// 设置远程文件权限
	err = c.sftpClient.Chmod(config.RemoteFilePath, localFileInfo.Mode())
	if err != nil {
		// 权限设置失败不是致命错误，只记录警告
		fmt.Printf("警告: 设置远程文件权限失败: %v\n", err)
	}

	return nil
}

// 检查远程目录是否存在
func (c *SftpClient) checkServerDir(dirPath string) (bool, error) {
	_, err := c.sftpClient.Stat(dirPath)
	if err != nil {
		log.Printf("sdk目录不存在(%+v): %+v", dirPath, err.Error())
		if os.IsNotExist(err) { // 目录不存在
			return false, nil
		}
		return false, err // 其他错误
	}

	return true, nil
}

// Close 关闭连接
func (c *SftpClient) Close() error {
	if c.sftpClient != nil {
		c.sftpClient.Close()
	}
	if c.sshClient != nil {
		c.sshClient.Close()
	}
	return nil
}

// UploadFileToSftp 对外封装的上传函数
func UploadFileToSftp(authConfig AuthConfig, fileConfig FileUploadConfig) error {
	// 创建SFTP客户端
	client, err := NewSftpClient(authConfig)
	if err != nil {
		return fmt.Errorf("创建SFTP客户端失败: %v", err)
	}
	defer client.Close()

	log.Println("开始上传文件")

	// 上传文件
	err = client.UploadFile(fileConfig)
	if err != nil {
		return fmt.Errorf("上传文件失败: %v", err)
	}

	return nil
}

// 示例使用
func main() {
	/*
		// 使用密码认证上传文件
		passwordAuth := AuthConfig{
			Type:     AuthPassword,
			Host:     "*************",
			Port:     52022,
			Username: "root",
			Password: "password", //修改为服务端密码
			Timeout:  30 * time.Second,
		}

		fileConfig := FileUploadConfig{
			LocalFilePath:  "./local_file.txt",
			RemoteFilePath: "/root/test/remote_file.txt",
		}

		err := UploadFileToSftp(passwordAuth, fileConfig)
		if err != nil {
			fmt.Printf("密码认证上传失败: %v\n", err)
		} else {
			fmt.Println("密码认证上传成功")
		}
	*/

	// 使用私钥认证上传文件（香港医管局可能使用密钥认证方式）
	keyAuth := AuthConfig{
		Type:           AuthPrivateKey,
		Host:           "*************",
		Port:           52022,
		Username:       "root",
		PrivateKeyPath: "./id_rsa", //修改为客户端私钥文件路径（参考readme文件生成公私钥对）
		Timeout:        30 * time.Second,
	}

	fileConfig := FileUploadConfig{
		LocalFilePath:  "./local_file.txt",
		RemoteFilePath: "/root/test/remote_file.txt",
	}

	err := UploadFileToSftp(keyAuth, fileConfig)
	if err != nil {
		fmt.Printf("私钥认证上传失败: %v\n", err)
	} else {
		fmt.Println("私钥认证上传成功")
	}

	// 批量上传示例
	//batchUploadExample()
}

// 批量上传示例
func batchUploadExample() {
	authConfig := AuthConfig{
		Type:     AuthPassword,
		Host:     "*************",
		Port:     22,
		Username: "user",
		Password: "password",
		Timeout:  30 * time.Second,
	}

	// 创建一个可重用的客户端
	client, err := NewSftpClient(authConfig)
	if err != nil {
		fmt.Printf("创建SFTP客户端失败: %v\n", err)
		return
	}
	defer client.Close()

	// 上传多个文件
	files := []FileUploadConfig{
		{
			LocalFilePath:  "./file1.txt",
			RemoteFilePath: "/remote/file1.txt",
		},
		{
			LocalFilePath:  "./file2.txt",
			RemoteFilePath: "/remote/file2.txt",
		},
	}

	for i, fileConfig := range files {
		err := client.UploadFile(fileConfig)
		if err != nil {
			fmt.Printf("上传文件 %d 失败: %v\n", i+1, err)
		} else {
			fmt.Printf("上传文件 %d 成功\n", i+1)
		}
	}
}
