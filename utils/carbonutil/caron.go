package carbonutil

import (
	"github.com/golang-module/carbon/v2"
	"sync"
)

var (
	CarbonInstance *carbon.Carbon
	one            sync.Once
)

func init() {
	one.Do(func() {
		carbon.SetDefault(carbon.Default{
			Layout:       carbon.DateTimeLayout,
			Timezone:     carbon.Local,
			WeekStartsAt: carbon.Sunday,
			Locale:       "zh-CN",
		})
		lang := carbon.NewLanguage()

		resources := map[string]string{
			"year":   "1年|%d年",
			"month":  "1个月|%d个月",
			"week":   "%d周",
			"day":    "%d天",
			"hour":   "%d小时",
			"minute": "%d分钟",
			"second": "%d秒",
			"now":    "刚刚",
			"ago":    "%s前",
		}
		lang.SetLocale("zh-CN").SetResources(resources)

		_carbonInstance := carbon.SetLanguage(lang)
		if _carbonInstance.Error != nil {
			panic(_carbonInstance.Error)
		}
		CarbonInstance = &_carbonInstance
	})
}
