package aosutil

import (
	"code.ixdev.cn/cnix/cbdv/hk-box-be/core"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/validator/common"
	"github.com/gin-gonic/gin"
	"github.com/h2non/filetype"
	"io"
	"log"
)

// Uploads 批量上传
func Uploads(ctx *gin.Context, path string) ([]string, error) {
	//logger, err := core.GetLogger()
	//if err != nil {
	//	log.Println(err)
	//	return nil, common.ErrInternalServerError
	//}

	//form, err := ctx.MultipartForm()
	//if err != nil {
	//	logger.Errorf("get multipart form err: %v", err)
	//	return nil, common.ErrUploadFailed
	//}
	//files, ok := form.File["file"]
	//if !ok {
	//	logger.Errorf("cannot find file field from form.Files")
	//	return nil, common.ErrUploadFailed
	//}
	//result := make([]string, 0)
	//for _, file := range files {
	//	err = func() error {
	//		src, err := file.Open()
	//		if err != nil {
	//			logger.Errorf("open file(filename=%s, size=%d) err: %v", file.Filename, file.Size, err)
	//			return common.ErrUploadFailed
	//		}
	//		defer src.Close()
	//		fileUrl, err := client.UploadFromStream(src, genObjectName(ctx, path, file.Filename))
	//		if err != nil {
	//			logger.Errorf("upload file(filename=%s, size=%d) to aos err: %v", file.Filename, file.Size, err)
	//			return common.ErrUploadFailed
	//		}
	//		result = append(result, fileUrl)
	//		return nil
	//	}()
	//	if err != nil {
	//		return nil, err
	//	}
	//}
	//return result, nil
	return nil, nil
}

type UploadOptions struct {
	AllowMimeTypes []string
	MaxSize        int
	MinSize        int
}

// Upload 单个上传
func Upload(ctx *gin.Context, path string, options *UploadOptions) (string, error) {
	logger, err := core.GetLogger()
	if err != nil {
		log.Println(err)
		return "", common.ErrUploadFailed
	}

	form, err := ctx.MultipartForm()
	if err != nil {
		logger.Errorf("get multipart form err: %v", err)
		return "", common.ErrUploadFailed
	}
	files, ok := form.File["file"]
	if !ok {
		logger.Errorf("cannot find file field from form.Files")
		return "", common.ErrUploadFailed
	}
	if len(files) < 1 {
		logger.Errorf("form.Files empty")
		return "", common.ErrUploadFailed
	}
	file := files[0]
	src, err := file.Open()
	if err != nil {
		logger.Errorf("open file(filename=%s, size=%d) err: %v", file.Filename, file.Size, err)
		return "", common.ErrUploadFailed
	}
	defer src.Close()
	contentBytes, err := io.ReadAll(src)
	if err != nil {
		logger.Error(err)
		return "", common.ErrInternalServerError
	}
	if options != nil {
		if len(options.AllowMimeTypes) > 0 {
			tryImgTypes, err := filetype.Get(contentBytes)
			if err != nil {
				logger.Error(err)
				return "", common.ErrInternalServerError
			}
			allowFileType := false
			for _, mimeType := range options.AllowMimeTypes {
				if mimeType == tryImgTypes.MIME.Value {
					allowFileType = true
					break
				}
			}
			if !allowFileType {
				return "", common.ErrFileTypeDisallowed
			}
		}
		if options.MinSize != 0 || options.MaxSize != 0 {
			tryImgSize := len(contentBytes)
			if options.MinSize != 0 {
				if tryImgSize < options.MinSize {
					return "", common.ErrFileTooSmall
				}
			}
			if options.MaxSize != 0 {
				if tryImgSize > options.MaxSize {
					return "", common.ErrFileTooBig
				}
			}
		}
	}

	//r, err := client.UploadFromStream(contentBytesReader, genObjectName(ctx, path, file.Filename))
	//if err != nil {
	//	logger.Errorf("upload file(filename=%s, size=%d) to aos err: %v", file.Filename, file.Size, err)
	//	return "", common.ErrUploadFailed
	//}
	//return r, nil
	return "", nil
}
