package pinyinutil

import (
	"github.com/mozillazg/go-pinyin"
	"regexp"
	"strings"
)

var (
	numP   = regexp.MustCompile(`\d+`)
	blankP = regexp.MustCompile(`\s+`)
)

func ConvertBatch(items []string) map[string]string {
	response := make(map[string]string)
	if items != nil {
		hansMap := make(map[string]string)
		for _, item := range items {
			hans := strings.TrimSpace(item)
			if len(hans) == 0 {
				continue
			}
			hansMap[hans] = item
		}

		pinyinArgs := pinyin.NewArgs()
		pinyinArgs.Fallback = func(r rune, a pinyin.Args) []string {
			return []string{string(r)}
		}
		for hans := range hansMap {
			response[hans] = handlePinyin(hans, pinyinArgs)
		}
	}
	return response
}

func Convert(item string) string {
	pinyinArgs := pinyin.NewArgs()
	pinyinArgs.Fallback = func(r rune, a pinyin.Args) []string {
		return []string{string(r)}
	}
	return handlePinyin(strings.TrimSpace(item), pinyinArgs)
}

func handlePinyin(hans string, pinyinArgs pinyin.Args) string {
	itemResult := strings.Join(pinyin.LazyPinyin(hans, pinyinArgs), " ")
	itemResult = regexp.MustCompile(`\s+`).ReplaceAllString(itemResult, " ")
	arr := strings.Split(itemResult, "")
	arrLen := len(arr)
	for i, s := range arr {
		if blankP.MatchString(s) {
			if i-1 >= 0 && i+1 < arrLen {
				if numP.MatchString(arr[i-1]) && numP.MatchString(arr[i+1]) {
					arr[i] = ""
					continue
				}
			}
		}
	}
	return strings.Join(arr, "")
}
