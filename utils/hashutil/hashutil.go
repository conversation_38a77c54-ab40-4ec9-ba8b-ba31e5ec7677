package hashutil

import (
	"crypto/sha256"
	"encoding/hex"
	"io"
	"os"
)

// GetFileHash returns the hash of the file at the given path.
func GetFileHash(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()
	hash := sha256.New()
	if _, err = io.Copy(hash, file); err != nil {
		return "", err
	}
	hv := hash.Sum(nil)
	return hex.EncodeToString(hv), nil
}
