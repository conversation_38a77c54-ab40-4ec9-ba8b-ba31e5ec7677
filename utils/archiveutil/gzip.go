package archiveutil

import (
	"compress/gzip"
	"io"
	"os"
)

func CreateGzipFromBuf(dst string, src io.Reader) error {
	buf, err := os.OpenFile(dst, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, os.ModePerm)
	if err != nil {
		return err
	}
	defer buf.Close()

	gz := gzip.NewWriter(buf)
	if _, err = io.Copy(gz, src); err != nil {
		return err
	}
	if err = gz.Close(); err != nil {
		return err
	}
	return nil
}
