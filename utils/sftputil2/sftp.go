package sftputil2

import (
	"crypto/x509"
	"encoding/pem"
	"fmt"
	"io"
	"log"
	"os"
	"strings"
	"time"

	"github.com/pkg/sftp"
	"golang.org/x/crypto/ssh"
)

// AuthType 认证类型
type AuthType int

const (
	AuthPassword AuthType = iota
	AuthPrivateKey
)

// AuthConfig 认证配置
type AuthConfig struct {
	Type             AuthType      // 认证类型
	Host             string        // 服务器地址
	Port             int           // 服务器端口
	Username         string        // 用户名
	Password         string        // 密码（密码认证时使用）
	PrivateKeyPath   string        // 私钥文件路径（公钥认证时使用）
	PrivateKey       []byte        // 私钥内容（公钥认证时使用，与PrivateKeyPath二选一）
	PrivateKeyPasswd string        // 私钥密码（私钥加密时使用）
	Timeout          time.Duration // 连接超时时间
}

// FileUploadConfig 文件上传配置
type FileUploadConfig struct {
	LocalFilePath  string // 本地文件路径
	RemoteFilePath string // 远程文件路径
}

// SftpClient SFTP客户端
type SftpClient struct {
	sshClient  *ssh.Client
	sftpClient *sftp.Client
}

// NewSftpClient 创建SFTP客户端
func NewSftpClient(config AuthConfig) (*SftpClient, error) {
	// 设置默认端口
	if config.Port == 0 {
		config.Port = 22
	}

	// 设置默认超时时间
	if config.Timeout == 0 {
		config.Timeout = 30 * time.Second
	}

	// 创建SSH客户端配置
	sshConfig := &ssh.ClientConfig{
		User:            config.Username,
		Timeout:         config.Timeout,
		HostKeyCallback: ssh.InsecureIgnoreHostKey(), // 注意：生产环境应该验证主机密钥
		Config: ssh.Config{
			KeyExchanges: []string{
				"diffie-hellman-group14-sha256",
				"diffie-hellman-group14-sha1",
				"diffie-hellman-group1-sha1",
				"ecdh-sha2-nistp256",
				"ecdh-sha2-nistp384",
				"ecdh-sha2-nistp521",
			},
			Ciphers: []string{
				"aes128-ctr",
				"aes192-ctr",
				"aes256-ctr",
				"<EMAIL>",
				"<EMAIL>",
				"<EMAIL>",
			},
			MACs: []string{
				"<EMAIL>",
				"hmac-sha2-256",
				"hmac-sha1",
				"hmac-sha1-96",
			},
			// 注意：Go SSH客户端会自动协商支持的算法，包括ssh-rsa
			// 这相当于 sftp 命令中的 -oHostKeyAlgorithms=+ssh-rsa -o PubkeyAcceptedKeyTypes=+ssh-rsa
		},
	}

	// 根据认证类型设置认证方法
	switch config.Type {
	case AuthPassword:
		sshConfig.Auth = []ssh.AuthMethod{
			ssh.Password(config.Password),
		}
	case AuthPrivateKey:
		var privateKey []byte
		var err error

		if len(config.PrivateKey) > 0 {
			privateKey = config.PrivateKey
		} else if config.PrivateKeyPath != "" {
			privateKey, err = os.ReadFile(config.PrivateKeyPath)
			if err != nil {
				return nil, fmt.Errorf("读取私钥文件失败: %v", err)
			}
		} else {
			return nil, fmt.Errorf("私钥认证需要提供私钥文件路径或私钥内容")
		}

		signer, err := parsePrivateKey(privateKey, config.PrivateKeyPasswd)
		if err != nil {
			return nil, fmt.Errorf("解析私钥失败: %v", err)
		}

		sshConfig.Auth = []ssh.AuthMethod{
			ssh.PublicKeys(signer),
		}
	default:
		return nil, fmt.Errorf("不支持的认证类型")
	}

	// 建立SSH连接
	addr := fmt.Sprintf("%s:%d", config.Host, config.Port)
	sshClient, err := ssh.Dial("tcp", addr, sshConfig)
	if err != nil {
		return nil, fmt.Errorf("SSH连接失败: %v", err)
	}

	// 建立SFTP连接
	sftpClient, err := sftp.NewClient(sshClient)
	if err != nil {
		sshClient.Close()
		return nil, fmt.Errorf("SFTP连接失败: %v", err)
	}

	return &SftpClient{
		sshClient:  sshClient,
		sftpClient: sftpClient,
	}, nil
}

// parsePrivateKey 解析私钥（支持加密私钥）
func parsePrivateKey(privateKey []byte, password string) (ssh.Signer, error) {
	// 尝试直接解析私钥
	signer, err := ssh.ParsePrivateKey(privateKey)
	if err == nil {
		return signer, nil
	}

	// 检查是否是加密的私钥
	block, _ := pem.Decode(privateKey)
	if block == nil {
		return nil, fmt.Errorf("无法解码PEM格式的私钥")
	}

	// 如果私钥被加密，需要密码
	if x509.IsEncryptedPEMBlock(block) {
		if password == "" {
			return nil, fmt.Errorf("私钥已加密，但未提供密码")
		}

		// 解密私钥
		decryptedBytes, err := x509.DecryptPEMBlock(block, []byte(password))
		if err != nil {
			return nil, fmt.Errorf("解密私钥失败: %v", err)
		}

		// 重新构造PEM块
		decryptedBlock := &pem.Block{
			Type:  block.Type,
			Bytes: decryptedBytes,
		}

		decryptedPEM := pem.EncodeToMemory(decryptedBlock)
		return ssh.ParsePrivateKey(decryptedPEM)
	}

	// 尝试解析不同类型的私钥
	switch block.Type {
	case "RSA PRIVATE KEY":
		key, err := x509.ParsePKCS1PrivateKey(block.Bytes)
		if err != nil {
			return nil, err
		}
		return ssh.NewSignerFromKey(key)
	case "PRIVATE KEY":
		key, err := x509.ParsePKCS8PrivateKey(block.Bytes)
		if err != nil {
			return nil, err
		}
		return ssh.NewSignerFromKey(key)
	case "EC PRIVATE KEY":
		key, err := x509.ParseECPrivateKey(block.Bytes)
		if err != nil {
			return nil, err
		}
		return ssh.NewSignerFromKey(key)
	case "OPENSSH PRIVATE KEY":
		return ssh.ParsePrivateKey(privateKey)
	default:
		return nil, fmt.Errorf("不支持的私钥类型: %s", block.Type)
	}
}

// UploadFile 上传文件
func (c *SftpClient) UploadFile(config FileUploadConfig) error {
	// 检查本地文件是否存在
	localFile, err := os.Open(config.LocalFilePath)
	if err != nil {
		return fmt.Errorf("打开本地文件失败: %v", err)
	}
	defer localFile.Close()

	// 获取本地文件信息
	localFileInfo, err := localFile.Stat()
	if err != nil {
		return fmt.Errorf("获取本地文件信息失败: %v", err)
	}

	// 使用Unix路径分隔符解析远程目录（修复Windows路径解析问题）
	remotePath := strings.ReplaceAll(config.RemoteFilePath, "\\", "/")
	lastSlash := strings.LastIndex(remotePath, "/")
	remotedir := ""
	if lastSlash >= 0 {
		remotedir = remotePath[:lastSlash]
	}

	log.Printf("解析的远程目录: '%s'", remotedir)

	// 检查远程目录是否存在（只有当目录路径不为空时才检查）
	if remotedir != "" && remotedir != "/" {
		dirExists, err := c.checkServerDir(remotedir)
		if err != nil {
			return fmt.Errorf("检查接收目录失败: %v", err)
		}
		if !dirExists {
			return fmt.Errorf("接收目录不存在: %s", remotedir)
		}
	}

	log.Printf("开始上传文件: %s -> %s\n", config.LocalFilePath, config.RemoteFilePath)

	// 检查远程文件是否已存在
	_, err = c.sftpClient.Stat(config.RemoteFilePath)
	fileExists := (err == nil)
	if fileExists {
		log.Printf("警告: 远程文件已存在，将尝试覆盖: %s", config.RemoteFilePath)
	}

	// 尝试创建远程文件，如果失败则尝试其他方法
	var remoteFile *sftp.File

	// 方法1：使用OpenFile（推荐）
	remoteFile, err = c.sftpClient.OpenFile(config.RemoteFilePath, os.O_WRONLY|os.O_CREATE|os.O_TRUNC)
	if err != nil {
		log.Printf("OpenFile方法失败: %v，尝试Create方法", err)

		// 方法2：如果文件存在，先删除再创建
		if fileExists {
			log.Printf("尝试删除已存在的文件: %s", config.RemoteFilePath)
			if removeErr := c.sftpClient.Remove(config.RemoteFilePath); removeErr != nil {
				log.Printf("删除文件失败: %v", removeErr)
			}
		}

		// 方法3：使用Create方法
		remoteFile, err = c.sftpClient.Create(config.RemoteFilePath)
		if err != nil {
			return fmt.Errorf("创建远程文件失败 (尝试了OpenFile和Create方法): %v", err)
		}
	}
	defer remoteFile.Close()

	// 复制文件内容
	copied, err := io.Copy(remoteFile, localFile)
	if err != nil {
		return fmt.Errorf("上传文件失败: %v", err)
	}

	log.Printf("文件上传成功，传输了 %d 字节", copied)

	// 设置远程文件权限（非关键操作）
	if err := c.sftpClient.Chmod(config.RemoteFilePath, localFileInfo.Mode()); err != nil {
		log.Printf("警告: 设置远程文件权限失败: %v", err)
	}

	return nil
}

// 检查远程目录是否存在
func (c *SftpClient) checkServerDir(dirPath string) (bool, error) {
	stat, err := c.sftpClient.Stat(dirPath)
	if err != nil {
		log.Printf("目录检查失败(%s): %v", dirPath, err)
		if os.IsNotExist(err) {
			return false, nil
		}
		return false, err
	}

	// 确保是目录而不是文件
	if !stat.IsDir() {
		return false, fmt.Errorf("路径存在但不是目录: %s", dirPath)
	}

	log.Printf("目录存在: %s", dirPath)
	return true, nil
}

// Close 关闭连接
func (c *SftpClient) Close() error {
	if c.sftpClient != nil {
		c.sftpClient.Close()
	}
	if c.sshClient != nil {
		c.sshClient.Close()
	}
	return nil
}

// UploadFileToSftp 对外封装的上传函数
func UploadFileToSftp(authConfig AuthConfig, fileConfig FileUploadConfig) error {
	// 创建SFTP客户端
	client, err := NewSftpClient(authConfig)
	if err != nil {
		return fmt.Errorf("创建SFTP客户端失败: %v", err)
	}
	defer client.Close()

	log.Println("开始上传文件")

	// 上传文件
	err = client.UploadFile(fileConfig)
	if err != nil {
		return fmt.Errorf("上传文件失败: %v", err)
	}

	log.Println("文件上传完成")
	return nil
}
