package authutil

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"code.ixdev.cn/cnix/cbdv/hk-box-be/core"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/global/metadata"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/validator/auth"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/validator/common"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/utils/tokenutil"
	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
)

func GetUsername(ctx *gin.Context) string {
	return ctx.GetString(metadata.UidKey)
}

func SetCookie(w http.ResponseWriter, name, value string, maxAge int) {
	cookieConf := core.GetConf().App.Cookie
	cookie := &http.Cookie{
		Name:     name,
		Value:    value,
		Path:     "/",
		MaxAge:   maxAge,
		Secure:   cookieConf.Secure,
		HttpOnly: cookieConf.HttpOnly,
		SameSite: http.SameSite(cookieConf.SameSite),
	}
	cookieStr := cookie.String()

	w.Header().Add("Set-Cookie", cookieStr)
}

func ParseAppToken(ctx context.Context, tokenVal string) (string, error) {
	redisClient, err := core.GetRedis()
	if err != nil {
		return "", common.ErrInternalServerError
	}
	token, err := jwt.ParseWithClaims(tokenVal, &jwt.RegisteredClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(metadata.AuthJwtKey), nil
	})
	if err != nil {
		return "", auth.ErrAuthTokenIllegal
	}
	if !token.Valid {
		return "", auth.ErrAuthTokenIllegal
	}
	claims, ok := token.Claims.(*jwt.RegisteredClaims)
	if !ok {
		return "", auth.ErrAuthTokenIllegal
	}

	val := redisClient.Get(ctx, claims.Subject).Val()
	uid := claims.ID
	if val == "" {
		membersKey := tokenutil.GetSessionMembersKey(uid)
		redisClient.SRem(ctx, membersKey, claims.Subject)
		return "", auth.ErrAuthTokenExpired
	}

	return uid, nil
}
func SetTokenTtl(ctx context.Context, tokenVal string, ttl time.Duration) error {
	redisClient, err := core.GetRedis()
	if err != nil {
		return common.ErrInternalServerError
	}
	token, err := jwt.ParseWithClaims(tokenVal, &jwt.RegisteredClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(metadata.AuthJwtKey), nil
	})
	if err != nil {
		return auth.ErrAuthTokenIllegal
	}
	if !token.Valid {
		return auth.ErrAuthTokenIllegal
	}
	claims, ok := token.Claims.(*jwt.RegisteredClaims)
	if !ok {
		return auth.ErrAuthTokenIllegal
	}

	return redisClient.Set(ctx, claims.Subject, tokenVal, ttl).Err()
}

func GetTokenTtl(ctx context.Context, tokenVal string) time.Duration {
	redisClient, err := core.GetRedis()
	if err != nil {
		return time.Duration(0)
	}
	token, err := jwt.ParseWithClaims(tokenVal, &jwt.RegisteredClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(metadata.AuthJwtKey), nil
	})
	if err != nil {
		return time.Duration(0)
	}
	if !token.Valid {
		return time.Duration(0)
	}
	claims, ok := token.Claims.(*jwt.RegisteredClaims)
	if !ok {
		return time.Duration(0)
	}

	return redisClient.TTL(ctx, claims.Subject).Val()
}

func parseToken(ctx *gin.Context) (string, error) {
	redisClient, err := core.GetRedis()
	if err != nil {
		return "", common.ErrInternalServerError
	}
	tokenVal := ctx.Request.Header.Get("X-Rise-Token")
	if tokenVal == "" {
		return "", auth.ErrAuthTokenRequired
	}
	token, err := jwt.ParseWithClaims(tokenVal, &jwt.RegisteredClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(metadata.AuthJwtKey), nil
	})
	if err != nil {
		return "", auth.ErrAuthTokenIllegal
	}
	if !token.Valid {
		return "", auth.ErrAuthTokenIllegal
	}
	claims, ok := token.Claims.(*jwt.RegisteredClaims)
	if !ok {
		return "", auth.ErrAuthTokenIllegal
	}

	val := redisClient.Get(ctx, claims.Subject).Val()
	if val == "" {
		membersKey := tokenutil.GetSessionMembersKey(claims.ID)
		redisClient.SRem(ctx, membersKey, claims.Subject)
		return "", auth.ErrAuthTokenExpired
	}
	ctx.Set(metadata.UidKey, claims.ID)
	return claims.ID, nil
}

func CheckToken(ctx *gin.Context) (string, error) {
	return parseToken(ctx)
}

func LogoutByUid(ctx context.Context, uid string) error {
	redisClient, err := core.GetRedis()
	if err != nil {
		return err
	}
	membersKey := tokenutil.GetSessionMembersKey(uid)
	if members := redisClient.SMembers(ctx, membersKey).Val(); members != nil && len(members) > 0 {
		newMembers := make([]interface{}, 0)
		for _, member := range members {
			newMembers = append(newMembers, member)
		}
		if err = redisClient.SRem(ctx, membersKey, newMembers...).Err(); err != nil {
			return err
		}
		if err = redisClient.Del(ctx, members...).Err(); err != nil {
			return err
		}
	}
	return nil
}
