package fileutil

import (
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
)

func CopyFile(src, dst string, perm os.FileMode) error {
	srcFile, err := os.Open(src)
	if err != nil {
		return fmt.Errorf("failed to open source file: %v", err)
	}
	defer srcFile.Close()

	if err = os.MkdirAll(filepath.Dir(dst), 0777); err != nil {
		return fmt.Errorf("failed to create directory: %v", err)
	}
	if err = os.Chmod(filepath.Dir(dst), perm); err != nil {
		log.Println(fmt.Sprintf("failed to change directory permission: %v", err))
	}

	fmt.Println("===================== CopyFile ===========================")
	fmt.Println(filepath.Dir(dst))
	fmt.Println(dst)
	fmt.Println("===================== CopyFile ===========================")

	dstFile, err := os.OpenFile(dst, os.O_RDWR|os.O_CREATE|os.O_TRUNC, 0777)
	if err != nil {
		return fmt.Errorf("failed to create target file: %v", err)
	}
	defer dstFile.Close()

	_, err = io.Copy(dstFile, srcFile)
	if err != nil {
		return fmt.Errorf("failed to copy file content: %v", err)
	}
	if err = os.Chmod(dst, perm); err != nil {
		log.Println(fmt.Sprintf("failed to change file permission: %v", err))
	}

	return nil
}
