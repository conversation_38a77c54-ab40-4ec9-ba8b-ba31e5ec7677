package ioutil

import (
	"errors"
	"io"
)

// errInvalidWrite means that a write returned an impossible count.
var errInvalidWrite = errors.New("invalid write result")

type AppendWriter func([]byte) (int, error)

func Copy(dst io.Writer, src io.Reader, buf []byte, appendWriters []AppendWriter) (written int64, err error) {
	//// If the reader has a WriteTo method, use it to do the copy.
	//// Avoids an allocation and a copy.
	//if wt, ok := src.(io.WriterTo); ok {
	//	return wt.WriteTo(dst)
	//}
	//// Similarly, if the writer has a ReadFrom method, use it to do the copy.
	//if rf, ok := dst.(io.ReaderFrom); ok {
	//	return rf.ReadFrom(src)
	//}
	if buf == nil {
		size := 32 * 1024
		if l, ok := src.(*io.LimitedReader); ok && int64(size) > l.N {
			if l.N < 1 {
				size = 1
			} else {
				size = int(l.N)
			}
		}
		buf = make([]byte, size)
	}
	for {
		nr, er := src.Read(buf)
		if nr > 0 {
			bufT := buf[0:nr]
			nw, ew := dst.Write(bufT)
			if nw < 0 || nr < nw {
				nw = 0
				if ew == nil {
					ew = errInvalidWrite
				}
			}
			written += int64(nw)
			if ew != nil {
				err = ew
				break
			}
			if nr != nw {
				err = io.ErrShortWrite
				break
			}
			if appendWriters != nil {
				for _, appendWriter := range appendWriters {
					ar, ae := appendWriter(bufT)
					if ae != nil {
						err = ae
						break
					}
					if ar != nw {
						err = io.ErrShortWrite
						break
					}
				}
			}
		}
		if er != nil {
			if er != io.EOF {
				err = er
			}
			break
		}
	}
	return written, err
}
