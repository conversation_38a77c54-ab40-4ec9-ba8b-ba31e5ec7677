package special

import (
	"code.ixdev.cn/cnix/cbdv/hk-box-be/core"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/model"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/utils/fileutil"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/utils/fiscoutil"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/utils/watermarkutil"
	"encoding/json"
	"fmt"
	"github.com/gookit/goutil/strutil"
	"github.com/yeka/zip"
	"io"
	"os"
	"path"
	"path/filepath"
)

func HandleHL7Zip(packageDst string, tarTempDir string, packageM *model.RevPackage, watermarkData WatermarkData, src, unzipBasePath string, enableWatermark bool) error {
	var err error
	logger, err := core.GetLogger()
	if err != nil {
		return err
	}

	certFilepath := filepath.Join(tarTempDir, packageM.Cert.Filepath, packageM.Cert.Filename)
	logger.Debugf("certFilePath: %s\n", certFilepath)
	certFileBytes, err := os.ReadFile(certFilepath)
	if err != nil {
		logger.Errorln(err)
		return err
	}
	certFileData := new(model.RevPackageCertData)
	if err = json.Unmarshal(certFileBytes, certFileData); err != nil {
		logger.Errorln(err)
		return err
	}

	blChain := fiscoutil.NewFiscoUtil()
	srcObj, err := os.Open(src)
	if err != nil {
		return fmt.Errorf("open [%s] error: %v", src, err)
	}
	srcFileInfo, err := srcObj.Stat()
	if err != nil {
		return fmt.Errorf("stat [%s] error: %v", src, err)
	}

	r, err := zip.NewReader(srcObj, srcFileInfo.Size())
	if err != nil {
		return fmt.Errorf("new zip reader error: %v", err)
	}

	unzippedDir := filepath.Join(unzipBasePath, filepath.Base(src)[:len(filepath.Base(src))-len(filepath.Ext(src))]+"_"+strutil.RandomCharsV3(8))
	defer os.RemoveAll(unzippedDir)

	needWatermarkFileRelPaths := make([]string, 0)
	encryptFiles := make(map[string]struct{})
	for _, ff := range r.File {
		targetPath := path.Join(unzippedDir, ff.Name)
		fInfo := ff.FileInfo()
		if !fInfo.IsDir() {
			if err := os.MkdirAll(filepath.Dir(targetPath), os.ModePerm); err != nil {
				return fmt.Errorf("mkdir [%s] error: %v", filepath.Dir(targetPath), err)
			}
			needWatermarkFileRelPaths = append(needWatermarkFileRelPaths, ff.Name)
			encryptFiles[ff.Name] = struct{}{}
		}
		if ff.IsEncrypted() {
			ff.SetPassword(certFileData.ZipMima)
		}

		rf, err := ff.Open()
		if err != nil {
			return fmt.Errorf("open zip inner file [%s] error: %v", ff.Name, err)
		}
		out, err := os.Create(targetPath)
		if err != nil {
			return fmt.Errorf("create [%s] error: %v", targetPath, err)
		}
		if _, err = io.Copy(out, rf); err != nil {
			return fmt.Errorf("copy [%s] error: %v", targetPath, err)
		}
		if err = rf.Close(); err != nil {
			return err
		}
		if err = out.Close(); err != nil {
			return err
		}
	}

	srcObj.Close()

	srcObj, err = os.Create(src)
	if err != nil {
		return fmt.Errorf("create [%s] error: %v", src, err)
	}
	defer srcObj.Close()

	w := zip.NewWriter(srcObj)
	for _, needWatermarkFileRelPath := range needWatermarkFileRelPaths {
		needWatermarkFilePathAbs := path.Join(unzippedDir, needWatermarkFileRelPath)
		watermarkedFilepath := ""
		if enableWatermark {
			_, watermarkedFilepath, err = doWatermark(needWatermarkFilePathAbs, watermarkData.PersonId, watermarkData.Operator, watermarkData.Institution)
			if err != nil {
				return fmt.Errorf("do watermark on file [%s] error: %v", needWatermarkFilePathAbs, err)
			}
		}
		_, err = blChain.IsDcmFileVerified(packageDst, needWatermarkFilePathAbs, certFileData, string(certFileBytes), packageM.SendCode, watermarkedFilepath)
		if err != nil {
			logger.Errorln(fmt.Errorf("verify file [%s] error: %v", needWatermarkFilePathAbs, err))
		}

		wwBytes := []byte{}

		if enableWatermark {
			watermarkedFileBytes, err := os.ReadFile(watermarkedFilepath)
			if err != nil {
				return fmt.Errorf("read file [%s] error: %v", watermarkedFilepath, err)
			}
			wwBytes = watermarkedFileBytes
			_ = os.Remove(watermarkedFilepath)
		} else {
			watermarkedFileBytes, err := os.ReadFile(needWatermarkFilePathAbs)
			if err != nil {
				return fmt.Errorf("read file [%s] error: %v", needWatermarkFilePathAbs, err)
			}
			wwBytes = watermarkedFileBytes
		}

		fh := zip.FileHeader{
			Name:   needWatermarkFileRelPath,
			Method: zip.Deflate,
		}
		if _, ok := encryptFiles[needWatermarkFileRelPath]; ok {
			fh.SetPassword(certFileData.ZipMima)
			fh.SetEncryptionMethod(zip.StandardEncryption)
		}
		ww, err := w.CreateHeader(&fh)
		if err != nil {
			return fmt.Errorf("create zip writer error: %v", err)
		}
		if _, err = ww.Write(wwBytes); err != nil {
			return fmt.Errorf("write zip writer error: %v", err)
		}

	}
	if err := w.Close(); err != nil {
		return fmt.Errorf("close zip writer error: %v", err)
	}

	return nil
}

func doWatermark(srcPath string, personId, operator, institution string) (map[string]any, string, error) {
	watermarkCfg := core.GetConf().App.Rev.Fisco.WatermarkApi
	watermarkFilepath := filepath.Join(watermarkCfg.TempDir, srcPath)
	if err := fileutil.CopyFile(srcPath, watermarkFilepath, 0775); err != nil {
		return nil, "", fmt.Errorf("copy file from [%s] to [%s] error: %v", srcPath, watermarkFilepath, err)
	}
	//defer os.RemoveAll(filepath.Dir(watermarkFilepath))

	_wmData, err := watermarkutil.DoWatermark(watermarkCfg.Addr, watermarkFilepath, personId, operator, institution)
	if err != nil {
		return nil, "", fmt.Errorf("do watermark on file [%s] error: %v", watermarkFilepath, err)
	}
	//if err := fileutil.CopyFile(watermarkFilepath, srcPath, 0775); err != nil {
	//	return nil,"", fmt.Errorf("copy file from [%s] to [%s] error: %v", watermarkFilepath, srcPath, err)
	//}
	return _wmData, watermarkFilepath, nil
}

func HandleDcmFile(packageDst string, tarTempDir string, packageM *model.RevPackage, watermarkData WatermarkData, src string) error {
	var err error
	logger, err := core.GetLogger()
	if err != nil {
		return err
	}
	certFilepath := filepath.Join(tarTempDir, packageM.Cert.Filepath, packageM.Cert.Filename)
	logger.Debugf("certFilePath: %s\n", certFilepath)
	certFileBytes, err := os.ReadFile(certFilepath)
	if err != nil {
		logger.Errorln(err)
		return err
	}
	certFileData := new(model.RevPackageCertData)
	if err = json.Unmarshal(certFileBytes, certFileData); err != nil {
		logger.Errorln(err)
		return err
	}

	blChain := fiscoutil.NewFiscoUtil()

	_, watermarkedFilepath, err := doWatermark(src, watermarkData.PersonId, watermarkData.Operator, watermarkData.Institution)
	if err != nil {
		return fmt.Errorf("do watermark on file [%s] error: %v", src, err)
	}

	_, err = blChain.IsDcmFileVerified(packageDst, src, certFileData, string(certFileBytes), packageM.SendCode, watermarkedFilepath)
	if err != nil {
		logger.Errorln(fmt.Errorf("verify file [%s] error: %v", src, err))
	}

	if err := fileutil.CopyFile(watermarkedFilepath, src, 0775); err != nil {
		return fmt.Errorf("copy file from [%s] to [%s] error: %v", watermarkedFilepath, src, err)
	}
	return nil
}
