package special

import (
	"code.ixdev.cn/cnix/cbdv/hk-box-be/core"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/model"
	"fmt"
	"gorm.io/gorm"
	"path/filepath"
)

func GetUnZipDir(filename string) string {
	basename := filepath.Base(filename)
	return "resources/hk-box/rev/temp/hl7zip/" + basename[:len(basename)-len(".zip")]
}

type WatermarkData struct {
	PersonId    string
	Operator    string
	Institution string
}

func GetWatermarkData(orm *gorm.DB, packageId string, filename string) (*WatermarkData, error) {
	var err error
	logger, err := core.GetLogger()
	if err != nil {
		return nil, err
	}
	packageM := new(model.RevPackage)
	if err = orm.Unscoped().Where("id=?", packageId).Limit(1).Find(packageM).Error; err != nil {
		logger.Errorln(err)
		return nil, err
	}
	if packageM.Id == "" {
		return nil, fmt.Errorf("包%s不存在", packageId)
	}

	adminUser := new(model.User)
	if err = orm.Where("username=?", model.AdminUsername).Where("is_admin", true).Limit(1).Find(adminUser).Error; err != nil {
		logger.Errorln(err)
		return nil, err
	}
	if adminUser.Username == "" {
		return nil, fmt.Errorf("adminUser not found")
	}

	personId := ""
	if len(packageM.Cert.Data.FileDetail) > 0 {
		for _, fileDetail := range packageM.Cert.Data.FileDetail {
			if fileDetail.FileName == filename {
				personId = fileDetail.PersonId
				break
			}
		}
	}

	return &WatermarkData{
		PersonId:    personId,
		Operator:    "",
		Institution: adminUser.Institution,
	}, nil
}
