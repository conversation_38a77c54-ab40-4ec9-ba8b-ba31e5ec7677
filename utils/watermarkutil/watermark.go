package watermarkutil

import (
	"encoding/json"
	"fmt"
	"github.com/go-resty/resty/v2"
	"log"
	"net/http"
	"path/filepath"
	"strings"
	"time"
)

// 增加水印接口请求参数
type WatermarkInfo struct {
	WatermarkFile string    `json:"watermark_file,omitempty" form:"watermark_file"` //待增加水印的文件完整路径
	FileType      string    `json:"file_type,omitempty" form:"file_type"`           //文件类型
	Timestamp     time.Time `json:"timestamp,omitempty" form:"timestamp"`
	PersonID      string    `json:"person_id,omitempty" form:"person_id"`       //患者ID
	Operator      string    `json:"operator,omitempty" form:"operator"`         //操作者
	OperatorOrg   string    `json:"operator_org,omitempty" form:"operator_org"` //操作企业
}

func DoWatermark(watermarkApi string, watermarkFile string, uid string, operator string, operatorOrg string) (map[string]any, error) {
	//beforeWatermarkFile := "test.pdf" //水印前的文件

	/*印前后的文件可以通过目录区分，从而可以用同名文件保存水印后的文件*/
	//watermarkDir := "./watermark" //水印后的目录
	//watermarkFileName := beforeWatermarkFile
	//watermarkFile := filepath.Join(watermarkDir, watermarkFileName) //待增加水印的文件完整路径

	//将水印前端文件复制一份到水印后的目录
	//if err := CopyFile(beforeWatermarkFile, watermarkFile); err != nil {
	//	log.Printf("复制文件失败: %v", err)
	//	return
	//}

	/*----开始调用增加水印接口----*/

	fileExt := filepath.Ext(watermarkFile)
	var watermarkInfoList []WatermarkInfo //接口支持对多个文件增加水印
	// 示例水印数据结构体
	watermarkInfo := WatermarkInfo{
		WatermarkFile: watermarkFile,                                   //待增加水印的文件完整路径
		FileType:      strings.ToLower(strings.TrimLeft(fileExt, ".")), //接口暂时只支持pdf类型
		Timestamp:     time.Now(),
		PersonID:      uid,
		Operator:      operator,
		OperatorOrg:   operatorOrg,
	}

	watermarkInfoList = append(watermarkInfoList, watermarkInfo) //接口支持对多个文件增加水印

	//apiAddr := "127.0.0.1:30002"
	//watermarkApi := fmt.Sprintf("http://%s/api/v1/add_watermark", apiAddr)
	if err := HttpPost(watermarkApi, watermarkInfoList, nil); err != nil {
		return nil, fmt.Errorf("call watermark api failed: %v", err)
	}

	wmData := map[string]any{
		"timestamp":    watermarkInfo.Timestamp.Format(time.DateTime),
		"operator":     watermarkInfo.Operator,
		"operator_org": watermarkInfo.OperatorOrg,
	}
	if watermarkInfo.PersonID != "" {
		wmData["person_id"] = watermarkInfo.PersonID
	}

	return wmData, nil
}

type Response struct {
	Code int         `json:"code"`
	Data interface{} `json:"data"`
	Msg  string      `json:"msg"`
}

const (
	NOAUTH  = 8
	ERROR   = 7
	SUCCESS = 0
)

func HttpPost(url string, body interface{}, resData interface{}) error {
	log.Printf("HttpPost开始: url: %+v, body: %+v\n", url, body)

	restyClient := resty.New()
	restyClient.SetTransport(&http.Transport{
		Proxy: nil, // 禁用代理
	})

	//resp, err := resty.New().R().SetHeader("Content-Type", "application/json").
	resp, err := restyClient.R().SetHeader("Content-Type", "application/json").
		SetBody(body).Post(url)
	if err != nil {
		log.Printf("HttpPost请求出错: err: %+v\n", err)
		return err
	}
	log.Printf("HttpPost请求成功, resp: %+v\n", resp)

	var response Response
	err = json.Unmarshal(resp.Body(), &response)
	if err != nil {
		return err
	}

	if response.Code != SUCCESS {
		return fmt.Errorf("%s", response.Msg)
	}

	if response.Data != nil {
		dataBytes, err := json.Marshal(response.Data)
		if err != nil {
			return err
		}
		err = json.Unmarshal(dataBytes, resData)
		if err != nil {
			return err
		}
	}

	return nil

}
