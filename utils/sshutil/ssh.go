package sshutil

import (
	"fmt"
	"github.com/melbahja/goph"
	"os"
)

type UploadPubKeyOpts struct {
	Host       string
	Port       int
	User       string
	Password   string
	PubKeyPath string
}

func UploadPubKey(opt UploadPubKeyOpts) error {
	client, err := goph.New(opt.User, fmt.Sprintf("%s:%d", opt.Host, opt.Port), goph.Password(opt.Password))
	if err != nil {
		return fmt.Errorf("SSH 连接失败: %v", err)
	}

	defer client.Close()

	// 读取本地公钥文件
	pubKeyData, err := os.ReadFile(opt.PubKeyPath)
	if err != nil {
		return fmt.Errorf("读取公钥文件失败: %v", err)
	}

	// 创建 .ssh 目录（如果不存在）
	if _, err = client.Run("mkdir -p ~/.ssh"); err != nil {
		return fmt.Errorf("创建 .ssh 失败: %v", err)
	}

	// 设置权限
	if _, err = client.Run("chmod 700 ~/.ssh"); err != nil {
		return fmt.Errorf("设置 .ssh 目录权限失败: %v", err)
	}

	// 写入 authorized_keys 文件（追加方式）
	addKeyCmd := fmt.Sprintf("echo '%s' >> ~/.ssh/authorized_keys", pubKeyData)
	if _, err = client.Run(addKeyCmd); err != nil {
		return fmt.Errorf("写入 authorized_keys 文件失败: %v", err)
	}

	// 设置 authorized_keys 权限
	if _, err = client.Run("chmod 600 ~/.ssh/authorized_keys"); err != nil {
		return fmt.Errorf("设置 authorized_keys 权限失败: %v", err)
	}

	return nil
}
