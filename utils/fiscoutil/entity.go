package fiscoutil

type SubFileDetail struct {
	FileHash       string `json:"file_hash"`
	FileHashVerify bool   `json:"file_hash_verify"`
}

type Result struct {
	PackageHash   string          `json:"package_hash"`
	SubFileCount  int             `json:"subfile_count"`
	DecryptStatus bool            `json:"decrypt_status"`
	SubFileDetail []SubFileDetail `json:"sub_file_detail"`
}

type FilesCertDataList struct {
	Result Result `json:"result"`
}
