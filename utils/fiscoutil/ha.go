// 恒安相关接口

package fiscoutil

type HaSenderRequest struct {
	CompanyId     string `json:"company_id,omitempty"`
	SenderCompany string `json:"sender_company,omitempty"`
}

type HaSenderResponseData struct {
	CompanyId     string `json:"company_id"`
	SenderCompany string `json:"sender_company"`
}

type HaSenderResponse struct {
	Code int                     `json:"code"`
	Data []*HaSenderResponseData `json:"data"`
	Msg  string                  `json:"msg"`
}

//// GetSenderNameBySenderId 通过 senderId 获取 senderName
//func GetSenderNameBySenderId(senderId string) (string, error) {
//	result, err := WildcardGetSender("", senderId)
//	if err != nil {
//		return "", err
//	}
//	if result != nil {
//		for _, data := range result {
//			if data.CompanyId == senderId {
//				return data.SenderCompany, nil
//			}
//		}
//	}
//	return "", nil
//}

//func GetSenderIdBySenderName(senderName string) (string, error) {
//	result, err := WildcardGetSender(senderName, "")
//	if err != nil {
//		return "", err
//	}
//	if result != nil {
//		for companyId, data := range result {
//			if data.SenderCompany == senderName {
//				return companyId, nil
//			}
//		}
//	}
//	return "", nil
//}
//
//func WildcardGetSenderIdListBySenderName(senderName string) ([]string, error) {
//	deepData, err := WildcardGetSender(senderName, "")
//	if err != nil {
//		return nil, err
//	}
//	result := make([]string, 0)
//	if deepData != nil {
//		for companyId := range deepData {
//			result = append(result, companyId)
//		}
//	}
//	return result, nil
//}

//func WildcardGetSender(senderName string, senderId string) (map[string]*HaSenderResponseData, error) {
//	reqData := HaSenderRequest{
//		SenderCompany: senderName,
//		CompanyId:     senderId,
//	}
//	body, err := json.Marshal(reqData)
//	if err != nil {
//		return nil, err
//	}
//	respBody, err := retry.DoWithData(func() ([]byte, error) {
//		resp, err := http.Post(core.GetConf().App.Rev.Fisco.SenderNameProviderUrl, "application/json", bytes.NewReader(body))
//		if err != nil {
//			return nil, err
//		}
//		defer resp.Body.Close()
//		return io.ReadAll(resp.Body)
//	}, retry.Attempts(3))
//	if err != nil {
//		return nil, err
//	}
//
//	respData := new(HaSenderResponse)
//	if err = json.Unmarshal(respBody, respData); err != nil {
//		return nil, err
//	}
//	if respData.Code != 0 && respData.Code != 200 {
//		return nil, fmt.Errorf("wildcardGetSender server error:%s[%d]", respData.Msg, respData.Code)
//	}
//	result := make(map[string]*HaSenderResponseData)
//	if respData.Data != nil {
//		for _, v := range respData.Data {
//			result[v.CompanyId] = v
//		}
//	}
//	return result, nil
//}
