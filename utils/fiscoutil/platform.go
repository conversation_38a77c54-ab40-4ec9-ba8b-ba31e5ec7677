package fiscoutil

// 旧版本迁移过来的方法，已不需要
//func SendPlatformCheckFile(hash, tarName string) error {
//	var err error
//	logger, err := core.GetLogger()
//	if err != nil {
//		log.Fatalln(err)
//		return err
//	}
//	hash = fmt.Sprintf("%s-%s.json", tarName, hash)
//	filePath := filepath.Join(core.GetConf().App.Fisco.Tars.CheckFilePath, hash)
//
//	// 读取 JSON 文件
//	certData, err := os.ReadFile(filePath)
//	if err != nil {
//		logger.Errorln(err)
//		return err
//	}
//
//	// 解析 JSON 内容
//	var certInfo FilesCertDataList
//	if err := json.Unmarshal(certData, &certInfo); err != nil {
//		logger.Errorln(err)
//		return err
//	}
//
//	for i, _ := range certInfo.Result.SubFileDetail {
//		certInfo.Result.SubFileDetail[i].FileHashVerify = true
//	}
//	jsonData, err := json.Marshal(certInfo)
//	if err != nil {
//		logger.Errorln(err)
//		return err
//	}
//
//	// 发送 POST 请求
//	resp, err := InsecureHttpClient().Post(core.GetConf().App.Fisco.SdkPlatformUrl, "application/json", bytes.NewBuffer(jsonData))
//	if err != nil {
//		logger.Errorln(err)
//		return err
//	}
//	defer resp.Body.Close()
//
//	return nil
//}
