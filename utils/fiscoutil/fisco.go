package fiscoutil

import (
	"bytes"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/core"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/model"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/utils/hashutil"
	"code.ixdev.cn/liush/xpb/errors"
	"encoding/json"
	"fmt"
	"github.com/avast/retry-go/v4"
	"github.com/gookit/goutil/jsonutil"
	"github.com/gookit/goutil/netutil"
	"go.uber.org/zap"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"
)

//type CheckFileSignInfo struct {
//	Logs      []string `json:"logs"`
//	Revoked   bool     `json:"revoked"`
//	Signature string   `json:"signature"`
//	Timestamp string   `json:"timestamp"`
//}
//
//type CheckFileRespBody struct {
//	CredentialHash string                       `json:"credentialHash"`
//	SignInfo       map[string]CheckFileSignInfo `json:"signInfo"`
//}
//
//type CheckFileResponse struct {
//	RespBody     CheckFileRespBody `json:"respBody"`
//	ErrorCode    int               `json:"errorCode"`
//	ErrorMessage string            `json:"errorMessage"`
//}
//
//type EvidenceLog struct {
//	FileHashVerify bool `json:"file_hash_verify"`
//}
//
//type AddEvidenceRequestData struct {
//	Hash     string `json:"hash"`
//	Log      string `json:"log"`
//	Password string `json:"password"`
//	WeId     string `json:"weId"`
//}
//
//func AddEvidence(hash string) (*CheckFileResponse, error) {
//	var err error
//	logger, err := core.GetLogger()
//	if err != nil {
//		log.Fatalln(err)
//		return nil, err
//	}
//	logData := &EvidenceLog{FileHashVerify: true}
//	// 将 EvidenceLog 实例转换为 JSON 字符串
//	logStr, err := json.Marshal(logData)
//	reqData := AddEvidenceRequestData{
//		Hash:     hash,
//		Log:      string(logStr),
//		Password: core.GetConf().App.Rev.Fisco.SignServerPassword,
//		WeId:     core.GetConf().App.Rev.Fisco.SignServerWeid,
//	}
//	jsonData, err := json.Marshal(reqData)
//	if err != nil {
//		logger.Errorln("Error marshaling JSON:", err)
//		return nil, err
//	}
//	resp, err := http.Post(core.GetConf().App.Rev.Fisco.SignServerUrl+"/add/log/hash-evidence", "application/json", bytes.NewBuffer(jsonData))
//	if err != nil {
//		logger.Errorln("Error sending request:", err)
//		return nil, err
//	}
//	defer resp.Body.Close()
//	body, err := io.ReadAll(resp.Body)
//	if err != nil {
//		fmt.Println("Error reading response body:", err)
//		return nil, err
//	}
//	response := new(CheckFileResponse)
//	err = json.Unmarshal(body, response)
//	if err != nil {
//		fmt.Println("Error unmarshaling response body:", err)
//		return nil, err
//	}
//	return response, nil
//}
//
//type VerifyHashRequestData struct {
//	Hash     string `json:"hash"`
//	Password string `json:"password"`
//	WeId     string `json:"weId"`
//}
//
//func VerifyHash(hash string) (*CheckFileResponse, error) {
//	var err error
//	logger, err := core.GetLogger()
//	if err != nil {
//		log.Fatalln(err)
//		return nil, err
//	}
//	cfg := core.GetConf().App.Rev.Fisco
//	reqData := VerifyHashRequestData{
//		Hash:     hash,
//		Password: cfg.SignServerPassword,
//		WeId:     cfg.SignServerWeid,
//	}
//	jsonData, err := json.Marshal(reqData)
//	if err != nil {
//		logger.Errorln("Error marshaling JSON:", err)
//		return nil, err
//	}
//	resp, err := http.Post(cfg.SignServerUrl+"/verify-hash", "application/json", bytes.NewBuffer(jsonData))
//	if err != nil {
//		logger.Errorln("Error sending request:", err)
//		return nil, err
//	}
//	defer resp.Body.Close()
//	body, err := io.ReadAll(resp.Body)
//	if err != nil {
//		fmt.Println("Error reading response body:", err)
//		return nil, err
//	}
//	response := new(CheckFileResponse)
//	err = json.Unmarshal(body, response)
//	if err != nil {
//		fmt.Println("Error unmarshaling response body:", err)
//		return nil, err
//	}
//	return response, nil
//}

type FiscoUtil struct {
	signServerUrl string
	weId          string
	password      string
	logger        *zap.SugaredLogger
}

func NewFisco(signServerUrl, weId, password string, logger *zap.SugaredLogger) *FiscoUtil {
	return &FiscoUtil{
		signServerUrl: strings.TrimRight(signServerUrl, "/"),
		weId:          weId,
		password:      password,
		logger:        logger,
	}
}

func NewFiscoUtil() *FiscoUtil {
	cfg := core.GetConf().App.Rev.Fisco
	logger, _ := core.GetLogger()
	return NewFisco(cfg.SignServerUrl, cfg.SignServerWeid, cfg.SignServerPassword, logger)
}

type FiscoRecord struct {
	Log string `json:"log"`
}

type FiscoResponse struct {
	ErrorCode    int    `json:"errorCode"`
	ErrorMessage string `json:"errorMessage"`
	RespBody     any    `json:"respBody"`
}

func (f *FiscoUtil) doPost(basePath string, postData any) (any, error) {
	var err error
	postDataBytes, err := json.Marshal(postData)
	if err != nil {
		return nil, fmt.Errorf("encode post data err: %v", err)
	}
	targetUri := f.signServerUrl + "/" + strings.Trim(basePath, "/")
	f.logger.Debugw("doPost send", "uri", targetUri, "postData", string(postDataBytes))
	respBody, err := retry.DoWithData(func() ([]byte, error) {
		resp, err := http.Post(targetUri, "application/json", bytes.NewReader(postDataBytes))
		if err != nil {
			return nil, err
		}
		defer resp.Body.Close()
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return nil, err
		}
		return body, nil
	})
	if err != nil {
		return nil, fmt.Errorf("post request err: %v", err)
	}
	f.logger.Debugw("doPost receive", "respBody", string(respBody))

	respPass := new(FiscoResponse)
	if err := jsonutil.Decode(respBody, respPass); err != nil {
		return nil, fmt.Errorf("decode response body err: %v", err)
	}
	if respPass.ErrorMessage != "success" {
		return nil, errors.New("", respPass.ErrorMessage, int32(respPass.ErrorCode))
	}

	return respPass.RespBody, nil
}

// CreateRecord 通用上链记录创建
func (f *FiscoUtil) CreateRecord(basePath, recordKeyName, recordKeyValue string, record *FiscoRecord) (any, error) {
	postData := map[string]any{
		"weId":        f.weId,
		"password":    f.password,
		recordKeyName: recordKeyValue,
		"log":         record.Log,
	}
	return f.doPost(basePath, postData)
}

// AppendLogToRecord 通用上链记录追加
func (f *FiscoUtil) AppendLogToRecord(basePath, recordKeyName, recordKeyValue string, record *FiscoRecord) (any, error) {
	return f.CreateRecord(basePath, recordKeyName, recordKeyValue, record)
}

const (
	ActionDirectionFileHashSend = iota
	ActionDirectionFileHashReceived
	ActionDirectionZipHashSend
	ActionDirectionZipHashReceived
)

// RecordLogTypeFileData 文件类型日志
type RecordLogTypeFileData struct {
	BatchCode                string `json:"batchCode"`
	ActionDirection          int    `json:"actionDirection"`
	FileHash                 string `json:"fileHash"`
	FileWatermarkHash        string `json:"fileWatermarkHash"`
	RevFileWatermarkHash     string `json:"revFileWatermarkHash"`
	FileZipHash              string `json:"fileZipHash"`
	FileTarHash              string `json:"fileTarHash"`
	FileLength               int64  `json:"fileLength"`
	SenderIdentityCode       string `json:"senderIdentityCode"`
	DataProviderIdentityCode string `json:"dataProviderIdentityCode"`
	DataSenderIpv4           string `json:"dataSenderIpv4"`
	DataSenderIpv6           string `json:"dataSenderIpv6"`
	SendMethod               int    `json:"sendMethod"`
	DataReceiverIpv4         string `json:"dataReceiverIpv4"`
	DataReceiverIpv6         string `json:"dataReceiverIpv6"`
	DataReceiverDomain       string `json:"dataReceiverDomain"`
	DataUserIdentityCode     string `json:"dataUserIdentityCode"`
	Timestamp                int64  `json:"timestamp"`
	ReservedField            string `json:"reservedField"`
}

// RecordLogTypeDecryptData 解密类型日志
type RecordLogTypeDecryptData struct {
	FileHash    string `json:"fileHash"`
	DecryptedAt int64  `json:"decryptedAt"`
	LogType     int    `json:"logType"`
}

type GetRecordResultSignInfo struct {
	Logs      []string `json:"logs"`
	Revoked   bool     `json:"revoked"`
	Signature string   `json:"signature"`
	Timestamp string   `json:"timestamp"`
}

type GetRecordResult struct {
	CredentialHash string                              `json:"credentialHash"`
	SignInfo       map[string]*GetRecordResultSignInfo `json:"signInfo"`
}

// GetRecord 通用上链记录查询
func (f *FiscoUtil) GetRecord(basePath, recordKeyName, recordKeyValue string) (*GetRecordResult, error) {
	postData := map[string]any{
		"weId":        f.weId,
		"password":    f.password,
		recordKeyName: recordKeyValue,
	}
	resp, err := f.doPost(basePath, postData)
	if err != nil {
		return nil, err
	}
	v, err := jsonutil.Encode(resp)
	if err != nil {
		return nil, err
	}
	result := new(GetRecordResult)
	if err = jsonutil.Decode(v, result); err != nil {
		return nil, err
	}
	return result, nil
}

// CreateHashRecord hash上链记录创建
func (f *FiscoUtil) CreateHashRecord(hash string, record *FiscoRecord) (any, error) {
	return f.CreateRecord("/upload/hash-with-log", "hash", hash, record)
}

// AppendLogToHashRecord hash上链记录追加
func (f *FiscoUtil) AppendLogToHashRecord(hash string, record *FiscoRecord) (any, error) {
	return f.AppendLogToRecord("/add/log/hash-evidence", "hash", hash, record)
}

// GetHashRecord hash上链记录查询
func (f *FiscoUtil) GetHashRecord(hash string) (*GetRecordResult, error) {
	return f.GetRecord("/verify-hash", "hash", hash)
}

// CreateBatchCodeRecord batchCode上链记录创建
func (f *FiscoUtil) CreateBatchCodeRecord(batchCode string, record *FiscoRecord) (any, error) {
	return f.CreateRecord("/upload/batchcode-with-log", "batchCode", batchCode, record)
}

// AppendLogToBatchCodeRecord batchCode上链记录追加
func (f *FiscoUtil) AppendLogToBatchCodeRecord(batchCode string, record *FiscoRecord) (any, error) {
	return f.AppendLogToRecord("/add/log/batchcode-evidence", "batchCode", batchCode, record)
}

// GetBatchCodeRecord batchCode上链记录查询
func (f *FiscoUtil) GetBatchCodeRecord(batchCode string) (*GetRecordResult, error) {
	return f.GetRecord("/verify-batchcode", "batchCode", batchCode)
}

// IsTarVerified 检查tar文件hash及tar包里面的zip文件hash是否匹配
func (f *FiscoUtil) IsTarVerified(batchCode, tarPath string, zipPath string) (bool, error) {
	localTarHash, err := hashutil.GetFileHash(tarPath)
	if err != nil {
		return false, fmt.Errorf("cal tar file hash err: %v", err)
	}
	localZipHash := ""
	if zipPath != "" {
		_localZipHash, err := hashutil.GetFileHash(zipPath)
		if err != nil {
			return false, fmt.Errorf("cal zip file hash err: %v", err)
		}
		localZipHash = _localZipHash
	}
	//localFileInfo, err := os.Stat(tarPath)
	//if err != nil {
	//	return false, fmt.Errorf("get file info err: %v", err)
	//}
	var sendLog *RecordLogTypeFileData
	recordResult, err := f.GetBatchCodeRecord(batchCode)
	if err != nil {
		if _, verr := f.reportTar(batchCode, localTarHash, localZipHash, sendLog); verr != nil {
			f.logger.Errorw("report tar file error", "err", verr)
		}
		return false, err
	}
	firstKey := ""
	senderKey, sTimestamp := "", int64(9223372036854775807)
	for s, sv := range recordResult.SignInfo {
		if firstKey == "" {
			firstKey = s
		}
		svTimestampStr := sv.Timestamp
		if len(svTimestampStr) < 13 {
			svTimestampStr = fmt.Sprintf("%-*s", 13, svTimestampStr)
		}
		svTimestamp, _ := strconv.ParseInt(svTimestampStr, 10, 64)
		if svTimestamp != 0 && svTimestamp < sTimestamp {
			sTimestamp = svTimestamp
			senderKey = s
			break
		}
	}
	if senderKey == "" {
		senderKey = firstKey
	}
	f.logger.Debugw("IsTarVerified selected key", "batchCode", batchCode, "senderKey", senderKey)
	firstData := recordResult.SignInfo[senderKey]

	verified := false
	for _, recordLogStr := range firstData.Logs {
		recordLog := new(RecordLogTypeFileData)
		if err := json.Unmarshal([]byte(recordLogStr), recordLog); err != nil {
			return false, err
		}
		if recordLog.ActionDirection == ActionDirectionZipHashSend {
			if recordLog.FileTarHash == localTarHash {
				verified = true
				sendLog = recordLog
				break
			}
		}
	}
	if firstData.Revoked {
		verified = false
	}
	if _, verr := f.reportTar(batchCode, localTarHash, localZipHash, sendLog); verr != nil {
		f.logger.Errorw("report tar file error", "err", verr)
	}

	return verified, nil
}

func (f *FiscoUtil) reportTar(batchCode string, localTarHash, localZipHash string, sendLog *RecordLogTypeFileData) (any, error) {
	newLog := false
	if sendLog == nil {
		newLog = true
		sendLog = &RecordLogTypeFileData{
			BatchCode:                batchCode,
			ActionDirection:          0,
			FileHash:                 "",
			FileWatermarkHash:        "",
			FileZipHash:              "",
			FileTarHash:              "",
			FileLength:               0,
			SenderIdentityCode:       "",
			DataProviderIdentityCode: "",
			DataSenderIpv4:           "",
			DataSenderIpv6:           "",
			SendMethod:               0,
			DataReceiverIpv4:         "",
			DataReceiverIpv6:         "",
			DataReceiverDomain:       "",
			DataUserIdentityCode:     "",
			Timestamp:                0,
			ReservedField:            "",
		}
	}
	sendLog.FileTarHash = localTarHash
	sendLog.FileZipHash = localZipHash
	//sendLog.FileLength = localFileInfo.Size()
	sendLog.ActionDirection = ActionDirectionZipHashReceived
	sendLog.Timestamp = time.Now().UnixMilli()
	sendLog.DataReceiverIpv4 = netutil.IPv4()
	sendLog.DataReceiverIpv6 = netutil.IPv6()
	appendLogBytes, err := json.Marshal(sendLog)
	if err != nil {
		return false, err
	}
	if newLog {
		return f.CreateBatchCodeRecord(batchCode, &FiscoRecord{Log: string(appendLogBytes)})
	} else {
		return f.AppendLogToBatchCodeRecord(batchCode, &FiscoRecord{Log: string(appendLogBytes)})
	}
}

// IsZipChildFileVerified 检查zip包里面的文件hash是否匹配
func (f *FiscoUtil) IsDcmFileVerified(tarPath, childFilePath string, certData *model.RevPackageCertData, certFileStr, batchCode string, watermarkFilepath string) (bool, error) {
	localTarHash, err := hashutil.GetFileHash(tarPath)
	if err != nil {
		return false, fmt.Errorf("cal tar file hash err: %v", err)
	}
	//localZipHash, err := hashutil.GetFileHash(zipPath)
	//if err != nil {
	//	return false, fmt.Errorf("cal zip file hash err: %v", err)
	//}
	localFileHash, err := hashutil.GetFileHash(childFilePath)
	if err != nil {
		return false, fmt.Errorf("cal file hash err: %v", err)
	}
	localFileInfo, err := os.Stat(childFilePath)
	if err != nil {
		return false, fmt.Errorf("get file info err: %v", err)
	}
	watermarkFileHash := ""
	if watermarkFilepath != "" {
		watermarkFileHash, err = hashutil.GetFileHash(watermarkFilepath)
		if err != nil {
			return false, fmt.Errorf("cal watermark file hash err: %v", err)
		}
	}

	var sendLog *RecordLogTypeFileData
	usedRecordStr := ""
	hashKey := localFileHash

	recordResult, err := f.GetHashRecord(hashKey)
	if err != nil {
		fileHashFromCert, fileWatermarkHashFromCert := "", ""
		childFilename := filepath.Base(childFilePath)
		if certData.FileDetail != nil {
			for _, fileDetail := range certData.FileDetail {
				if fileDetail.FileName == childFilename {
					fileHashFromCert, fileWatermarkHashFromCert = fileDetail.FileHash, fileDetail.FileWatermarkHash
					break
				}
			}
		}
		hashKey = fileHashFromCert
		if fileWatermarkHashFromCert != "" {
			hashKey = fileWatermarkHashFromCert
		}
		if hashKey == "" {
			f.logger.Errorw("get hash key error", "hashKey", hashKey, "tarPath", childFilePath, "childFilename", childFilename, "certData", certData)
			return false, errors.New("", "cannot determine hashKey from certData", http.StatusNotFound)
		}
		if _, verr := f.reportDcmFile(hashKey, localFileHash, localTarHash, localFileInfo, sendLog, certData, watermarkFileHash); verr != nil {
			f.logger.Errorw("report zip file error", "err", verr)
		} else {
			go f.doWebhook(hashKey, localFileHash, false, certData, certFileStr, usedRecordStr)
		}
		return false, err
	}

	// 取第一个key,因为要获取上链信息，上链方的机构id为第一个key
	firstKey := ""
	senderKey, sTimestamp := "", int64(9223372036854775807)
	for s, sv := range recordResult.SignInfo {
		if firstKey == "" {
			firstKey = s
		}
		svTimestampStr := sv.Timestamp
		if len(svTimestampStr) < 13 {
			svTimestampStr = fmt.Sprintf("%-*s", 13, svTimestampStr)
		}
		svTimestamp, _ := strconv.ParseInt(svTimestampStr, 10, 64)
		if svTimestamp != 0 && svTimestamp < sTimestamp {
			sTimestamp = svTimestamp
			senderKey = s
			break
		}
	}
	if senderKey == "" {
		senderKey = firstKey
	}
	f.logger.Debugw("IsZipChildFileVerified selected key", "hashKey", hashKey, "senderKey", senderKey)

	firstData := recordResult.SignInfo[senderKey]

	verified := false
	for _, recordLogStr := range firstData.Logs {
		recordLog := new(RecordLogTypeFileData)
		if err := jsonutil.DecodeString(recordLogStr, recordLog); err != nil {
			return false, err
		}
		if recordLog.BatchCode == batchCode && recordLog.ActionDirection == ActionDirectionFileHashSend {
			if recordLog.FileHash == localFileHash || recordLog.FileWatermarkHash == localFileHash {
				verified = true
				//if recordLog.FileHash != "" {
				//	recordLog.FileHash = localFileHash
				//} else if recordLog.FileWatermarkHash != "" {
				//	recordLog.FileWatermarkHash = localFileHash
				//}
				//if recordLog. {
				//}
				sendLog = recordLog
				usedRecordStr = recordLogStr
				break
			}
		}
	}

	if firstData.Revoked {
		verified = false
	}
	if _, verr := f.reportDcmFile(hashKey, localFileHash, localTarHash, localFileInfo, sendLog, certData, watermarkFileHash); verr != nil {
		f.logger.Errorw("report zip file error", "err", verr)
	} else {
		go f.doWebhook(hashKey, localFileHash, verified, certData, certFileStr, usedRecordStr)
	}

	return verified, nil
}

func (f *FiscoUtil) reportDcmFile(hashKey, localFileHash, localTarHash string, localFileInfo os.FileInfo, sendLog *RecordLogTypeFileData, certData *model.RevPackageCertData, revFileWatermarkHash string) (any, error) {
	if sendLog == nil {
		sendLog = &RecordLogTypeFileData{
			BatchCode:                certData.FileSendCode,
			ActionDirection:          0,
			FileHash:                 localFileHash,
			FileWatermarkHash:        "",
			FileZipHash:              "",
			FileTarHash:              "",
			FileLength:               0,
			SenderIdentityCode:       certData.SenderId,
			DataProviderIdentityCode: certData.DataProviderId,
			DataSenderIpv4:           certData.SenderIpv4,
			DataSenderIpv6:           certData.SenderIpv6,
			SendMethod:               certData.SenderType,
			DataReceiverIpv4:         "",
			DataReceiverIpv6:         "",
			DataReceiverDomain:       "",
			DataUserIdentityCode:     certData.DataUserId,
			Timestamp:                0,
			ReservedField:            "",
		}
	}
	sendLog.ActionDirection = ActionDirectionFileHashReceived
	sendLog.FileTarHash = localTarHash
	//sendLog.FileZipHash = localZipHash
	sendLog.Timestamp = time.Now().UnixMilli()
	sendLog.FileLength = localFileInfo.Size()
	sendLog.DataReceiverIpv4 = netutil.IPv4()
	sendLog.DataReceiverIpv6 = netutil.IPv6()
	if revFileWatermarkHash != "" {
		sendLog.RevFileWatermarkHash = revFileWatermarkHash
	}

	appendLogBytes, err := json.Marshal(sendLog)
	if err != nil {
		return false, err
	}

	r1, aerr := f.AppendLogToHashRecord(hashKey, &FiscoRecord{Log: string(appendLogBytes)})
	if aerr != nil {
		f.logger.Errorw("AppendLogToHashRecord", "hashKey", hashKey, "appendLogBytes", string(appendLogBytes), "err", aerr)
		f.logger.Debug("attempt CreateHashRecord")
		r2, aerr2 := f.CreateHashRecord(hashKey, &FiscoRecord{Log: string(appendLogBytes)})
		if aerr2 != nil {
			f.logger.Errorw("CreateHashRecord", "hashKey", hashKey, "appendLogBytes", string(appendLogBytes), "err", aerr2)
			return nil, aerr2
		}
		return r2, nil
	}
	return r1, nil
}

// IsZipChildFileVerified 检查zip包里面的文件hash是否匹配
func (f *FiscoUtil) IsZipChildFileVerified(tarPath, zipPath, childFilePath string, certData *model.RevPackageCertData, certFileStr, batchCode string, watermarkFilepath string) (bool, error) {
	localTarHash, err := hashutil.GetFileHash(tarPath)
	if err != nil {
		return false, fmt.Errorf("cal tar file hash err: %v", err)
	}
	localZipHash, err := hashutil.GetFileHash(zipPath)
	if err != nil {
		return false, fmt.Errorf("cal zip file hash err: %v", err)
	}
	localFileHash, err := hashutil.GetFileHash(childFilePath)
	if err != nil {
		return false, fmt.Errorf("cal file hash err: %v", err)
	}
	localFileInfo, err := os.Stat(childFilePath)
	if err != nil {
		return false, fmt.Errorf("get file info err: %v", err)
	}
	watermarkFileHash := ""
	if watermarkFilepath != "" {
		watermarkFileHash, err = hashutil.GetFileHash(watermarkFilepath)
		if err != nil {
			return false, fmt.Errorf("cal watermark file hash err: %v", err)
		}
	}

	var sendLog *RecordLogTypeFileData
	usedRecordStr := ""
	hashKey := localFileHash

	recordResult, err := f.GetHashRecord(hashKey)
	if err != nil {
		fileHashFromCert, fileWatermarkHashFromCert := "", ""
		childFilename := filepath.Base(childFilePath)
		if certData.FileDetail != nil {
			for _, fileDetail := range certData.FileDetail {
				if fileDetail.FileName == childFilename {
					fileHashFromCert, fileWatermarkHashFromCert = fileDetail.FileHash, fileDetail.FileWatermarkHash
					break
				}
			}
		}
		hashKey = fileHashFromCert
		if fileWatermarkHashFromCert != "" {
			hashKey = fileWatermarkHashFromCert
		}
		if hashKey == "" {
			f.logger.Errorw("get hash key error", "hashKey", hashKey, "tarPath", tarPath, "zipPath", zipPath, "childFilePath", childFilePath, "childFilename", childFilename, "certData", certData)
			return false, errors.New("", "cannot determine hashKey from certData", http.StatusNotFound)
		}
		if _, verr := f.reportZipChildFile(hashKey, localFileHash, localTarHash, localZipHash, localFileInfo, sendLog, certData, watermarkFileHash); verr != nil {
			f.logger.Errorw("report zip file error", "err", verr)
		} else {
			go f.doWebhook(hashKey, localFileHash, false, certData, certFileStr, usedRecordStr)
		}
		return false, err
	}

	// 取第一个key,因为要获取上链信息，上链方的机构id为第一个key
	firstKey := ""
	senderKey, sTimestamp := "", int64(9223372036854775807)
	for s, sv := range recordResult.SignInfo {
		if firstKey == "" {
			firstKey = s
		}
		svTimestampStr := sv.Timestamp
		if len(svTimestampStr) < 13 {
			svTimestampStr = fmt.Sprintf("%-*s", 13, svTimestampStr)
		}
		svTimestamp, _ := strconv.ParseInt(svTimestampStr, 10, 64)
		if svTimestamp != 0 && svTimestamp < sTimestamp {
			sTimestamp = svTimestamp
			senderKey = s
			break
		}
	}
	if senderKey == "" {
		senderKey = firstKey
	}
	f.logger.Debugw("IsZipChildFileVerified selected key", "hashKey", hashKey, "senderKey", senderKey)

	firstData := recordResult.SignInfo[senderKey]

	verified := false
	for _, recordLogStr := range firstData.Logs {
		recordLog := new(RecordLogTypeFileData)
		if err := jsonutil.DecodeString(recordLogStr, recordLog); err != nil {
			return false, err
		}
		if recordLog.BatchCode == batchCode && recordLog.ActionDirection == ActionDirectionFileHashSend {
			if recordLog.FileHash == localFileHash || recordLog.FileWatermarkHash == localFileHash {
				verified = true
				//if recordLog.FileHash != "" {
				//	recordLog.FileHash = localFileHash
				//} else if recordLog.FileWatermarkHash != "" {
				//	recordLog.FileWatermarkHash = localFileHash
				//}
				//if recordLog. {
				//}
				sendLog = recordLog
				usedRecordStr = recordLogStr
				break
			}
		}
	}

	if firstData.Revoked {
		verified = false
	}
	if _, verr := f.reportZipChildFile(hashKey, localFileHash, localTarHash, localZipHash, localFileInfo, sendLog, certData, watermarkFileHash); verr != nil {
		f.logger.Errorw("report zip file error", "err", verr)
	} else {
		go f.doWebhook(hashKey, localFileHash, verified, certData, certFileStr, usedRecordStr)
	}

	return verified, nil
}

//// WatermarkFileHashAppendToBlockChain 该方法与IsZipChildFileVerified完全相同，只是增加了一个watermarkFileHash参数
//func (f *FiscoUtil) WatermarkFileHashAppendToBlockChain(tarPath, zipPath, childFilePath string, certData *model.RevPackageCertData, certFileStr, batchCode string, watermarkFileHash string) (bool, error) {
//	localTarHash, err := hashutil.GetFileHash(tarPath)
//	if err != nil {
//		return false, fmt.Errorf("cal tar file hash err: %v", err)
//	}
//	localZipHash, err := hashutil.GetFileHash(zipPath)
//	if err != nil {
//		return false, fmt.Errorf("cal zip file hash err: %v", err)
//	}
//	localFileHash, err := hashutil.GetFileHash(childFilePath)
//	if err != nil {
//		return false, fmt.Errorf("cal file hash err: %v", err)
//	}
//	localFileInfo, err := os.Stat(childFilePath)
//	if err != nil {
//		return false, fmt.Errorf("get file info err: %v", err)
//	}
//
//	var sendLog *RecordLogTypeFileData
//	usedRecordStr := ""
//	hashKey := localFileHash
//
//	recordResult, err := f.GetHashRecord(hashKey)
//	if err != nil {
//		fileHashFromCert, fileWatermarkHashFromCert := "", ""
//		childFilename := filepath.Base(childFilePath)
//		if certData.FileDetail != nil {
//			for _, fileDetail := range certData.FileDetail {
//				if fileDetail.FileName == childFilename {
//					fileHashFromCert, fileWatermarkHashFromCert = fileDetail.FileHash, fileDetail.FileWatermarkHash
//					break
//				}
//			}
//		}
//		hashKey = fileHashFromCert
//		if fileWatermarkHashFromCert != "" {
//			hashKey = fileWatermarkHashFromCert
//		}
//		if hashKey == "" {
//			f.logger.Errorw("get hash key error", "hashKey", hashKey, "tarPath", tarPath, "zipPath", zipPath, "childFilePath", childFilePath, "childFilename", childFilename, "certData", certData)
//			return false, errors.New("", "cannot determine hashKey from certData", http.StatusNotFound)
//		}
//		if _, verr := f.reportZipChildFile(hashKey, localFileHash, localTarHash, localZipHash, localFileInfo, sendLog, certData, watermarkFileHash); verr != nil {
//			f.logger.Errorw("report zip file error", "err", verr)
//		} else {
//			go f.doWebhook(hashKey, localFileHash, false, certData, certFileStr, usedRecordStr)
//		}
//		return false, err
//	}
//
//	// 取第一个key,因为要获取上链信息，上链方的机构id为第一个key
//	firstKey := ""
//
//	senderKey, sTimestamp := "", int64(9223372036854775807)
//	for s, sv := range recordResult.SignInfo {
//		if firstKey == "" {
//			firstKey = s
//		}
//		svTimestampStr := sv.Timestamp
//		if len(svTimestampStr) < 13 {
//			svTimestampStr = fmt.Sprintf("%-*s", 13, svTimestampStr)
//		}
//		svTimestamp, _ := strconv.ParseInt(svTimestampStr, 10, 64)
//		if svTimestamp != 0 && svTimestamp < sTimestamp {
//			sTimestamp = svTimestamp
//			senderKey = s
//			break
//		}
//	}
//	if senderKey == "" {
//		senderKey = firstKey
//	}
//	f.logger.Debugw("WatermarkFileHashAppendToBlockChain selected key", "hashKey", hashKey, "senderKey", senderKey)
//
//	firstData := recordResult.SignInfo[senderKey]
//
//	verified := false
//	for _, recordLogStr := range firstData.Logs {
//		recordLog := new(RecordLogTypeFileData)
//		if err := jsonutil.DecodeString(recordLogStr, recordLog); err != nil {
//			return false, err
//		}
//		if recordLog.BatchCode == batchCode && recordLog.ActionDirection == ActionDirectionFileHashSend {
//			if recordLog.FileHash == localFileHash || recordLog.FileWatermarkHash == localFileHash {
//				verified = true
//				//if recordLog.FileHash != "" {
//				//	recordLog.FileHash = localFileHash
//				//} else if recordLog.FileWatermarkHash != "" {
//				//	recordLog.FileWatermarkHash = localFileHash
//				//}
//				//if recordLog. {
//				//}
//				sendLog = recordLog
//				usedRecordStr = recordLogStr
//				break
//			}
//		}
//	}
//
//	if firstData.Revoked {
//		verified = false
//	}
//	if _, verr := f.reportZipChildFile(hashKey, localFileHash, localTarHash, localZipHash, localFileInfo, sendLog, certData, watermarkFileHash); verr != nil {
//		f.logger.Errorw("report zip file error", "err", verr)
//	}
//	return verified, nil
//}

func (f *FiscoUtil) reportZipChildFile(hashKey, localFileHash, localTarHash, localZipHash string, localFileInfo os.FileInfo, sendLog *RecordLogTypeFileData, certData *model.RevPackageCertData, revFileWatermarkHash string) (any, error) {
	if sendLog == nil {
		sendLog = &RecordLogTypeFileData{
			BatchCode:                certData.FileSendCode,
			ActionDirection:          0,
			FileHash:                 localFileHash,
			FileWatermarkHash:        "",
			FileZipHash:              "",
			FileTarHash:              "",
			FileLength:               0,
			SenderIdentityCode:       certData.SenderId,
			DataProviderIdentityCode: certData.DataProviderId,
			DataSenderIpv4:           certData.SenderIpv4,
			DataSenderIpv6:           certData.SenderIpv6,
			SendMethod:               certData.SenderType,
			DataReceiverIpv4:         "",
			DataReceiverIpv6:         "",
			DataReceiverDomain:       "",
			DataUserIdentityCode:     certData.DataUserId,
			Timestamp:                0,
			ReservedField:            "",
		}
	}
	sendLog.ActionDirection = ActionDirectionFileHashReceived
	sendLog.FileTarHash = localTarHash
	sendLog.FileZipHash = localZipHash
	sendLog.Timestamp = time.Now().UnixMilli()
	sendLog.FileLength = localFileInfo.Size()
	sendLog.DataReceiverIpv4 = netutil.IPv4()
	sendLog.DataReceiverIpv6 = netutil.IPv6()
	if revFileWatermarkHash != "" {
		sendLog.RevFileWatermarkHash = revFileWatermarkHash
	}

	appendLogBytes, err := json.Marshal(sendLog)
	if err != nil {
		return false, err
	}

	r1, aerr := f.AppendLogToHashRecord(hashKey, &FiscoRecord{Log: string(appendLogBytes)})
	if aerr != nil {
		f.logger.Errorw("AppendLogToHashRecord", "hashKey", hashKey, "appendLogBytes", string(appendLogBytes), "err", aerr)
		f.logger.Debug("attempt CreateHashRecord")
		r2, aerr2 := f.CreateHashRecord(hashKey, &FiscoRecord{Log: string(appendLogBytes)})
		if aerr2 != nil {
			f.logger.Errorw("CreateHashRecord", "hashKey", hashKey, "appendLogBytes", string(appendLogBytes), "err", aerr2)
			return nil, aerr2
		}
		return r2, nil
	}
	return r1, nil
}

func (f *FiscoUtil) doWebhook(hashKey, localFileHash string, verifyResult bool, certData *model.RevPackageCertData, certFileStr, usedRecordStr string) {
	webhook := core.GetConf().App.Rev.Fisco.Webhook
	if webhook != nil {
		//certDataBytes, err := json.Marshal(certData)
		//if err != nil {
		//	f.logger.Errorw("marshal cert data", "err", err)
		//	return
		//}
		postData := map[string]any{
			"blockchain_key":  hashKey,
			"rev_hash":        localFileHash,
			"verify_result":   verifyResult,
			"info_cert":       certFileStr,
			"blockchain_data": usedRecordStr,
		}
		postDataBytes, err := json.Marshal(postData)
		if err != nil {
			f.logger.Errorw("marshal post data", "err", err)
			return
		}

		f.logger.Debugw("doWebhook", "hashKey", hashKey, "localFileHash", localFileHash, "verifyResult", verifyResult, "certData", certData, "certFileStr", certFileStr, "usedRecordStr", usedRecordStr, "postData", string(postDataBytes))

		for _, webhookCfg := range core.GetConf().App.Rev.Fisco.Webhook {
			if !webhookCfg.Enabled || webhookCfg.Addr == "" {
				continue
			}
			err = retry.Do(func() error {
				resp, err := http.Post(webhookCfg.Addr, "application/json", bytes.NewReader(postDataBytes))
				if err != nil {
					return fmt.Errorf("webhook[%s] err: %v", webhookCfg.Addr, err)
				}
				if resp.StatusCode != http.StatusOK {
					return fmt.Errorf("webhook[%s] failed: %v", webhookCfg.Addr, resp.Status)
				}
				return nil
			}, retry.Attempts(3))
			if err != nil {
				f.logger.Errorln(err)
			}
		}
	}
}
