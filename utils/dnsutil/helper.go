package dnsutil

import (
	"fmt"
	"github.com/gookit/goutil/mathutil"
	"net"
	"strings"
)

func ParseIp(domain, dnsServer string) (string, error) {
	domain, dnsServer = strings.TrimSpace(domain), strings.TrimSpace(dnsServer)
	parsedIp := net.ParseIP(domain)
	if parsedIp != nil { // 已经是IP地址
		return domain, nil
	}
	if dnsServer == "" { // dnsServer 为空，使用系统默认的DNS
		addr, err := net.LookupIP(domain)
		if err != nil {
			return "", err
		}
		if addr == nil {
			return "", fmt.<PERSON><PERSON><PERSON>("no ip found for domain[%s]", domain)
		}
		return addr[0].String(), nil
	}
	result1, err := ResolveDNS(domain, dnsServer)
	if err != nil {
		return "", err
	}
	if len(result1) == 0 {
		return "", fmt.Errorf("no ip found for domain[%s] with dnsServer[%s]", domain, dnsServer)
	}

	ipList := make([]string, 0)
	for s := range result1 {
		ipList = append(ipList, s)
	}
	randomKey := mathutil.RandomInt(0, len(ipList))
	return ipList[randomKey], nil
}
