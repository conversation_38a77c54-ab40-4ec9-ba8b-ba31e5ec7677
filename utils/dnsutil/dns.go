package dnsutil

import (
	"fmt"
	"net"
	"strings"
	"time"

	"github.com/miekg/dns"
)

// IPInfo 存储IP地址的详细信息
type IPInfo struct {
	Type       string `json:"type"`        // "ipv4" 或 "ipv6"
	RecordType string `json:"record_type"` // "A" 或 "AAAA"
	TTL        uint32 `json:"ttl"`         // TTL值
	Domain     string `json:"domain"`      // 关联的域名
	DNSServer  string `json:"dns_server"`  // 使用的DNS服务器
}

// ResolveDNS 通过指定的DNS服务器解析域名获取IP地址
// domain: 要解析的域名
// dnsServer: DNS服务器地址（如: "*******:53" 或 "*******"）
// 返回: map[string]interface{} 以IP地址为键的map
func ResolveDNS(domain string, dnsServer string) (map[string]interface{}, error) {
	result := make(map[string]interface{})

	// 确保DNS服务器地址包含端口
	if !strings.Contains(dnsServer, ":") {
		dnsServer = dnsServer + ":53"
	}

	// 验证DNS服务器地址格式
	if _, _, err := net.SplitHostPort(dnsServer); err != nil {
		return result, fmt.Errorf("invalid DNS server address: %v", err)
	}

	// 创建DNS客户端
	client := new(dns.Client)
	client.Timeout = 2 * time.Second

	var allErrors []string

	// 查询A记录（IPv4）
	if err := queryAndAddToResult(client, domain, dns.TypeA, dnsServer, result); err != nil {
		allErrors = append(allErrors, fmt.Sprintf("A record query failed: %v", err))
	}

	// 查询AAAA记录（IPv6）
	if err := queryAndAddToResult(client, domain, dns.TypeAAAA, dnsServer, result); err != nil {
		allErrors = append(allErrors, fmt.Sprintf("AAAA record query failed: %v", err))
	}

	// 如果没有获取到任何IP地址，返回错误
	if len(result) == 0 {
		if len(allErrors) > 0 {
			return result, fmt.Errorf("failed to resolve domain %s: %s", domain, strings.Join(allErrors, "; "))
		}
		return result, fmt.Errorf("no IP addresses found for domain: %s", domain)
	}

	return result, nil
}

// queryAndAddToResult 执行DNS查询并将结果添加到result map中
func queryAndAddToResult(client *dns.Client, domain string, queryType uint16, dnsServer string, result map[string]interface{}) error {
	// 创建DNS查询消息
	msg := new(dns.Msg)
	msg.SetQuestion(dns.Fqdn(domain), queryType)
	msg.RecursionDesired = true

	// 执行查询
	response, _, err := client.Exchange(msg, dnsServer)
	if err != nil {
		return err
	}

	// 检查响应状态
	if response.Rcode != dns.RcodeSuccess {
		return fmt.Errorf("DNS query failed with rcode: %d", response.Rcode)
	}

	// 解析响应并添加到结果中
	for _, answer := range response.Answer {
		var ip string
		var recordType string
		var ipType string

		switch record := answer.(type) {
		case *dns.A:
			ip = record.A.String()
			recordType = "A"
			ipType = "ipv4"
		case *dns.AAAA:
			ip = record.AAAA.String()
			recordType = "AAAA"
			ipType = "ipv6"
		default:
			continue
		}

		// 将IP信息添加到结果map中
		result[ip] = IPInfo{
			Type:       ipType,
			RecordType: recordType,
			TTL:        answer.Header().Ttl,
			Domain:     domain,
			DNSServer:  dnsServer,
		}
	}

	return nil
}

// GetIPsByType 从结果中获取指定类型的IP地址列表
func GetIPsByType(result map[string]interface{}, ipType string) []string {
	var ips []string
	for ip, info := range result {
		if ipInfo, ok := info.(IPInfo); ok && ipInfo.Type == ipType {
			ips = append(ips, ip)
		}
	}
	return ips
}

// GetAllIPs 获取所有IP地址列表
func GetAllIPs(result map[string]interface{}) []string {
	var ips []string
	for ip := range result {
		ips = append(ips, ip)
	}
	return ips
}

// PrintDNSResult 格式化打印DNS解析结果
func PrintDNSResult(result map[string]interface{}) {
	if len(result) == 0 {
		fmt.Println("No IP addresses found")
		return
	}

	fmt.Println("=== DNS Resolution Results ===")

	// 获取第一个IP的信息来显示域名和DNS服务器
	for _, info := range result {
		if ipInfo, ok := info.(IPInfo); ok {
			fmt.Printf("Domain: %s\n", ipInfo.Domain)
			fmt.Printf("DNS Server: %s\n", ipInfo.DNSServer)
			break
		}
	}

	fmt.Println("\nResolved IP Addresses:")

	// 按类型分组显示
	ipv4List := GetIPsByType(result, "ipv4")
	ipv6List := GetIPsByType(result, "ipv6")

	if len(ipv4List) > 0 {
		fmt.Println("\nIPv4 Addresses:")
		for _, ip := range ipv4List {
			if info, ok := result[ip].(IPInfo); ok {
				fmt.Printf("  %s (TTL: %d)\n", ip, info.TTL)
			}
		}
	}

	if len(ipv6List) > 0 {
		fmt.Println("\nIPv6 Addresses:")
		for _, ip := range ipv6List {
			if info, ok := result[ip].(IPInfo); ok {
				fmt.Printf("  %s (TTL: %d)\n", ip, info.TTL)
			}
		}
	}

	fmt.Printf("\nTotal: %d IP addresses\n", len(result))
	fmt.Println("===============================")
}

// 使用示例
/*
说明：如果只使用域名返回的ip，则只取ResolveDNS结果的key（ip）即可，如果key（ip）有多个则只使用第一个。
*/
func main() {
	// 示例1: 解析百度
	fmt.Println("Resolving baidu.com...")
	result1, err := ResolveDNS("baidu.com", "*******")
	if err != nil {
		fmt.Printf("Error: %v\n", err)
	} else {
		PrintDNSResult(result1)

		// 按IP索引访问示例
		fmt.Println("\nAccessing by IP index:")
		for ip, info := range result1 {
			if ipInfo, ok := info.(IPInfo); ok {
				fmt.Printf("IP: %s -> Type: %s, TTL: %d\n", ip, ipInfo.Type, ipInfo.TTL)
			}
		}
	}

	fmt.Println("\n" + strings.Repeat("=", 50) + "\n")

	// 示例2: 解析Google
	fmt.Println("Resolving google.com...")
	result2, err := ResolveDNS("google.com", "*******")
	if err != nil {
		fmt.Printf("Error: %v\n", err)
	} else {
		PrintDNSResult(result2)

		// 获取特定类型的IP
		ipv4List := GetIPsByType(result2, "ipv4")
		ipv6List := GetIPsByType(result2, "ipv6")

		fmt.Printf("\nFiltered results:\n")
		fmt.Printf("IPv4 count: %d\n", len(ipv4List))
		fmt.Printf("IPv6 count: %d\n", len(ipv6List))

		// 检查特定IP是否存在
		if len(ipv4List) > 0 {
			firstIPv4 := ipv4List[0]
			if info, exists := result2[firstIPv4]; exists {
				fmt.Printf("First IPv4 %s exists with info: %+v\n", firstIPv4, info)
			}
		}
	}

	fmt.Println("\n" + strings.Repeat("=", 50) + "\n")

	// 示例3: 解析GitHub
	fmt.Println("Resolving github.com...")
	result3, err := ResolveDNS("github.com", "*********")
	if err != nil {
		fmt.Printf("Error: %v\n", err)
	} else {
		// 演示各种使用方式
		fmt.Printf("Total IPs found: %d\n", len(result3))

		// 遍历所有IP
		fmt.Println("\nAll IPs with details:")
		for ip, info := range result3 {
			if ipInfo, ok := info.(IPInfo); ok {
				fmt.Printf("  %s: %s record, TTL=%d\n", ip, ipInfo.RecordType, ipInfo.TTL)
			}
		}

		// 检查特定IP是否在结果中
		allIPs := GetAllIPs(result3)
		if len(allIPs) > 0 {
			testIP := allIPs[0]
			if _, exists := result3[testIP]; exists {
				fmt.Printf("\nIP %s is in the results\n", testIP)
			}
		}
	}
}
