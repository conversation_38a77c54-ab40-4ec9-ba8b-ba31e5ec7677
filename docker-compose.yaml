services:
  hk_box:
    container_name: hk_box
    user: "${USER_UID}:${USER_GID}"
    image: registry.datasecchk.net/library/hk-box-be:feature-v1.3-a1c5f9bc
    volumes:
      - ./conf.yaml:/app/conf.yaml
      - ./rev/watermark:/app/watermark # modify
      - ./rev/tars:/app/rev/tars # modify
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "10"
    extra_hosts:
      - "host.docker.internal:host-gateway"
    networks:
      cnix:
        ipv4_address: ***********

networks:
  cnix:
    external: true