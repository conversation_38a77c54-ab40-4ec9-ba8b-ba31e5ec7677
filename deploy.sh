#!/bin/bash

INFRA_DIR=/root/docker/infra

# 初始化变量
INNER_IP=""

# 解析命令行参数
for i in "$@"
do
case $i in
    --inner_ip=*)
    INNER_IP="${i#*=}"
    shift # 过去当前参数
    ;;
    *)
    # 未知选项
    ;;
esac
done

# 如果未通过参数传入IP，则尝试获取网络接口的IP地址
if [ -z "$INNER_IP" ]; then
    # 尝试从eth0网卡获取IP地址
    INNER_IP=$(ip addr show eth0 2>/dev/null | grep 'inet ' | awk '{print $2}' | cut -d/ -f1)

    # 如果未成功获取到eth0的IP地址，则尝试从enp1s0网卡获取
    if [ -z "$INNER_IP" ]; then
        INNER_IP=$(ip addr show enp1s0 2>/dev/null | grep 'inet ' | awk '{print $2}' | cut -d/ -f1)
    fi
fi

# 检查是否成功获取到IP地址
if [ -z "$INNER_IP" ]; then
    echo "未能获取到内网IP地址"
    exit 1
else
    echo "获取到的内网IP地址为: $INNER_IP"
    # 在此处可以将$INNER_IP用于其他操作
fi

KAFKA_DIR=/root/docker/infra/kafka

echo -e "\033[33m正在创建kafka目录 ["+"${KAFKA_DIR}"+"]\033[0m"
mkdir -p "${KAFKA_DIR}/conf"
echo -e "\033[33m成功创建kafka目录\033[0m"
echo -e "\033[33m正在配置kafka\033[0m"
docker run -d --name kafka apache/kafka:3.9.0 && docker cp kafka:/etc/kafka/docker/server.properties "${KAFKA_DIR}/conf"
sed -i 's/advertised\.listeners=PLAINTEXT:\/\/localhost:9092,CONTROLLER:\/\/localhost:9093/advertised\.listeners=PLAINTEXT:\/\/'+${INNER_IP}+':9092,CONTROLLER:\/\/localhost:9093/' "${KAFKA_DIR}/conf/server.properties"
docker rm -f kafka
echo -e "\033[33m成功配置kafka\033[0m"

# ---------------------------------------------------------------------------------
MYSQL_DIR=/root/docker/infra/mysql
MYSQL_ROOT_PASSWORD=rootroot
echo -e "\033[33m正在创建mysql目录 ["+"${MYSQL_DIR}"+"]\033[0m"
mkdir -p "${MYSQL_DIR}/datadir"
echo -e "\033[33m成功创建mysql目录\033[0m"
echo -e "\033[33m正在配置mysql\033[0m"
echo -e "\033[33m成功配置mysql [MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}]\033[0m"

# ---------------------------------------------------------------------------------
REDIS_DIR=/root/docker/infra/redis
REDIS_PASSWORD="123456"
echo -e "\033[33m正在创建redis目录 ["+"${REDIS_DIR}"+"]\033[0m"
mkdir -p "${REDIS_DIR}/conf" && mkdir -p "${REDIS_DIR}/data"
echo -e "\033[33m成功创建redis目录\033[0m"
echo -e "\033[33m正在配置redis\033[0m"
cat << EOF > "${REDIS_DIR}/conf/redis.conf"
requirepass ${REDIS_PASSWORD}
EOF
echo -e "\033[33m成功配置redis\033[0m"

# 检查是否已经存在名为cnix的Docker网络
network_exists=$(docker network ls --format "{{.Name}}" | grep "^cnix$" | wc -l)

if [ "$network_exists" -eq 0 ]; then
    echo "\033[33mDocker network cnix not found，creating...\033[0m"
    # 创建名为cnix的Docker网络
    docker network create cnix
    if [ $? -eq 0 ]; then
        echo "\033[33mDocker network cnix created successfully\033[0m"
    else
        echo "\033[31mDocker network cnix create failed\033[0m"
        exit 1
    fi
else
    echo "\033[33mDocker network cnix exist, skip!\033[0m"
fi

echo "\033[33mDeploy infra ["+${INFRA_DIR}+"]\033[0m"
cat << EOF > "${INFRA_DIR}/docker-compose.yml"
services:
  kafka:
    container_name: kafka
    image: apache/kafka:3.9.0
    ports:
      - "9092:9092"
    volumes:
      - ${KAFKA_DIR}/conf/server.properties:/etc/kafka/docker/server.properties
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 4G
        reservations:
          cpus: '0.25'
          memory: 256M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "10"
    networks:
      - cnix

  elasticsearch:
    container_name: elasticsearch
    image: elasticsearch:8.17.0
    ports:
      - "9200:9200"
      - "9300:9300"
    environment:
      - xpack.security.enrollment.enabled=false
      - discovery.type=single-node
      - xpack.security.enabled=false
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 4G
        reservations:
          cpus: '0.25'
          memory: 256M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "10"
    networks:
      - cnix

  mysql:
    container_name: mysql
    image: mysql:8.0.39
    ports:
      - "33068:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
    volumes:
      - ${MYSQL_DIR}/datadir:/var/lib/mysql
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 4G
        reservations:
          cpus: '0.25'
          memory: 256M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "10"
    networks:
      - cnix

  redis:
    container_name: redis
    image: redis:latest
    ports:
      - "36379:6379"
    volumes:
      - ${REDIS_DIR}/conf:/usr/local/etc/redis
      - ${REDIS_DIR}/data:/data
    command:
      - redis-server
      - /usr/local/etc/redis/redis.conf
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 4G
        reservations:
          cpus: '0.25'
          memory: 256M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "10"
    networks:
      - cnix

networks:
  cnix:
    external: true
EOF

docker compose -f "${INFRA_DIR}/docker-compose.yml" up -d
