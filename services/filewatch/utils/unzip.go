package utils

import (
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/model"
	"github.com/yeka/zip"
	"io"
	"os"
	"path"
	"path/filepath"
)

func TryUnzipFromReader(fileReader io.ReaderAt, size int64, basePath, targetDir string, password string) ([]*model.RevPackageZipFile, error) {
	r, err := zip.NewReader(fileReader, size)
	if err != nil {
		return nil, err
	}

	infos := make([]*model.RevPackageZipFile, 0)
	for _, ff := range r.File {
		targetPath := path.Join(basePath, targetDir, ff.Name)
		fInfo := ff.FileInfo()
		if fInfo.IsDir() {
			if err := os.MkdirAll(targetPath, os.ModePerm); err != nil {
				return nil, err
			}
			continue
		}

		zipFile := &model.RevPackageZipFile{
			Filename:    filepath.Base(ff.Name),
			Filepath:    path.Join(targetDir, filepath.Dir(ff.Name)),
			Filesize:    fInfo.Size(),
			IsEncrypted: ff.IsEncrypted(),
		}

		infos = append(infos, zipFile)

		if zipFile.IsEncrypted {
			//continue
			ff.SetPassword(password)
		}
		if !fInfo.IsDir() {
			if err := os.MkdirAll(filepath.Dir(targetPath), os.ModePerm); err != nil {
				return nil, err
			}
		}

		rf, err := ff.Open()
		if err != nil {
			return nil, err
		}
		out, err := os.Create(targetPath)
		if err != nil {
			return nil, err
		}
		if _, err = io.Copy(out, rf); err != nil {
			return nil, err
		}
		if err = rf.Close(); err != nil {
			return nil, err
		}
		if err = out.Close(); err != nil {
			return nil, err
		}
	}
	return infos, nil
}
