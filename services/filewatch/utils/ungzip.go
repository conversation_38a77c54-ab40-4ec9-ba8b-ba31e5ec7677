package utils

import (
	"archive/tar"
	"bytes"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/core"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/model"
	"compress/gzip"
	"encoding/json"
	"errors"
	"fmt"
	"go.uber.org/zap"
	"io"
	"os"
	"path"
	"path/filepath"
	"strconv"
	"strings"
)

func parseGzipCertFile(logger *zap.SugaredLogger, filename string) (*model.RevPackageCertData, error) {
	fInfo, err := os.Stat(filename)
	if err != nil {
		logger.Errorw("stat file failed", "err", err)
		return nil, err
	}
	if fInfo.IsDir() { // skip dir
		return nil, nil
	}
	if fInfo.Size() == 0 { // skip empty file
		return nil, nil
	}
	if !strings.HasSuffix(fInfo.Name(), ".tar.gz") { // skip non-tar.gz file
		return nil, nil
	}

	src, err := os.Open(filename)
	if err != nil {
		logger.Errorw("open file failed", "err", err)
		return nil, err
	}
	defer src.Close()

	gzReader, err := gzip.NewReader(src)
	if err != nil {
		logger.Errorw("create gzip reader failed", "err", err)
		return nil, err
	}
	defer gzReader.Close()

	tarReader := tar.NewReader(gzReader)

	var pkgCertData *model.RevPackageCertData

	for {
		header, err := tarReader.Next()
		if err != nil {
			if err != io.EOF {
				logger.Errorw("read tar file failed", "err", err)
				return nil, err
			}
			break
		}
		if header.Typeflag != tar.TypeDir && header.Typeflag != tar.TypeReg {
			// Only handle dir and file
			continue
		}
		//targetPath := path.Join(tempDir, header.Name)
		//if header.Typeflag == tar.TypeDir {
		//	if err = os.MkdirAll(targetPath, os.ModePerm); err != nil {
		//		logger.Errorw("mkdir failed", "err", err)
		//		return nil, err
		//	}
		//	continue
		//}
		//if _, err = os.Stat(filepath.Dir(targetPath)); err != nil {
		//	if errors.Is(err, os.ErrNotExist) {
		//		if err = os.MkdirAll(filepath.Dir(targetPath), os.ModePerm); err != nil {
		//			logger.Errorw("mkdir failed", "err", err)
		//			return nil, err
		//		}
		//	}
		//}

		if filepath.Base(header.Name) == "info.cert" {
			bf := bytes.NewBuffer(nil)
			if _, err := io.Copy(bf, tarReader); err != nil {
				logger.Errorw("read tar file failed", "err", err)
				return nil, err
			}
			pkgCertData = new(model.RevPackageCertData)
			if err := json.Unmarshal(bf.Bytes(), pkgCertData); err != nil {
				logger.Errorw("unmarshal info.cert failed", "err", err)
				return nil, err
			}
			if len(pkgCertData.FileDetail) == 0 {
				err = fmt.Errorf("file details not found in cert[%s]", filename)
				logger.Errorln(err)
				return nil, err
			}
			break
		}
	}

	return pkgCertData, nil
}

func TryParseGzip(logger *zap.SugaredLogger, filename, tempDir string, unzip bool) (*model.RevPackage, error) {
	__pkgCertData__, err := parseGzipCertFile(logger, filename)
	if err != nil {
		return nil, err
	}

	fInfo, err := os.Stat(filename)
	if err != nil {
		logger.Errorw("stat file failed", "err", err)
		return nil, err
	}
	if fInfo.IsDir() { // skip dir
		return nil, nil
	}
	if fInfo.Size() == 0 { // skip empty file
		return nil, nil
	}
	if !strings.HasSuffix(fInfo.Name(), ".tar.gz") { // skip non-tar.gz file
		return nil, nil
	}

	pkgDocument := &model.RevPackage{
		Id:             strconv.FormatInt(core.GetNextId(), 10),
		Filename:       fInfo.Name(),
		Filepath:       filepath.Dir(filename),
		Filesize:       fInfo.Size(),
		SendCode:       "",
		SenderId:       "",
		Timestamp:      0,
		Cert:           nil,
		SftpSend:       nil,
		DcmPath:        "",
		DcmPaths:       nil,
		ZipControlPath: "",
		Zips:           nil,
	}

	sftpSendMap := make(map[string]struct{})
	if __pkgCertData__.SftpSend != nil {
		for _, sftpSend := range __pkgCertData__.SftpSend {
			sftpSendMap[sftpSend] = struct{}{}
		}
	}

	src, err := os.Open(filename)
	if err != nil {
		logger.Errorw("open file failed", "err", err)
		return nil, err
	}
	defer src.Close()

	gzReader, err := gzip.NewReader(src)
	if err != nil {
		logger.Errorw("create gzip reader failed", "err", err)
		return nil, err
	}
	defer gzReader.Close()

	tarReader := tar.NewReader(gzReader)

	containCertFile := false
	for {
		header, err := tarReader.Next()
		if err != nil {
			if err != io.EOF {
				logger.Errorw("read tar file failed", "err", err)
				return nil, err
			}
			break
		}
		if header.Typeflag != tar.TypeDir && header.Typeflag != tar.TypeReg {
			// Only handle dir and file
			continue
		}
		targetPath := path.Join(tempDir, header.Name)
		if header.Typeflag == tar.TypeDir {
			if err = os.MkdirAll(targetPath, os.ModePerm); err != nil {
				logger.Errorw("mkdir failed", "err", err)
				return nil, err
			}
			continue
		}
		if _, err = os.Stat(filepath.Dir(targetPath)); err != nil {
			if errors.Is(err, os.ErrNotExist) {
				if err = os.MkdirAll(filepath.Dir(targetPath), os.ModePerm); err != nil {
					logger.Errorw("mkdir failed", "err", err)
					return nil, err
				}
			}
		}

		if _, ok := sftpSendMap[filepath.Clean(header.Name)]; ok {
			if pkgDocument.SftpSend == nil {
				pkgDocument.SftpSend = make([]*model.RevPackageFileSftpSendFile, 0)
			}
			pkgDocument.SftpSend = append(pkgDocument.SftpSend, &model.RevPackageFileSftpSendFile{
				Filesize:     header.Size,
				FileRelPath:  header.Name,
				FileBasename: filepath.Base(header.Name),
			})
			if err = writeFile(targetPath, tarReader); err != nil {
				logger.Errorw("write file failed", "err", err)
				return nil, err
			}
		} else {
			if filepath.Ext(header.Name) == ".zip" {
				bf := bytes.NewBuffer(nil)
				if _, err := io.Copy(bf, tarReader); err != nil {
					logger.Errorw("read tar file failed", "err", err)
					return nil, err
				}

				zipChildFiles := make([]*model.RevPackageZipFile, 0)
				if unzip {
					_zipChildFiles, err := TryUnzipFromReader(
						bytes.NewReader(bf.Bytes()),
						header.Size,
						filepath.Dir(targetPath),
						filepath.Base(header.Name)[:len(filepath.Base(header.Name))-len(filepath.Ext(header.Name))],
						__pkgCertData__.ZipMima,
					)
					if err != nil {
						logger.Errorw("unzip failed", "err", err)
						return nil, err
					}
					zipChildFiles = _zipChildFiles
				}
				if len(zipChildFiles) > 0 {
					zipFile := &model.RevPackageZip{
						Filename: filepath.Base(header.Name),
						Filepath: filepath.Dir(header.Name),
						Filesize: header.Size,
						Files:    zipChildFiles,
					}
					if pkgDocument.Zips == nil {
						pkgDocument.Zips = make([]*model.RevPackageZip, 0)
					}
					pkgDocument.Zips = append(pkgDocument.Zips, zipFile)
				}

				if err = writeFile(targetPath, bf); err != nil {
					logger.Errorw("write file failed", "err", err)
					return nil, err
				}
			} else if filepath.Base(header.Name) == "info.cert" {
				bf := bytes.NewBuffer(nil)
				if _, err := io.Copy(bf, tarReader); err != nil {
					logger.Errorw("read tar file failed", "err", err)
					return nil, err
				}
				pkgCertData := new(model.RevPackageCertData)
				if err := json.Unmarshal(bf.Bytes(), pkgCertData); err != nil {
					logger.Errorw("unmarshal info.cert failed", "err", err)
					return nil, err
				}
				if len(pkgCertData.FileDetail) == 0 {
					err = fmt.Errorf("file details not found in cert[%s]", filename)
					logger.Errorln(err)
					return nil, err
				}
				pkgCert := &model.RevPackageCert{
					Filename: filepath.Base(header.Name),
					Filepath: filepath.Dir(header.Name), // 此处记录下文件路径，方便后续找到该文件以验证哈希值
					Data:     pkgCertData,
				}
				pkgDocument.Timestamp = pkgCert.Data.Timestamp
				pkgDocument.SendCode = pkgCert.Data.FileSendCode
				pkgDocument.SenderId = pkgCert.Data.SenderId
				pkgDocument.Cert = pkgCert

				if err = writeFile(targetPath, bf); err != nil {
					logger.Errorw("write file failed", "err", err)
					return nil, err
				}
				containCertFile = true
			} else if strings.ToLower(filepath.Ext(header.Name)) == ".dcm" {
				if pkgDocument.DcmPaths == nil {
					pkgDocument.DcmPaths = make([]string, 0)
				}
				pkgDocument.DcmPaths = append(pkgDocument.DcmPaths, header.Name)
				if err = writeFile(targetPath, tarReader); err != nil {
					logger.Errorw("write file failed", "err", err)
					return nil, err
				}
			} else {
				if err = writeFile(targetPath, tarReader); err != nil {
					logger.Errorw("write file failed", "err", err)
					return nil, err
				}
			}
		}
	}
	if !containCertFile {
		return nil, nil
	}

	return pkgDocument, nil
}
