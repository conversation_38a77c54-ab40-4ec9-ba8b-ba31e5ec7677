package filewatch

import (
	"bytes"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"code.ixdev.cn/cnix/cbdv/hk-box-be/core"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/services/filewatch/utils"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/services/ipmanager"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/model"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/utils/dnsutil"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/utils/fiscoutil"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/utils/pinyinutil"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/utils/sftputil2"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/utils/special"
	"github.com/fsnotify/fsnotify"
	"github.com/gookit/goutil/strutil"
	"go.uber.org/zap"
	"golang.org/x/sync/errgroup"
	"gorm.io/gorm"
)

//var (
//	revMutex = &sync.Mutex{}
//)

//func SyncFile() {
//	logger, err := core.GetLogger()
//	if err != nil {
//		log.Fatalln(err)
//	}
//	consumer, err := core.GetKafkaConsumer()
//	if err != nil {
//		logger.Errorln(err)
//		return
//	}
//	if err = consumer.Subscribe(global.KafkaTopicFileWatch, nil); err != nil {
//		logger.Errorln(err)
//		return
//	}
//	orm, err := core.GetDatabase()
//	if err != nil {
//		logger.Errorln(err)
//		return
//	}
//	handler := newMessageHandler(orm, logger)
//	for {
//		message, err := consumer.ReadMessage(5 * time.Second)
//		if err != nil {
//			if consumer.IsClosed() {
//				break
//			}
//			if !err.(kafka.Error).IsTimeout() {
//				logger.Errorln(err)
//			}
//			continue
//		}
//		topic := ""
//		if message.TopicPartition.Topic != nil {
//			topic = *message.TopicPartition.Topic
//		}
//		logger.Infow("fsnotify", "from topic", topic, "rev message", string(message.Value))
//		handler.handle(message)
//	}
//}

type messageHandler struct {
	logger    *zap.SugaredLogger
	orm       *gorm.DB
	mutex     sync.Mutex
	ipManager *ipmanager.IPManager
}

func newMessageHandler(orm *gorm.DB, logger *zap.SugaredLogger) *messageHandler {
	ipMgr, err := ipmanager.NewIPManager()
	if err != nil {
		logger.Errorw("failed to create ip manager", "error", err)
		// 继续运行，但IP管理器为nil
	}

	return &messageHandler{
		orm:       orm,
		logger:    logger,
		mutex:     sync.Mutex{},
		ipManager: ipMgr,
	}
}

func (f *messageHandler) handle(op fsnotify.Op, src, dst string) {
	f.mutex.Lock()
	defer f.mutex.Unlock()

	var err error

	if dst == "" {
		dst = src
	}
	basePathAbs, err := filepath.Abs(".")
	if err != nil {
		f.logger.Errorw("get abs path failed", "err", err)
		return
	}
	relPath, err := filepath.Rel(basePathAbs, dst)
	if err != nil {
		f.logger.Errorw("get rel path failed", "err", err)
		return
	}

	fileDir, fileBasename := filepath.Dir(relPath), filepath.Base(relPath)
	if !strings.HasSuffix(fileBasename, ".tar.gz") {
		return
	}
	pkgDocument := new(model.RevPackage)
	if err = f.orm.Unscoped().Where("filepath=? and filename=?", fileDir, fileBasename).Limit(1).Find(pkgDocument).Error; err != nil {
		f.logger.Errorw("find pkg document failed", "err", err)
		return
	}

	switch op {
	case fsnotify.Create:
		f.handlePackage(relPath, pkgDocument)
	case fsnotify.Write:
		f.handlePackage(relPath, pkgDocument)
	case fsnotify.Rename:
		f.handlePackage(relPath, pkgDocument)
	case fsnotify.Remove:
		if pkgDocument.Id != "" {
			if err = f.orm.Delete(pkgDocument).Error; err != nil {
				f.logger.Errorw("delete pkg document failed", "err", err)
				return
			}
		}
	default:
		f.logger.Errorw("unsupported fsnotify op", "op", op)
	}
}

type UpToBlChainData struct {
	PackageDst  string
	TempDir     string
	PackageM    *model.RevPackage
	Institution string
}

func (f *messageHandler) handleRemoteUpload(sysConfig *model.SysConfig, sendCode string, baseDirPath string, dcmRelPaths []string, sftpSendFiles []*model.RevPackageFileSftpSendFile, upToBlChainData *UpToBlChainData) (sftpUploadSuccess bool) {
	if sysConfig.Sftp == nil && sysConfig.Dicom == nil {
		f.logger.Debugw("no sftp or dicom config")
		return false
	}

	startAt := time.Now()
	sftpUploadSuccess = true // 默认为true，只有在SFTP上传失败时才设为false

	// 处理SFTP上传
	if sysConfig.Sftp != nil && sysConfig.Sftp.Enabled && sftpSendFiles != nil {
		if err := f.handleSftpUpload(sysConfig, sendCode, baseDirPath, sftpSendFiles, upToBlChainData); err != nil {
			f.logger.Errorw("sftp upload failed", "error", err)
			sftpUploadSuccess = false
		}
	} else {
		f.logger.Debugw("sftp upload disabled or no files to upload")
	}

	// 处理DICOM上传
	if sysConfig.Dicom != nil && sysConfig.Dicom.Enabled && len(dcmRelPaths) > 0 {
		if err := f.handleDicomUpload(sysConfig, sendCode, baseDirPath, dcmRelPaths, upToBlChainData); err != nil {
			f.logger.Errorw("dicom upload failed", "error", err)
		}
	} else {
		f.logger.Debugw("dicom upload disabled or no files to upload")
	}

	f.logger.Infof("remote upload finished in %v ms\n", time.Since(startAt).Milliseconds())
	return sftpUploadSuccess
}

// handleSftpUpload 处理SFTP上传逻辑
func (f *messageHandler) handleSftpUpload(sysConfig *model.SysConfig, sendCode string, baseDirPath string, sftpSendFiles []*model.RevPackageFileSftpSendFile, upToBlChainData *UpToBlChainData) error {
	// 重置重试计数器
	if f.ipManager != nil {
		f.ipManager.ResetRetryCounters()
	}

	authConfigs, err := f.buildSftpAuthConfigs(sysConfig)
	if err != nil {
		return err
	}

	if len(authConfigs) == 0 {
		return fmt.Errorf("no valid sftp auth configs found")
	}

	var lastErr error
	for {
		for _, uploadAuthConfig := range authConfigs {
			err := f.uploadSftpFiles(uploadAuthConfig, sftpSendFiles, sendCode, baseDirPath, upToBlChainData, sysConfig)
			if err == nil {
				f.logger.Infow("sftp upload successful", "host", uploadAuthConfig.Host)
				return nil
			}
			lastErr = err
			f.logger.Warnw("sftp upload with one config failed", "host", uploadAuthConfig.Host, "error", err)

			// 如果是IP模式且有IP管理器，尝试切换到下一个可用IP
			if sysConfig.Sftp.Auth.ResolveType == "ip" && f.ipManager != nil {
				nextIP, switchErr := f.ipManager.SwitchToNextSftpIP(uploadAuthConfig.Host)
				if switchErr != nil {
					f.logger.Warnw("failed to switch to next sftp ip", "error", switchErr)
					return fmt.Errorf("all sftp upload attempts failed: %w", lastErr)
				} else {
					f.logger.Infow("switched to next sftp ip", "from", uploadAuthConfig.Host, "to", nextIP.Host)
					// 重新构建认证配置列表，使用新的活跃IP
					newAuthConfig := f.buildSftpAuthConfig(nextIP)
					if newAuthConfig != nil {
						authConfigs = []sftputil2.AuthConfig{*newAuthConfig}
						f.logger.Infow("updated auth configs with new active ip", "host", nextIP.Host)
						break // 跳出内层循环，使用新IP重试
					}
				}
			} else {
				// 非IP模式，直接返回错误
				return fmt.Errorf("sftp upload failed: %w", lastErr)
			}
		}
	}
}

// buildSftpAuthConfigs 构建SFTP认证配置列表
func (f *messageHandler) buildSftpAuthConfigs(sysConfig *model.SysConfig) ([]sftputil2.AuthConfig, error) {
	var authConfigs []sftputil2.AuthConfig

	if sysConfig.Sftp.Auth.ResolveType == "ip" && len(sysConfig.Sftp.Auth.IpConfigs) > 0 {
		// 使用IP管理器确保有活跃的IP
		if f.ipManager != nil {
			if err := f.ipManager.EnsureActiveIPExists(); err != nil {
				f.logger.Warnw("failed to ensure active ip exists", "error", err)
			}
		}

		// 优先使用活跃的IP，如果没有则使用所有IP
		activeIP, err := f.getActiveSftpIP(sysConfig)
		if err == nil && activeIP != nil {
			// 只使用活跃的IP
			authConfig := f.buildSftpAuthConfig(activeIP)
			if authConfig != nil {
				authConfigs = append(authConfigs, *authConfig)
			}
		} else {
			// 如果没有活跃IP或获取失败，使用所有IP
			for _, ipConfig := range sysConfig.Sftp.Auth.IpConfigs {
				authConfig := f.buildSftpAuthConfig(&ipConfig)
				if authConfig != nil {
					authConfigs = append(authConfigs, *authConfig)
				}
			}
		}
	} else {
		authIp, err := dnsutil.ParseIp(sysConfig.Sftp.Auth.Host, sysConfig.Sftp.Auth.DnsServer)
		if err != nil {
			f.logger.Errorw("parse ip error", "error", err)
			return nil, err
		}
		authConfig := sftputil2.AuthConfig{
			Host:     authIp,
			Port:     int(sysConfig.Sftp.Auth.Port),
			Username: sysConfig.Sftp.Auth.Username,
			Timeout:  time.Duration(sysConfig.Sftp.Auth.Timeout) * time.Millisecond,
		}

		switch sysConfig.Sftp.Auth.Type {
		case 0:
			authConfig.Type = sftputil2.AuthPassword
			authConfig.Password = sysConfig.Sftp.Auth.Password
		case 1:
			authConfig.Type = sftputil2.AuthPrivateKey
			if sysConfig.Sftp.Auth.PrivateKeyType == 0 {
				authConfig.PrivateKey = []byte(sysConfig.Sftp.Auth.PrivateKeyContent)
			} else {
				authConfig.PrivateKeyPath = sysConfig.Sftp.Auth.PrivateKeyPath
			}
			authConfig.PrivateKeyPasswd = sysConfig.Sftp.Auth.PrivateKeyPassphrase
		default:
			return nil, fmt.Errorf("unsupported sftp auth type")
		}
		authConfigs = append(authConfigs, authConfig)
	}

	return authConfigs, nil
}

func (f *messageHandler) handlePackage(relPath string, pkgDocument *model.RevPackage) {
	tempDir := utils.GetUnGzipDir(relPath) + strutil.RandomCharsV3(8)
	defer func() {
		_ = os.RemoveAll(tempDir)
	}()
	_pkgDocument, err := utils.TryParseGzip(f.logger, relPath, tempDir, true)
	if err != nil {
		return
	}
	//revMutex.Lock()
	//defer revMutex.Unlock()
	if _pkgDocument != nil && _pkgDocument.Cert != nil {
		//senderName, err := fiscoutil.GetSenderNameBySenderId(_pkgDocument.SenderId)
		//if err != nil {
		//	f.logger.Errorw("get sender name failed", "err", err)
		//	return
		//}
		senderName := _pkgDocument.Cert.Data.DataSenderInfo
		senderNamePy := pinyinutil.Convert(senderName)
		revSender := &model.RevSender{
			Id:           strconv.FormatInt(core.GetNextId(), 10),
			SenderId:     _pkgDocument.SenderId,
			SenderName:   senderName,
			SenderNamePy: senderNamePy,
		}

		if pkgDocument.Id == "" {
			sysConfig := new(model.SysConfig)
			if err = f.orm.Where("id=?", model.SysConfigKey).Limit(1).Find(sysConfig).Error; err != nil {
				f.logger.Errorw("find sys config failed", "err", err)
				return
			}
			if sysConfig.Id == 0 {
				f.logger.Errorln("no system config found")
				return
			}
			if sysConfig.Sftp != nil && sysConfig.Sftp.Upload.DeleteAfterUpload && len(_pkgDocument.SftpSend) > 0 {
				_pkgDocument.RecordReserved = true
			}

			isVerified := false
			var firstZip *model.RevPackageZip
			if len(_pkgDocument.Zips) > 0 {
				firstZip = _pkgDocument.Zips[0]
				zipFilePath := filepath.Join(tempDir, firstZip.Filepath, firstZip.Filename)
				_isVerified, err := fiscoutil.NewFiscoUtil().IsTarVerified(_pkgDocument.SendCode, relPath, zipFilePath)
				if err != nil {
					f.logger.Errorw("verify tar error", "err", err)
					//return
				}
				isVerified = _isVerified
				f.logger.Debugw("verified result", "isVerified", isVerified, "relPath", relPath, "tempDir", tempDir, "_pkgDocument.SendCode", _pkgDocument.SendCode, "zipFilePath", zipFilePath)
			} else {
				if len(_pkgDocument.SftpSend) > 0 {
					for _, sendFile := range _pkgDocument.SftpSend {
						if filepath.Ext(sendFile.FileBasename) == ".zip" {
							_isVerified, err := fiscoutil.NewFiscoUtil().IsTarVerified(_pkgDocument.SendCode, relPath, filepath.Join(tempDir, sendFile.FileRelPath))
							if err != nil {
								f.logger.Errorw("verify tar error", "err", err)
								//return
							}
							isVerified = _isVerified
							break
						}
					}
				} else {
					_isVerified, err := fiscoutil.NewFiscoUtil().IsTarVerified(_pkgDocument.SendCode, relPath, "")
					if err != nil {
						f.logger.Errorw("verify tar error", "err", err)
						//return
					}
					isVerified = _isVerified
				}
			}

			_pkgDocument.TarHashVerified = isVerified
			_pkgDocument.ZipHashVerified = isVerified

			err = f.orm.Transaction(func(tx *gorm.DB) error {
				revSenderM := new(model.RevSender)
				if err = tx.Model(new(model.RevSender)).Where("sender_id=?", _pkgDocument.SenderId).Limit(1).Find(revSenderM).Error; err != nil {
					f.logger.Errorw("find sender failed", "err", err)
					return err
				}
				if revSenderM.Id == "" {
					if err = tx.Create(revSender).Error; err != nil {
						f.logger.Errorw("create sender failed", "err", err)
						return err
					}
				} else {
					if err = tx.Model(revSenderM).Select("sender_id", "sender_name", "sender_name_py").Updates(revSender).Error; err != nil {
						f.logger.Errorw("update sender failed", "err", err)
						return err
					}
				}
				if err = tx.Create(_pkgDocument).Error; err != nil {
					f.logger.Errorw("create pkg document failed", "err", err)
					return err
				}
				return nil
			})
			if err != nil {
				return
			}
			//if _pkgDocument.SftpSend != nil {
			//	_pkgDocument.SftpSend = append(_pkgDocument.SftpSend, &model.RevPackageFileSftpSendFile{
			//		Filesize:     0,
			//		FileRelPath:  _pkgDocument.ZipControlPath,
			//		FileBasename: filepath.Base(_pkgDocument.ZipControlPath),
			//	})
			//}

			adminUser := new(model.User)
			if err = f.orm.Where("username=?", model.AdminUsername).Where("is_admin", true).Limit(1).Find(adminUser).Error; err != nil {
				f.logger.Errorln(err)
			}

			upToBlChainData := &UpToBlChainData{
				PackageDst:  filepath.Join(_pkgDocument.Filepath, _pkgDocument.Filename),
				TempDir:     tempDir,
				PackageM:    _pkgDocument,
				Institution: adminUser.Institution,
			}

			sftpUploadSuccess := f.handleRemoteUpload(sysConfig, _pkgDocument.SendCode, tempDir, _pkgDocument.DcmPaths, _pkgDocument.SftpSend, upToBlChainData)

			// 根据配置决定是否删除文件
			shouldDelete := false
			if _pkgDocument.RecordReserved {
				// 上传成功后删除
				shouldDelete = true
			} else if sysConfig.Sftp != nil && sysConfig.Sftp.Upload.DeleteAfterUploadFail && !sftpUploadSuccess && len(_pkgDocument.SftpSend) > 0 {
				// 上传失败后删除
				shouldDelete = true
				f.logger.Infow("deleting file after sftp upload failure", "sendCode", _pkgDocument.SendCode, "relPath", relPath)
			}

			if shouldDelete {
				err = os.Remove(relPath)
				if err != nil {
					f.logger.Errorw("remove pkg document failed", "err", err)
				}
			}
		} else {
			_pkgDocument.Id = ""

			err = f.orm.Transaction(func(tx *gorm.DB) error {
				revSenderM := new(model.RevSender)
				if err = tx.Model(new(model.RevSender)).Where("sender_id=?", _pkgDocument.SenderId).Limit(1).Find(revSenderM).Error; err != nil {
					f.logger.Errorw("find sender failed", "err", err)
					return err
				}
				if revSenderM.Id == "" {
					if err = tx.Create(revSender).Error; err != nil {
						f.logger.Errorw("create sender failed", "err", err)
						return err
					}
				} else {
					if err = tx.Model(revSenderM).Select("sender_id", "sender_name", "sender_name_py").Updates(revSender).Error; err != nil {
						f.logger.Errorw("update sender failed", "err", err)
						return err
					}
				}
				if err = tx.Unscoped().Model(pkgDocument).Select(
					"Filename",
					"Filepath",
					"Filesize",
					"SendCode",
					"SenderId",
					"Timestamp",
					"Cert",
					"Zips",
					"ArchivedAt",
					//"CreatedAt",
					"UpdatedAt",
					"DeletedAt",
				).Updates(_pkgDocument).Error; err != nil {
					f.logger.Errorw("update pkg document failed", "err", err)
					return err
				}
				return nil
			})
			if err != nil {
				return
			}
		}
	}
}

// getActiveSftpIP 获取活跃的SFTP IP配置
func (f *messageHandler) getActiveSftpIP(sysConfig *model.SysConfig) (*model.SftpIpConfig, error) {
	if f.ipManager == nil {
		return nil, fmt.Errorf("ip manager not available")
	}
	return f.ipManager.GetActiveSftpIP()
}

// buildSftpAuthConfig 构建SFTP认证配置
func (f *messageHandler) buildSftpAuthConfig(ipConfig *model.SftpIpConfig) *sftputil2.AuthConfig {
	authConfig := sftputil2.AuthConfig{
		Host:     ipConfig.Host,
		Port:     int(ipConfig.Port),
		Username: ipConfig.Username,
		Timeout:  time.Duration(ipConfig.Timeout) * time.Millisecond,
	}

	switch ipConfig.Type {
	case 0:
		authConfig.Type = sftputil2.AuthPassword
		authConfig.Password = ipConfig.Password
	case 1:
		authConfig.Type = sftputil2.AuthPrivateKey
		if ipConfig.PrivateKeyType == 0 {
			authConfig.PrivateKey = []byte(ipConfig.PrivateKeyContent)
		} else {
			authConfig.PrivateKeyPath = ipConfig.PrivateKeyPath
		}
		authConfig.PrivateKeyPasswd = ipConfig.PrivateKeyPassphrase
	default:
		f.logger.Errorw("unsupported sftp auth type in ip_configs", "type", ipConfig.Type)
		return nil
	}

	return &authConfig
}

// getActiveDicomIP 获取活跃的DICOM IP配置
func (f *messageHandler) getActiveDicomIP(sysConfig *model.SysConfig) (*model.DicomIpConfig, error) {
	if f.ipManager == nil {
		return nil, fmt.Errorf("ip manager not available")
	}
	return f.ipManager.GetActiveDicomIP()
}

// uploadSftpFiles 上传SFTP文件
func (f *messageHandler) uploadSftpFiles(config sftputil2.AuthConfig, sftpSendFiles []*model.RevPackageFileSftpSendFile, sendCode string, baseDirPath string, upToBlChainData *UpToBlChainData, sysConfig *model.SysConfig) error {
	for _, sftpSendFile := range sftpSendFiles {
		sftpSendFilepath := filepath.Join(baseDirPath, sftpSendFile.FileRelPath)

		// 检查是否已经上传过
		total := int64(0)
		err := f.orm.Model(new(model.RevPackageRemoteUploadRecord)).
			Where("send_code=?", sendCode).
			Where("upload_method=?", model.RevPackageRemoteUploadMethod_SFTP).
			Where("upload_file=?", sftpSendFile.FileRelPath).
			Count(&total).Error
		if err != nil {
			f.logger.Errorw("count pkg document sftp remote upload failed", "sendCode", sendCode, "err", err)
			return err
		}
		if total > 0 {
			f.logger.Infow("pkg document sftp remote upload previous", "sendCode", sendCode)
			return fmt.Errorf("pkg document sftp remote upload done")
		}

		// 处理ZIP文件的特殊逻辑
		if strings.ToLower(filepath.Ext(sftpSendFilepath)) == ".zip" {
			if upToBlChainData != nil {
				personId := ""
				if len(upToBlChainData.PackageM.Cert.Data.FileDetail) > 0 {
					for _, fileDetail := range upToBlChainData.PackageM.Cert.Data.FileDetail {
						if fileDetail.FileName == filepath.Base(sftpSendFilepath) {
							personId = fileDetail.PersonId
							break
						}
					}
				}
				watermarkData := special.WatermarkData{
					PersonId:    personId,
					Operator:    "",
					Institution: upToBlChainData.Institution,
				}
				err = special.HandleHL7Zip(upToBlChainData.PackageDst, upToBlChainData.TempDir, upToBlChainData.PackageM, watermarkData, sftpSendFilepath, special.GetUnZipDir(sftpSendFilepath), true)
				if err != nil {
					f.logger.Errorw("handleHL7Zip failed", "err", err)
				}
			}
		}

		// 执行SFTP上传
		localFilepath := sftpSendFilepath
		remoteFilePath := filepath.Join(sysConfig.Sftp.Upload.DstDir, filepath.Base(sftpSendFilepath))
		f.logger.Debugw("pkg document sftp remote upload", "localFilepath", localFilepath, "remoteFilePath", remoteFilePath)

		uploadFileConfig := sftputil2.FileUploadConfig{
			LocalFilePath:  localFilepath,
			RemoteFilePath: remoteFilePath,
		}
		err = sftputil2.UploadFileToSftp(config, uploadFileConfig)
		if err != nil {
			f.logger.Errorw("pkg document sftp remote upload failed", "localFilepath", localFilepath, "remoteFilePath", remoteFilePath, "err", err)
			return err
		}

		// 记录上传成功
		uploadRecord := &model.RevPackageRemoteUploadRecord{
			Id:           core.GetNextId(),
			SendCode:     sendCode,
			UploadMethod: model.RevPackageRemoteUploadMethod_SFTP,
			UploadFile:   sftpSendFile.FileRelPath,
			UploadDetail: map[string]any{
				"uploadAuthConfig": config,
				"uploadFileConfig": uploadFileConfig,
			},
			UploadResult:   http.StatusOK,
			ResponseDetail: nil,
		}
		if err = f.orm.Create(uploadRecord).Error; err != nil {
			f.logger.Errorw("create pkg document sftp remote upload record failed", "sendCode", sendCode, "err", err)
			return err
		}
	}
	return nil
}

// handleDicomUpload 处理DICOM上传逻辑
func (f *messageHandler) handleDicomUpload(sysConfig *model.SysConfig, sendCode string, baseDirPath string, dcmRelPaths []string, upToBlChainData *UpToBlChainData) error {
	// 重置重试计数器
	if f.ipManager != nil {
		f.ipManager.ResetRetryCounters()
	}

	dicomTargets, err := f.buildDicomTargets(sysConfig)
	if err != nil {
		return err
	}

	if len(dicomTargets) == 0 {
		return fmt.Errorf("no valid dicom targets found")
	}

	var lastErr error
	for {
		for _, target := range dicomTargets {
			err := f.uploadDicomFiles(target, dcmRelPaths, sendCode, baseDirPath, upToBlChainData, sysConfig)
			if err == nil {
				f.logger.Infow("dicom upload successful", "target", target)
				return nil
			}
			lastErr = err
			f.logger.Warnw("dicom upload to one target failed", "target", target, "error", err)

			// 如果是IP模式且有IP管理器，尝试切换到下一个可用IP
			if sysConfig.Dicom.Auth.ResolveType == "ip" && f.ipManager != nil {
				nextIP, switchErr := f.ipManager.SwitchToNextDicomIP(target.ServerIp)
				if switchErr != nil {
					f.logger.Warnw("failed to switch to next dicom ip", "error", switchErr)
					return fmt.Errorf("all dicom upload attempts failed: %w", lastErr)
				} else {
					f.logger.Infow("switched to next dicom ip", "from", target.ServerIp, "to", nextIP.ServerIp)
					// 重新构建目标列表，使用新的活跃IP
					dicomTargets = []DicomTarget{{
						ServerAet:  nextIP.ServerAet,
						ServerIp:   nextIP.ServerIp,
						ServerPort: uint16(nextIP.ServerPort),
					}}
					f.logger.Infow("updated dicom targets with new active ip", "host", nextIP.ServerIp)
					break // 跳出内层循环，使用新IP重试
				}
			} else {
				// 非IP模式，直接返回错误
				return fmt.Errorf("dicom upload failed: %w", lastErr)
			}
		}
	}
}

// DicomTarget DICOM目标结构
type DicomTarget struct {
	ServerAet  string
	ServerIp   string
	ServerPort uint16
}

// buildDicomTargets 构建DICOM目标列表
func (f *messageHandler) buildDicomTargets(sysConfig *model.SysConfig) ([]DicomTarget, error) {
	var dicomTargets []DicomTarget

	if sysConfig.Dicom.Auth.ResolveType == "ip" && len(sysConfig.Dicom.Auth.IpConfigs) > 0 {
		// 使用IP管理器确保有活跃的IP
		if f.ipManager != nil {
			if err := f.ipManager.EnsureActiveIPExists(); err != nil {
				f.logger.Warnw("failed to ensure active ip exists", "error", err)
			}
		}

		// 优先使用活跃的IP，如果没有则使用所有IP
		activeIP, err := f.getActiveDicomIP(sysConfig)
		if err == nil && activeIP != nil {
			// 只使用活跃的IP
			dicomTargets = append(dicomTargets, DicomTarget{
				ServerAet:  activeIP.ServerAet,
				ServerIp:   activeIP.ServerIp,
				ServerPort: uint16(activeIP.ServerPort),
			})
		} else {
			// 如果没有活跃IP或获取失败，使用所有IP
			for _, ipConfig := range sysConfig.Dicom.Auth.IpConfigs {
				dicomTargets = append(dicomTargets, DicomTarget{
					ServerAet:  ipConfig.ServerAet,
					ServerIp:   ipConfig.ServerIp,
					ServerPort: uint16(ipConfig.ServerPort),
				})
			}
		}
	} else {
		serverIp, err := dnsutil.ParseIp(sysConfig.Dicom.Auth.ServerIp, sysConfig.Dicom.Auth.DnsServer)
		if err != nil {
			f.logger.Errorw("parse ip error", "error", err)
			return nil, err
		}
		dicomTargets = append(dicomTargets, DicomTarget{
			ServerAet:  sysConfig.Dicom.Auth.ServerAet,
			ServerIp:   serverIp,
			ServerPort: sysConfig.Dicom.Auth.ServerPort,
		})
	}

	return dicomTargets, nil
}

// uploadDicomFiles 上传DICOM文件
func (f *messageHandler) uploadDicomFiles(target DicomTarget, dcmRelPaths []string, sendCode string, baseDirPath string, upToBlChainData *UpToBlChainData, sysConfig *model.SysConfig) error {
	dcmCount := len(dcmRelPaths)
	batchNum := 20
	threadNum := dcmCount / batchNum
	if dcmCount%batchNum != 0 {
		threadNum += 1
	}

	wg := errgroup.Group{}
	for i := 0; i < threadNum; i++ {
		start, end := i*batchNum, i*batchNum+batchNum
		if end > dcmCount {
			end = dcmCount
		}
		_dcmRelPaths := dcmRelPaths[start:end]
		wg.Go(func() error {
			return f.uploadDicomBatch(target, _dcmRelPaths, sendCode, baseDirPath, upToBlChainData, sysConfig)
		})
	}
	return wg.Wait()
}

// uploadDicomBatch 批量上传DICOM文件
func (f *messageHandler) uploadDicomBatch(target DicomTarget, dcmRelPaths []string, sendCode string, baseDirPath string, upToBlChainData *UpToBlChainData, sysConfig *model.SysConfig) error {
	for _, dcmRelPath := range dcmRelPaths {
		dcmPath := filepath.Join(baseDirPath, dcmRelPath)
		filestat, err := os.Stat(dcmPath)
		if err != nil {
			f.logger.Errorw("stat dcm file failed", "error", err, "file", dcmPath)
			return err
		}
		if filestat.IsDir() {
			f.logger.Warnw("dcm file is directory", "file", dcmPath)
			continue
		}

		// 检查是否已经上传过
		total := int64(0)
		err = f.orm.Model(new(model.RevPackageRemoteUploadRecord)).
			Where("send_code=?", sendCode).
			Where("upload_method=?", model.RevPackageRemoteUploadMethod_DICOM).
			Where("upload_file=?", dcmRelPath).
			Count(&total).Error
		if err != nil {
			f.logger.Errorw("count pkg document dicom remote upload failed", "sendCode", sendCode, "err", err)
			return err
		}
		if total > 0 {
			f.logger.Infow("pkg document dicom remote upload previous", "sendCode", sendCode)
			continue
		}

		// 处理DCM文件的特殊逻辑
		if upToBlChainData != nil {
			personId := ""
			if len(upToBlChainData.PackageM.Cert.Data.FileDetail) > 0 {
				for _, fileDetail := range upToBlChainData.PackageM.Cert.Data.FileDetail {
					if fileDetail.FileName == filepath.Base(dcmPath) {
						personId = fileDetail.PersonId
						break
					}
				}
			}
			watermarkData := special.WatermarkData{
				PersonId:    personId,
				Operator:    "",
				Institution: upToBlChainData.Institution,
			}
			err = special.HandleDcmFile(upToBlChainData.PackageDst, upToBlChainData.TempDir, upToBlChainData.PackageM, watermarkData, dcmPath)
			if err != nil {
				f.logger.Errorw("handle dcmFile failed", "err", err)
			}
		}

		// 准备上传数据
		dataReader := bytes.NewBuffer(nil)
		w := multipart.NewWriter(dataReader)

		fw, err := w.CreateFormFile("files", filepath.Base(dcmPath))
		if err != nil {
			f.logger.Errorw("create form file failed", "err", err, "file", dcmPath)
			continue // 文件处理错误，跳过这个文件，继续处理下一个
		}
		gf, err := os.Open(dcmPath)
		if err != nil {
			f.logger.Errorw("open dcm file failed", "err", err, "file", dcmPath)
			continue // 文件打开失败，跳过这个文件
		}
		if _, err := io.Copy(fw, gf); err != nil {
			f.logger.Errorw("copy dcm file failed", "err", err, "file", dcmPath)
			gf.Close()
			continue // 文件复制失败，跳过这个文件
		}
		if err = gf.Close(); err != nil {
			f.logger.Errorw("close dcm file failed", "err", err, "file", dcmPath)
			continue // 文件关闭失败，跳过这个文件
		}

		if err = w.WriteField("server_aet", target.ServerAet); err != nil {
			f.logger.Errorw("write server_aet field failed", "err", err)
			continue // 表单字段写入失败，跳过这个文件
		}
		if err = w.WriteField("server_ip", target.ServerIp); err != nil {
			f.logger.Errorw("write server_ip field failed", "err", err)
			continue // 表单字段写入失败，跳过这个文件
		}
		if err = w.WriteField("server_port", fmt.Sprintf("%d", target.ServerPort)); err != nil {
			f.logger.Errorw("write server_port field failed", "err", err)
			continue // 表单字段写入失败，跳过这个文件
		}

		if err = w.Close(); err != nil {
			f.logger.Errorw("close multipart writer failed", "err", err)
			continue // 表单关闭失败，跳过这个文件
		}

		// 执行HTTP上传
		req, err := http.NewRequest(http.MethodPost, sysConfig.Dicom.Upload.DstServer, dataReader)
		if err != nil {
			f.logger.Errorln(err)
			continue
		}
		req.Header.Set("Content-Type", w.FormDataContentType())

		resp, err := http.DefaultClient.Do(req)
		if err != nil {
			f.logger.Errorw("pkg document dicom remote upload failed", "sendCode", sendCode, "err", err)
			return err // 返回错误以触发IP切换
		}
		defer resp.Body.Close()

		respBody, err := io.ReadAll(resp.Body)
		if err != nil {
			f.logger.Errorw("read dicom upload response failed", "sendCode", sendCode, "err", err)
			return err // 返回错误以触发IP切换
		}

		// 检查HTTP状态码
		if resp.StatusCode != http.StatusOK {
			f.logger.Errorw("dicom upload failed with status", "sendCode", sendCode, "status", resp.Status, "body", string(respBody))
			return fmt.Errorf("dicom upload failed with status: %s", resp.Status)
		}

		// 记录上传成功
		uploadRecord := &model.RevPackageRemoteUploadRecord{
			Id:           core.GetNextId(),
			SendCode:     sendCode,
			UploadMethod: model.RevPackageRemoteUploadMethod_DICOM,
			UploadFile:   dcmRelPath,
			UploadDetail: map[string]any{
				"server_aet":    target.ServerAet,
				"server_ip":     target.ServerIp,
				"server_port":   target.ServerPort,
				"localFilepath": dcmPath,
			},
			UploadResult:   http.StatusOK,
			ResponseDetail: string(respBody),
		}
		if err = f.orm.Create(uploadRecord).Error; err != nil {
			f.logger.Errorw("create pkg document dicom remote upload record failed", "sendCode", sendCode, "err", err)
		}
	}
	return nil
}
