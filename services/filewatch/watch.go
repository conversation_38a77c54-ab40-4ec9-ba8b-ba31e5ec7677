package filewatch

import (
	"code.ixdev.cn/cnix/cbdv/hk-box-be/core"
	"errors"
	"fmt"
	"github.com/fsnotify/fsnotify"
	"io/fs"
	"log"
	"os"
	"path/filepath"
	"regexp"
)

func WatchFile() {
	logger, err := core.GetLogger()
	if err != nil {
		log.Fatalln(err)
	}
	orm, err := core.GetDatabase()
	if err != nil {
		log.Fatalln(err)
	}
	dst := core.Container.Conf.App.Rev.Fisco.Tars.Src
	fp, err := filepath.Abs(dst)
	if err != nil {
		log.Fatalln(err)
	}
	if _, err := os.Stat(fp); err != nil {
		if errors.Is(err, fs.ErrNotExist) {
			_ = os.MkdirAll(dst, os.ModePerm)
		} else {
			log.Fatalln(err)
		}
	}
	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		log.Fatalln(err)
	}
	defer watcher.Close()

	// Add a path.
	err = watcher.AddWith(fp)
	if err != nil {
		log.Fatalln(err)
	}

	//kafkaProducer, err := core.GetKafkaProducer()
	//if err != nil {
	//	log.Fatalln(err)
	//}

	revHandler := newMessageHandler(orm, logger)

	for {
		select {
		case event, ok := <-watcher.Events:
			if !ok {
				return
			}

			fmt.Println("================================================================================")
			if event.Has(fsnotify.Create) {
				matched := regexp.MustCompile("\"(.+?)\" ← \"(.+?)\"").FindStringSubmatch(event.String())
				if len(matched) > 0 { // 完成重命名文件（在监测范围内重命名）
					nFilename, oFilename := matched[1], matched[2]
					logger.Infof("mv %s to %s\n", oFilename, nFilename)
					revHandler.handle(fsnotify.Rename, oFilename, nFilename)
				} else { // 创建文件，此时有两种情况，一是从监测范围外移动到监测范围内（只触发create事件，不触发write事件），另一种是直接在监测范围内创建文件，此时还未完成内容写入（写入后还会触发write事件，且可能会触发多次）
					logger.Infof("create %s\n", event.Name)
					revHandler.handle(fsnotify.Create, "", event.Name)
				}
			} else if event.Has(fsnotify.Rename) { // 重命名文件（无论移动至监测范围内外都会触发该事件），由于把文件移动到监测范围之外不会有create事件，所以此处将rename事件视为删除
				logger.Infof("rm(rename) %s\n", event.Name)
				revHandler.handle(fsnotify.Remove, event.Name, "")
			} else if event.Has(fsnotify.Write) { // 新建文件并已写入，但不代表已写入完成
				logger.Infof("write %s\n", event.Name)
				revHandler.handle(fsnotify.Write, "", event.Name)
			} else if event.Has(fsnotify.Remove) {
				logger.Infof("rm %s\n", event.Name)
				revHandler.handle(fsnotify.Remove, event.Name, "")
			} else {
				logger.Infow("fsnotify", "event", event)
			}
			//if message != nil && !kafkaProducer.IsClosed() {
			//	messageB, err := json.Marshal(message)
			//	if err != nil {
			//		logger.Error(err)
			//		continue
			//	}
			//	err = kafkaProducer.Produce(&kafka.Message{
			//		TopicPartition: kafka.TopicPartition{
			//			Topic:     pointer.String(global.KafkaTopicFileWatch),
			//			Partition: kafka.PartitionAny,
			//		},
			//		Value:         messageB,
			//		Key:           nil,
			//		Timestamp:     time.Time{},
			//		TimestampType: 0,
			//		Opaque:        nil,
			//		Headers:       nil,
			//	}, nil)
			//	if err != nil {
			//		logger.Error(err)
			//	}
			//}
		case err, ok := <-watcher.Errors:
			if !ok {
				return
			}
			log.Println("error:", err)
		}
	}
}
