package ipmanager

import (
	"fmt"
	"sync"

	"code.ixdev.cn/cnix/cbdv/hk-box-be/core"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/model"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// IPManager IP状态管理器
type IPManager struct {
	logger *zap.SugaredLogger
	orm    *gorm.DB
	mutex  sync.RWMutex

	// 重试计数器
	sftpRetryRound  int // SFTP当前重试轮次
	dicomRetryRound int // DICOM当前重试轮次
}

// NewIPManager 创建IP管理器
func NewIPManager() (*IPManager, error) {
	logger, err := core.GetLogger()
	if err != nil {
		return nil, fmt.Errorf("get logger failed: %v", err)
	}

	orm, err := core.GetDatabase()
	if err != nil {
		return nil, fmt.Errorf("get database failed: %v", err)
	}

	return &IPManager{
		logger: logger,
		orm:    orm,
		mutex:  sync.RWMutex{},
	}, nil
}

// GetActiveSftpIP 获取当前活跃的SFTP IP配置
func (m *IPManager) GetActiveSftpIP() (*model.SftpIpConfig, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	sysConfig := new(model.SysConfig)
	if err := m.orm.Where("id=?", model.SysConfigKey).First(sysConfig).Error; err != nil {
		return nil, fmt.Errorf("get sys config failed: %v", err)
	}

	if sysConfig.Sftp == nil || len(sysConfig.Sftp.Auth.IpConfigs) == 0 {
		return nil, fmt.Errorf("no sftp ip configs found")
	}

	for _, ipConfig := range sysConfig.Sftp.Auth.IpConfigs {
		if ipConfig.Active {
			return &ipConfig, nil
		}
	}

	// 如果没有找到活跃的IP，返回第一个
	return &sysConfig.Sftp.Auth.IpConfigs[0], nil
}

// GetActiveDicomIP 获取当前活跃的DICOM IP配置
func (m *IPManager) GetActiveDicomIP() (*model.DicomIpConfig, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	sysConfig := new(model.SysConfig)
	if err := m.orm.Where("id=?", model.SysConfigKey).First(sysConfig).Error; err != nil {
		return nil, fmt.Errorf("get sys config failed: %v", err)
	}

	if sysConfig.Dicom == nil || len(sysConfig.Dicom.Auth.IpConfigs) == 0 {
		return nil, fmt.Errorf("no dicom ip configs found")
	}

	for _, ipConfig := range sysConfig.Dicom.Auth.IpConfigs {
		if ipConfig.Active {
			return &ipConfig, nil
		}
	}

	// 如果没有找到活跃的IP，返回第一个
	return &sysConfig.Dicom.Auth.IpConfigs[0], nil
}

// SwitchToNextSftpIP 切换到下一个可用的SFTP IP
func (m *IPManager) SwitchToNextSftpIP(currentHost string) (*model.SftpIpConfig, error) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	sysConfig := new(model.SysConfig)
	if err := m.orm.Where("id=?", model.SysConfigKey).First(sysConfig).Error; err != nil {
		return nil, fmt.Errorf("get sys config failed: %v", err)
	}

	if sysConfig.Sftp == nil || len(sysConfig.Sftp.Auth.IpConfigs) == 0 {
		return nil, fmt.Errorf("no sftp ip configs found")
	}

	// 找到当前IP的索引
	currentIndex := -1
	for i, ipConfig := range sysConfig.Sftp.Auth.IpConfigs {
		if ipConfig.Host == currentHost {
			currentIndex = i
			break
		}
	}

	if currentIndex == -1 {
		return nil, fmt.Errorf("current host not found in ip configs")
	}

	// 切换到下一个IP
	nextIndex := (currentIndex + 1) % len(sysConfig.Sftp.Auth.IpConfigs)

	// 如果回到了第一个IP，说明一轮尝试完成，增加重试轮次
	if nextIndex == 0 {
		m.sftpRetryRound++
		m.logger.Infow("sftp ip list completed one round", "round", m.sftpRetryRound)

		// 如果已经重试了3轮，返回错误
		if m.sftpRetryRound >= 3 {
			m.sftpRetryRound = 0 // 重置计数器
			return nil, fmt.Errorf("no available sftp ip found after 3 rounds")
		}
	}

	nextIP := &sysConfig.Sftp.Auth.IpConfigs[nextIndex]

	// 更新active状态
	for i := range sysConfig.Sftp.Auth.IpConfigs {
		sysConfig.Sftp.Auth.IpConfigs[i].Active = (i == nextIndex)
	}

	// 保存到数据库
	if err := m.orm.Model(sysConfig).Select("sftp").Updates(sysConfig).Error; err != nil {
		return nil, fmt.Errorf("update sftp config failed: %v", err)
	}

	m.logger.Infow("switched to next sftp ip",
		"from", currentHost,
		"to", nextIP.Host,
		"round", m.sftpRetryRound+1,
		"index", nextIndex)

	return nextIP, nil
}

// SwitchToNextDicomIP 切换到下一个可用的DICOM IP
func (m *IPManager) SwitchToNextDicomIP(currentHost string) (*model.DicomIpConfig, error) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	sysConfig := new(model.SysConfig)
	if err := m.orm.Where("id=?", model.SysConfigKey).First(sysConfig).Error; err != nil {
		return nil, fmt.Errorf("get sys config failed: %v", err)
	}

	if sysConfig.Dicom == nil || len(sysConfig.Dicom.Auth.IpConfigs) == 0 {
		return nil, fmt.Errorf("no dicom ip configs found")
	}

	// 找到当前IP的索引
	currentIndex := -1
	for i, ipConfig := range sysConfig.Dicom.Auth.IpConfigs {
		if ipConfig.ServerIp == currentHost {
			currentIndex = i
			break
		}
	}

	if currentIndex == -1 {
		return nil, fmt.Errorf("current host not found in ip configs")
	}

	// 切换到下一个IP
	nextIndex := (currentIndex + 1) % len(sysConfig.Dicom.Auth.IpConfigs)

	// 如果回到了第一个IP，说明一轮尝试完成，增加重试轮次
	if nextIndex == 0 {
		m.dicomRetryRound++
		m.logger.Infow("dicom ip list completed one round", "round", m.dicomRetryRound)

		// 如果已经重试了3轮，返回错误
		if m.dicomRetryRound >= 3 {
			m.dicomRetryRound = 0 // 重置计数器
			return nil, fmt.Errorf("no available dicom ip found after 3 rounds")
		}
	}

	nextIP := &sysConfig.Dicom.Auth.IpConfigs[nextIndex]

	// 更新active状态
	for i := range sysConfig.Dicom.Auth.IpConfigs {
		sysConfig.Dicom.Auth.IpConfigs[i].Active = (i == nextIndex)
	}

	// 保存到数据库
	if err := m.orm.Model(sysConfig).Select("dicom").Updates(sysConfig).Error; err != nil {
		return nil, fmt.Errorf("update dicom config failed: %v", err)
	}

	m.logger.Infow("switched to next dicom ip",
		"from", currentHost,
		"to", nextIP.ServerIp,
		"round", m.dicomRetryRound+1,
		"index", nextIndex)

	return nextIP, nil
}

// ResetRetryCounters 重置重试计数器
func (m *IPManager) ResetRetryCounters() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	m.sftpRetryRound = 0
	m.dicomRetryRound = 0
	m.logger.Debugw("reset retry counters")
}

// EnsureActiveIPExists 确保存在活跃的IP配置
func (m *IPManager) EnsureActiveIPExists() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	sysConfig := new(model.SysConfig)
	if err := m.orm.Where("id=?", model.SysConfigKey).First(sysConfig).Error; err != nil {
		return fmt.Errorf("get sys config failed: %v", err)
	}

	updated := false

	// 检查SFTP配置
	if sysConfig.Sftp != nil && len(sysConfig.Sftp.Auth.IpConfigs) > 0 {
		hasActiveSftp := false
		for _, ipConfig := range sysConfig.Sftp.Auth.IpConfigs {
			if ipConfig.Active {
				hasActiveSftp = true
				break
			}
		}
		if !hasActiveSftp {
			sysConfig.Sftp.Auth.IpConfigs[0].Active = true
			updated = true
			m.logger.Infow("set first sftp ip as active", "host", sysConfig.Sftp.Auth.IpConfigs[0].Host)
		}
	}

	// 检查DICOM配置
	if sysConfig.Dicom != nil && len(sysConfig.Dicom.Auth.IpConfigs) > 0 {
		hasActiveDicom := false
		for _, ipConfig := range sysConfig.Dicom.Auth.IpConfigs {
			if ipConfig.Active {
				hasActiveDicom = true
				break
			}
		}
		if !hasActiveDicom {
			sysConfig.Dicom.Auth.IpConfigs[0].Active = true
			updated = true
			m.logger.Infow("set first dicom ip as active", "host", sysConfig.Dicom.Auth.IpConfigs[0].ServerIp)
		}
	}

	if updated {
		if err := m.orm.Model(sysConfig).Select("sftp", "dicom").Updates(sysConfig).Error; err != nil {
			return fmt.Errorf("update config failed: %v", err)
		}
	}

	return nil
}

// GetAllSftpIPs 获取所有SFTP IP配置
func (m *IPManager) GetAllSftpIPs() ([]model.SftpIpConfig, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	sysConfig := new(model.SysConfig)
	if err := m.orm.Where("id=?", model.SysConfigKey).First(sysConfig).Error; err != nil {
		return nil, fmt.Errorf("get sys config failed: %v", err)
	}

	if sysConfig.Sftp == nil {
		return []model.SftpIpConfig{}, nil
	}

	return sysConfig.Sftp.Auth.IpConfigs, nil
}

// GetAllDicomIPs 获取所有DICOM IP配置
func (m *IPManager) GetAllDicomIPs() ([]model.DicomIpConfig, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	sysConfig := new(model.SysConfig)
	if err := m.orm.Where("id=?", model.SysConfigKey).First(sysConfig).Error; err != nil {
		return nil, fmt.Errorf("get sys config failed: %v", err)
	}

	if sysConfig.Dicom == nil {
		return []model.DicomIpConfig{}, nil
	}

	return sysConfig.Dicom.Auth.IpConfigs, nil
}
