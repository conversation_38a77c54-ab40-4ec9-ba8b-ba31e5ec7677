# 部署

### 基础准备

1. 安装最新版Docker以及docker-compose插件
2. 在宿主机上创建目录 `mkdir /data`
3. 复制整个 `deploy/docker` 目录到宿主机的 `/data` 目录下`
4. 进入`/data/docker` 目录
5. 调整宿主机配置，执行 `sysctl -w vm.max_map_count=262144` 或更大，否则可能不足以支撑 elasticsearch 索引数据
6. 调整宿主机配置，执行 `sysctl fs.inotify.max_user_watches=124983` 和 `sysctl fs.inotify.max_user_instances=128` 或更大，因为本程序会使用系统通知监听文件变化

### 一、创建容器网络

```shell
docker network create cnix
```
若非 `cnix` 命名，需要修改所有的 docker-compose.yml 文件中的 `networks: cnix`

### 二、部署基础设施依赖

前置说明：基础设置部分包括四个组件： `elasticsearch`、`redis`、`mysql`、`kafka`

组件介绍：
1. `elasticsearch`：用于高效查询，主要存储收到的tar结构，和MySQL中的hk_box.rev_packages表对应，未手动维护，而是通过 `gorm` 框架 `hook` 机制同步到 elasticsearch
    ，若启动失败，则可能出现了容器和宿主机目录权限冲突，删除 docker/infra/docker-compose.yml 中 elasticsearch 部分 volume 中的 data 目录映射，es 会自动创建存储卷。
2. 其它组件应没有问题

启动：进入 `docker/infra` 目录，执行 `docker-compose up -d` 启动所有依赖，等待数十秒，确认所有依赖启动成功。

### 三、部署cb-gateway-sdk-bridge [可选]

介绍：该服务实际上是一个 http服务 + grpc 服务，内部集成了区块链网关提供的 java sdk，用于跳过`区块链网关（cb-gateway:5013端口）`的验证，需要预先配置。该服务并非必要，若其他地方已经有该服务，可以直接使用
部署：
1. 进入 `docker/cb-gateway-sdk-bridge` 目录 
2. 创建 mysql cb_gateway 数据库 并将 `20250311.sql` 导入
3. 启动：执行 `docker-compose up -d`，查看容器日志确认启动成功

### 四、部署hk-box [必选]

前置设置：进入 mysql 并创建 `hk_box` 数据库，导入 `deploy/sql` 目录下的所有sql文件

1. 进入 `docker/hk-box` 目录
2. 修改 `conf.yaml` 中 `app.cookie.domain` 为实际域名或IP
3. 修改 `conf.yaml` 中 `app.sender.upload.remote_url` 替换 `[宿主机IP]` 为实际IP，该地址由恒安提供服务，恒安的30002端口服务部署在裸机上。
4. 修改 `conf.yaml` 中 `app.rev.fisco.sign_server_url` 为实际的 `cb-gateway-sdk-bridge` 地址，如果第三步部署了 `cb-gateway-sdk-bridge` 则无需修改
5. 修改 `conf.yaml` 中 `app.rev.fisco.watermark_api.addr` 替换 `[宿主机IP]` 为实际IP，该地址由恒安提供服务，恒安的30002端口服务部署在裸机上。！！！注意！！！ `app.rev.fisco.watermark_api.temp_dir` 非必要不要修改
   ，否则可能出现目录权限问题，该目录为 恒安与 本服务 共享的目录，需要双方同时具有完整的读写权限，若修改为非默认值，请确保该目录在宿主机和本服务中具有完全的读写权限，否则将可能需要修改 `Dockerfile` 和 `entrypoint.sh` 设置权限。
6. 修改 `conf.yaml` 中 `app.platform.report_url` 替换 `[宿主机IP]` 为实际IP，该地址由恒安提供服务，恒安的80端口服务部署在裸机上。
7. 修改 `conf.yaml` 中 `app.platform.local_ip` 替换为本服务实际可以访问到的域名和IP，在向 `app.platform.report_url` 配置的地址发送请求时会发送本IP，恒安会回调该地址 。 
8. 修改 `conf.yaml` 中 `app.platform.local_port` 替换为本服务实际可以访问到的端口，在向 `app.platform.report_url` 配置的地址发送请求时会发送本端口，恒安会回调该端口。
9. 若部署 `infra` 时使用了默认配置，其它部分应当无需修改
10. 若服务正常启动，但无法访问web页面，通常是出现了跨域问题，修改 `conf.yaml` 中 `app.cors.allowed_origins` 添加本服务真正运行的域名或IP，如有端口需加端口号。

启动：执行 `docker-compose up -d` 启动服务，访问 `http://[宿主机IP]:8080` 确认启动成功