services:
  hk_box:
    container_name: hk_box
    image: cr.ixdev.cn/cnix/cbdv/hk-box-be:feature-v1.3-a178f34a
    user: "${UID}:${GID}" # 先导出 export GID=$(id -g)
    ports:
      - "8080:80"
    volumes:
      - ./resources:/app/resources
      - ./conf.yaml:/app/conf.yaml
      - /watermark:/watermark
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 4G
        reservations:
          cpus: '0.25'
          memory: 256M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "10"
    networks:
      - cnix

networks:
  cnix:
    external: true
