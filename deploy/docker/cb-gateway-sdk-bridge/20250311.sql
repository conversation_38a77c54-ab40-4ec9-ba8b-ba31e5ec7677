-- auto-generated definition
create table cb_webhooks
(
    id              bigint auto_increment comment '主键id'
        primary key,
    we_id           varchar(255) default '' not null comment '机构id',
    uri             varchar(255) default '' not null comment 'uri地址',
    custom_headers  json                    null comment '自定义标头',
    encrypt_enabled tinyint(1)   default 0  not null comment '是否启用加密,1启用0禁用',
    encrypt_key     varchar(32)             null comment '加密密钥',
    notify_retries  int          default 0  not null comment '最大重试通知次数',
    retry_delay     int          default 0  not null comment '重试通知间隔，单位：秒',
    trigger_sources json                    not null comment '触发来源',
    subscribed      json                    null comment '订阅的weid列表',
    created_at      datetime(3)             not null comment '创建时间',
    updated_at      datetime(3)             not null comment '更新时间'
)
    comment 'webhook' collate = utf8mb4_general_ci;

create index idx_weid
    on cb_webhooks (we_id);


