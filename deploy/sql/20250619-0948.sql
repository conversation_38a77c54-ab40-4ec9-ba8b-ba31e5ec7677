create table rev_package_remote_upload_record
(
    id              bigint       not null primary key comment '主键id',
    send_code       varchar(255) not null default '' comment '批次号',
    upload_method   tinyint      not null default 0 comment '上传方式,1sftp,2dicom',
    upload_detail   json         not null comment '上传内容详情',
    upload_result   int          not null default 0 comment '上传结果',
    response_detail json         null comment '响应详情',
    created_at      datetime(3)  not null comment '创建时间',
    updated_at      datetime(3)  not null comment '更新时间'
) comment '接收包-远程上传';

create unique index idx_send_code_upload_method on rev_package_remote_upload_record (send_code, upload_method);

alter table sys_config
    add column dicom json null comment 'dicom配置' after sender_up_wm_enabled,
    add column sftp  json null comment 'sftp配置' after dicom;

alter table rev_packages add column dcm_path varchar(255) not null default '' comment 'dcm file relative path' after cert;
