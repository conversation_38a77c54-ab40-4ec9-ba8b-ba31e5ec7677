{"name": "hk-box-fe", "version": "0.1.0", "private": true, "dependencies": {"antd": "^5.22.3", "axios": "^1.7.9", "clipboard": "^2.0.11", "echarts": "^5.6.0", "i18next": "^25.2.0", "immer": "^10.1.1", "js-cookie": "^3.0.5", "js-sha256": "^0.11.0", "lodash": "^4.17.21", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^15.5.1", "react-router-dom": "^7.0.2", "react-scripts": "5.0.1", "simplebar-react": "^3.2.6", "use-immer": "^0.10.0"}, "scripts": {"start": "BROWSER=none craco start", "build": "craco build", "test": "craco test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@craco/craco": "^7.1.0", "sass": "^1.82.0", "tailwindcss": "^3.4.16"}}