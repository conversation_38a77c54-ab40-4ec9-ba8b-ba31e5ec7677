ARG react_app_api_server_url

FROM node:20-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN cp -a /etc/apk/repositories /etc/apk/repositories.bak \
    && sed -i 's/dl-cdn.alpinelinux.org/mirrors.tuna.tsinghua.edu.cn/g' /etc/apk/repositories \
    && apk update \
    && apk add --no-cache libc6-compat

WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json yarn.lock* package-lock.json* pnpm-lock.yaml* ./
RUN npm config set registry https://registry.npmmirror.com/ && npm install


# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

ARG react_app_api_server_url
ENV REACT_APP_API_SERVER_URL=${react_app_api_server_url}

ENV NODE_ENV=production

RUN npm run build

# If using npm comment out above and use below instead
#RUN npm run build

# Production image, copy all the files and run next
FROM nginx:alpine AS runner

COPY --from=builder /app/nginx.conf /etc/nginx/conf.d/default.conf
COPY --from=builder /app/build /usr/share/nginx/html/site
