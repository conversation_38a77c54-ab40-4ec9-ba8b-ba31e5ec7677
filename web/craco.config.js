const {resolve} = require("path");

module.exports = {
    webpack: {
        configure: (webpackConfig, {env, paths}) => {
            Object.assign(webpackConfig.resolve, {
                alias: {
                    '@': resolve(__dirname, './src'),
                }
            })
            return webpackConfig;
        },
    },
    babel: {
        // presets: [ /* ... */],
        // plugins: [
        //     [
        //         'babel-plugin-import',
        //         {
        //             libraryName: '@arco-design/web-react',
        //             libraryDirectory: 'es',
        //             camel2DashComponentName: false,
        //             style: true, // 样式按需加载
        //         },
        //     ],
        // ],
        loaderOptions: { /* ... */},
        // loaderOptions: (babelLoaderOptions, { env, paths }) => {
        //     /* ... */
        //     return babelLoaderOptions;
        // },
    },
    plugins: [
        // {
        //     plugin: require('craco-less'),
        //     options: {
        //         noIeCompat: true,
        //         lessLoaderOptions: {
        //             lessOptions: {
        //                 modifyVars: {
        //                     prefix: "rise",
        //                     "arco-vars-prefix": "rise",
        //                     'table-size-default-font-size': '13px',
        //                     'btn-size-default-font-size': '13px',
        //                     'link-font-size': '13px',
        //                     // "menu-dark-color-bg": "#000000"
        //                 },
        //                 javascriptEnabled: true,
        //             }
        //         }
        //     },
        // },
    ],
    style: {
        sass: {
            loaderOptions: (sassLoaderOptions, { env, paths }) => {
                /* ... */
                return {
                    ...sassLoaderOptions,
                    api: "modern"
                };
            },
        },
    }
};
