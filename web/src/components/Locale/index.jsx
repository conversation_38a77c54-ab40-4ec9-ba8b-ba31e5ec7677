import styles from './index.module.scss'
import {useTranslation} from "react-i18next";
import {Select} from "antd";
import {useContext, useEffect, useState} from "react";
import {GlobalContext} from "@/provider/global";
import Cookie from "js-cookie";

const cookieKey = 'cnix_hk_box_locale'
export default function ({className, variant}) {
    const { t, i18n } = useTranslation();
    const globalContext = useContext(GlobalContext)

    const locales = [
        {  value: 'zh_CN', label: t('simplified chinese')},
        {  value: 'zh_HK', label: t('traditional chinese')}
    ]

    const [selectedLocale, setSelectedLocale] = useState('zh_CN')

    const handleChange = (value) => {
        setSelectedLocale(value)
        i18n.changeLanguage(value)
        globalContext.changeLocale(value)
        document.title = t('app sub name')
        Cookie.set(cookieKey, value)
    }

    useEffect(()=>{
        const value = Cookie.get(cookieKey) || 'zh_CN'
        handleChange(value)
    },[])

    return (
        <div className={styles.wrapper+' component-locale'}>
            <div className={'wrapper-inner'}>
                <Select
                    variant={variant}
                    style={{ width: 120 }}
                    value={selectedLocale}
                    onChange={handleChange}
                    options={locales}
                />
            </div>
        </div>
    )
}