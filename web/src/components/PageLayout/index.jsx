import styles from './index.module.scss'
import {forwardRef, useImperativeHandle, useRef} from "react";

function PageLayout({children, header, headerTop = null, search, action, actionExtra, extra, embed = false}, ref) {
    const containerRef = useRef()
    // const scrollToTop = top=>{
    //     containerRef.current.scrollTo({
    //         top,
    //         behavior: "smooth"
    //     })
    // }
    // const scrollTo = obj=>{
    //     containerRef.current.scrollTo(obj)
    // }
    //
    // const getScrollTop = ()=>{
    //     return containerRef.current.scrollTop
    // }
    //
    useImperativeHandle(ref, () => {
        // return {
        //     scrollToTop: scrollToTop,
        //     scrollTo: scrollTo,
        //     getScrollTop: getScrollTop,
        // }
        return containerRef.current;
    }, [])
    //
    return (
        <div className={"page-layout " + styles.wrapper + (embed ? ' embed' : '')}>
            {header ? (<div className={"page-header"}>{header}</div>) : null}
            <div ref={containerRef} className={"page-container"}>
                <div className={"container-inner"}>
                    {headerTop? (<div className={"container-top"}>{headerTop}</div>):null}
                    {(search || action || actionExtra) ? (
                        <div className={"container-header"}>
                            {search ? (<div className={"container-search"}>{search}</div>) : null}
                            <div className={"container-action"}>
                                <div className={"container-action__main"}>{action}</div>
                                <div className={"container-action__extra"}>{actionExtra}</div>
                            </div>
                        </div>
                    ) : null}
                    <div className={"container-body"}>{children}</div>
                </div>
            </div>
            {extra ? (<div className={"page-extra"}>{extra}</div>) : null}
        </div>
    )
}

export default forwardRef(PageLayout)