.wrapper:global{
  height: 100%;
  background-color: #f5f5f5;
  //overflow: hidden auto;
  ///* background-color: #fff; */
  //padding: 12px;
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
  >.page-header{
    padding: 15px 24px;
    background-color: #fff;
    box-shadow: 0 0 2px #ccc;
    z-index: 999;
    font-size: 16px;
    font-weight: bold;
  }
  >.page-container{
    flex: 1;
    background-color: #f5f5f5;
    padding: 12px;
    overflow: hidden auto;
    >.container-inner{
      background-color: #fff;
      min-height: 100%;
      >.container-header{
        padding: 24px 36px 6px;
        >.container-search{
          display: flex;
          border-bottom: 1px solid #eaeaea;
          padding-bottom: 18px;
          margin-bottom: 18px;
          >.ant-form{
            flex: 1;
          }
          >.ant-divider{
            height: auto;
            margin: 0 12px;
          }
          >.actions{
            display: flex;
            flex-direction: column;
            justify-content: space-between;
          }
        }
        >.container-action{
          display: flex;
          justify-content: space-between;
          >.container-action__main{}
          >.container-action__extra{}
        }
      }
      >.container-body{
        padding: 10px 36px;
      }
    }
  }
  &.embed{
    >.page-container{
      padding: 0;
    }
  }
}