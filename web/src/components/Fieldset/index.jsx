import {Card} from "antd";
import React from "react";
import styles from './index.module.scss'

export default function Fieldset({legend, className, children,extra}) {
    return (<Card title={legend}
                  extra={extra}
                  className={styles.fieldset +
                      " app-fieldset" +
                      (className ? (" " + className) : "")}
    >
        {children}
    </Card>)
}