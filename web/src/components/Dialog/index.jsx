import {<PERSON><PERSON>, Spin} from "antd";
import styles from './index.module.scss'
import {LoadingOutlined} from "@ant-design/icons";
import {useTranslation} from "react-i18next";


export default function Dialog({
                                   children,
                                   fullscreen,
                                   title,
                                   visible,
                                   width,
                                   okText,
                                   cancelText,
                                   closable,
                                   maskClosable = false,
                                   maskStyle,
                                   onOk,
                                   onCancel,
                                   loading,
    loadingTip = '拼命加载中...',
                                   footer,
                                   className,
                                   zIndex,
    rootClassName
                               }) {
    const { t, i18n } = useTranslation();

    return (
        <Modal rootClassName={rootClassName} wrapClassName={styles.dialog}
               className={(fullscreen ? "app-dialog-fullscreen " : "") + className}
               title={title}
               open={visible}
               width={width || 520}
               okText={okText}
               cancelText={cancelText}
               closable={closable}
               maskClosable={maskClosable}
               styles={{
                   mask: maskStyle
               }}
               onOk={onOk}
               onCancel={onCancel}
               confirmLoading={loading}
               footer={footer}
               zIndex={zIndex}
        >
            <Spin spinning={loading} tip={loadingTip} indicator={
                <LoadingOutlined
                    style={{
                        fontSize: 36,
                    }}
                    spin
                />
            }>{children}</Spin>
        </Modal>
    )
}