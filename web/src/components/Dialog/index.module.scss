.dialog:global{
  &.ant-modal-wrap{
    overflow: hidden auto !important;
    .ant-modal {
      .ant-modal-content {
        //position: absolute;
        //top: 0;
        //right: 0;
        //bottom: 0;
        //left: 0;
        width: 100%;
        height: 100%;
        overflow-x: hidden;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        border-radius: 0;
        padding: 0;
        > .ant-modal-close {
          right: 36px;
          top: 20px;
          background: none !important;
          >.ant-modal-close-x{
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
          }
          &:hover {
            >.ant-modal-close-x{
              transform: rotate(180deg);
              font-size: 18px;
              color: #0042ff;
            }
          }
        }
        > .ant-modal-header {
          border-bottom: none;
          border-top: 4px solid #0042ff;
          padding: 18px 36px 12px;
          border-radius: 0;
          > .ant-modal-title {
          }
        }
        > .ant-modal-body {
          flex: auto;
          padding: 0;
          overflow: hidden;
          > .ant-spin-nested-loading {
            width: 100%;
            height: 100%;
            overflow: hidden;
            > .ant-spin-container {
              height: 100%;
              overflow-x: hidden;
              overflow-y: auto;
              padding: 8px 36px 8px;
              > .ant-form {
                > [class^='ant-card ant-card-bordered antFieldset--'] {
                  > .ant-card-head {
                    margin-left: -48px;
                  }
                }
              }
            }
          }
        }
        > .ant-modal-footer {
          border-top: none;
          padding: 15px 36px;
          text-align: center;
          .ant-btn{
            padding: 4px 28px;
          }
        }
      }
    }
    .app-dialog-fullscreen {
      &.ant-modal {
        top: 0;
        overflow: hidden;
        height: 100%;
        padding: 10vh 0;
        >div{
          height: 100%;
          overflow: hidden;
        }
        .ant-modal-content {
          > .ant-modal-body {
            > .ant-spin-nested-loading {
              > .ant-spin-container {
              }
            }
          }
        }
      }
    }
  }
}
