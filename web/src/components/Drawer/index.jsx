import {But<PERSON>, Drawer as <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>} from "antd";
import styles from './index.module.scss'
import {isUndefined, isBoolean} from "lodash";
import {LoadingOutlined} from "@ant-design/icons";

export default function Drawer({
                                   title,
                                   visible,
                                   width,
                                   maskClosable,
                                   footerVisible,
                                   className,
                                   loading = false,
                                   children,
                                   onOk,
                                   onCancel,
                               }) {
    return (
        <AntDrawer
            title={title}
            open={visible}
            width={width || 378}
            maskClosable={maskClosable}
            footer={(isUndefined(footerVisible) || (isBoolean(footerVisible) && footerVisible)) && (
                <Space size={"middle"}>
                    <Button onClick={onCancel}>取消</Button>
                    <Button type={"primary"} onClick={onOk} loading={loading}>确定</Button>
                </Space>
            )}
            className={styles.drawer + " app-ui-drawer" + (className ? " " + className : "")}
            onClose={onCancel}
        >
            <Spin spinning={loading} indicator={
                <LoadingOutlined
                    style={{
                        fontSize: 36,
                    }}
                    spin
                />
            }>{children}</Spin>
        </AntDrawer>
    )
}