import {isFunction, isPlainObject, isArray, isNil, get, has} from "lodash";
import {useCallback, useMemo, useState} from "react";

class Fetcher {
    constructor({api, args, params, beforeCallback, finishCallback}) {
        this.checkApi(api)
        this.api = api
        this.args = isArray(args) ? args : []
        this.params = isPlainObject(params) ? params : {}
        this.beforeCallback = beforeCallback
        this.finishCallback = finishCallback
    }

    checkApi(api) {
        if (api && !isFunction(api)) {
            throw new Error("api is not an function")
        }
    }

    setApi(api) {
        this.checkApi(api)
        this.api = api
    }

    getApi() {
        return this.api
    }

    handleArgs(args) {
        return isArray(args) ? args : []
    }

    setArgs(args) {
        this.args = this.handleArgs(args)
    }

    getArgs() {
        return this.args
    }

    handleParams(params) {
        return isPlainObject(params) ? params : {}
    }

    setParams(params) {
        this.params = this.handleParams(params)
    }

    getParams() {
        return this.params
    }

    setBeforeCallback(cb) {
        this.beforeCallback = cb
    }

    getBeforeCallback() {
        return this.beforeCallback
    }

    setFinishCallback(cb) {
        this.finishCallback = cb
    }

    getFinishCallback() {
        return this.finishCallback
    }

    do() {
        return new Promise(async (resolve, reject) => {
            if (isFunction(this.beforeCallback)) {
                const before = this.beforeCallback()
                try {
                    if (before instanceof Promise) {
                        const beforeResponse = await before
                        if (!isNil(beforeResponse) && !beforeResponse) {
                            resolve(beforeResponse)
                            return
                        }
                    } else {
                        if (!isNil(before) && !before) {
                            resolve(before)
                            return
                        }
                    }
                } catch (e) {
                    reject(e)
                    return
                }
            }

            if (!isFunction(this.api)) {
                reject(new Error("api is not function"))
                return
            }
            try {
                const response = await this.api(...this.args, this.params)
                if (isFunction(this.finishCallback)) {
                    const finish = this.finishCallback()
                    if (finish instanceof Promise) {
                        await finish
                    }
                }
                resolve(response)
            } catch (e) {
                reject(e)
            }
        })
    }
}

export function useFetcher() {
    const fetcher = useMemo(() => {
        return new Fetcher({})
    }, [])

    const [rows, setRows] = useState([])
    const [total, setTotal] = useState(0)
    const [loading, setLoading] = useState(false)

    const [params, setParamsState] = useState({})
    const [args, setArgsState] = useState([])

    const setApi = (_api) => {
        fetcher.setApi(_api)
    }
    const setArgs = (_args) => {
        _args = fetcher.handleArgs(_args)
        fetcher.setArgs(_args)
        setArgsState(_args)
    }
    const setParams = (_params) => {
        _params = fetcher.handleParams(_params)
        fetcher.setParams(_params)
        setParamsState(_params)
    }
    const setBeforeCallback = (cb) => {
        fetcher.setBeforeCallback(cb)
    }
    const setFinishCallback = (cb) => {
        fetcher.setFinishCallback(cb)
    }

    const getApi = () => {
        return fetcher.getApi()
    }

    const getArgs = () => {
        return fetcher.getArgs()
    }

    const getParams = () => {
        return fetcher.getParams()
    }

    const getBeforeCallback = () => {
        return fetcher.getBeforeCallback()
    }

    const getFinishCallback = () => {
        return fetcher.getFinishCallback()
    }

    const set = (api, params, args, beforeCallback, finishCallback) => {
        setApi(api)
        setArgs(args)
        setParams(params)
        setBeforeCallback(beforeCallback)
        setFinishCallback(finishCallback)
    }

    const fetch = useCallback(() => {
        setLoading(true)
        return new Promise((resolve, reject) => {
            fetcher.do().then((response) => {
                if (has(response,"data.total")) {
                    setTotal(get(response,"data.total"))
                }else if (has(response,"data.Total")){
                    setTotal(get(response,"data.Total"))
                }else {
                    setTotal(0)
                }
                if (has(response,"data.Data")) {
                    setRows(get(response,"data.Data"))
                }else if (has(response,"data.data")) {
                    setRows(get(response,"data.data"))
                }else if (has(response,"data.List")){
                    setRows(get(response,"data.List"))
                }else if (has(response,"data.list")){
                    setRows(get(response,"data.list"))
                }else if (has(response,"data.Rows")){
                    setRows(get(response,"data.Rows"))
                }else if (has(response,"data.rows")){
                    setRows(get(response,"data.rows"))
                }else {
                    setRows([])
                }
                resolve(response)
            }).catch(e => {
                reject(e)
            }).finally(() => {
                setLoading(false)
            })
        })
    }, [fetcher])

    return {
        set,
        setApi,
        setArgs,
        setParams,
        setBeforeCallback,
        setFinishCallback,
        getApi,
        getArgs,
        getParams,
        getBeforeCallback,
        getFinishCallback,
        rows,
        total,
        loading,
        fetch,
        args,
        params
    }
}

