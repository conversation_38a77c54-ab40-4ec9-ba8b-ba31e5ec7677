import {createElement, lazy, Suspense} from "react";
import LayoutDefault from '@/layout/default'
import {Spin} from "antd";
import {LoadingOutlined} from '@ant-design/icons'

function ComponentWrapper(v) {
    return (
        <Suspense
            fallback={<div className={'suspenseSpin'}><Spin size={"large"} className={'spins'} indicator={
                <LoadingOutlined
                    style={{
                        fontSize: 36,
                    }}
                    spin
                />
            }/></div>}
        >{createElement(lazy(() => v))}</Suspense>
    )
}

const routes = [
    {
        path: "/",
        element: <LayoutDefault/>,
        children: [
            {
                path: "",
                element: ComponentWrapper(import("@/pages/homepage"))
            },
            {
                path: "sender",
                children: [
                    {
                        path: "",
                        element: ComponentWrapper(import("@/pages/sender/homepage"))
                    }
                ]
            },
            {
                path: "rev",
                children: [
                    {
                        path: "",
                        element: ComponentWrapper(import("@/pages/rev/homepage"))
                    }
                ]
            },
            {
                path: "config",
                children: [
                    {
                        path: 'global',
                        element: ComponentWrapper(import("@/pages/config/global"))
                    },
                    {
                        path: 'rev_sender',
                        element: ComponentWrapper(import("@/pages/config/rev_sender"))
                    },
                    {
                        path: 'transport-channel',
                        element: ComponentWrapper(import("@/pages/config/channel"))
                    },
                    {
                        path: "sys_info",
                        element: ComponentWrapper(import("@/pages/config/sys_info"))
                    },
                    {
                        path: "user",
                        element: ComponentWrapper(import("@/pages/user/list"))
                    },
                    {
                        path: 'remote-upload',
                        element: ComponentWrapper(import("@/pages/config/remote-upload"))
                    }
                ]
            }
        ]
    },
    {
        path: "/login",
        element: ComponentWrapper(import("@/pages/login"))
    }
];

export default routes