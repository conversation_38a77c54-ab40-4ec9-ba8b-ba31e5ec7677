@layer tailwind-base, antd;

@layer tailwind-base {
  @tailwind base;
}
@tailwind components;
@tailwind utilities;

html,body,#root,.ant-app{
  position: relative;
  height: 100%;
  overflow: hidden;
}

.suspenseSpin{
  width: 100%;
  height: 100vh;
  position: relative;
  .spins{
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%,-50%);
  }
}

.ant-spin-nested-loading{
  height: 100%;
  //overflow: hidden;
  position: relative;
  >div{
    >.ant-spin{
      max-height: unset;
      >.ant-spin-text{
        padding-top: 24px;
      }
    }
    &.ant-spin-container{
      height: 100%;
      //overflow: hidden;
      position: relative;
    }
  }
}