export const AntdTheme = {
    hashed: false,
    token: {
        borderRadius: 2,
        colorPrimary: '#3366ff',
        colorLink: '#3366ff',
        fontSize: 14,
        screenXLMax: 1399,
        screenXXLMin: 1400,
        screenXXL: 1400,
        colorInfo: "#3366ff"
    },
    components: {
        Input: {
            controlOutlineWidth: 0,
            fontSize: 13,
            hoverBorderColor: "#999999"
        },
        Button: {
            colorBorder: "#e6e6e6",
            defaultShadow: "none",
            defaultColor: "rgba(0,0,0,0.8)"
        },
        Modal: {
            motionDurationMid: "0.1s",
            motionDurationSlow: "0.1s"
        },
        Table: {
            headerBg: "#ffffff",
            fontSize: 13,
            colorText: "rgba(0, 0, 0, 0.7)"
        },
        Select: {
            colorPrimaryHover: "#999999",
            controlOutlineWidth: 0
        },
        DatePicker: {
            hoverBorderColor: "#999999",
            activeShadow: "none",
        },
        Pagination: {
            itemActiveBg: "#cadbf8",
            itemSize: 28
        },
        Menu: {
            darkSubMenuItemBg: "#1e3a8a",
            itemHeight: 52
        }
    },
}