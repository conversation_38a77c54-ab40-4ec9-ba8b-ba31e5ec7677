// import {createElement} from 'react';
import ReactDOM from 'react-dom/client';
// import {<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useRoutes} from "react-router-dom";
// import routes from "@/router";
import './i18n';

import {ConfigProvider, App as BoxApp} from "antd";
import {legacyLogicalPropertiesTransformer, StyleProvider} from "@ant-design/cssinjs";
import zhCN from 'antd/locale/zh_CN';
import zhHK from 'antd/locale/zh_HK'
import {AntdTheme} from "@/styles/antd-theme";

import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import 'dayjs/locale/zh-hk'

import "antd/dist/reset.css"
import 'simplebar-react/dist/simplebar.min.css';
import "@/styles/global.scss"


import TransferInstance from "@/utils/app/transfer";
import Dev from "@/utils/app/singleton";
import App from './app'
import {GlobalContext} from "@/provider/global";
import {useState} from "react";

TransferInstance.initialize(Dev)

const localeMap = {
    zh_CN: {
        antd: zhCN,
        dayJs: 'zh-cn'
    },
    zh_HK: {
        antd: zhHK,
        dayJs: 'zh-hk'
    }
}

function WrapperApp({children}) {
    dayjs.locale('zh-hk');

    const [locale,setLocale] = useState(zhCN)
    const handleChangeLocale = (targetLocale)=>{
        const l = localeMap[targetLocale]
        if (!l) return;
        dayjs.locale(l.dayJs)
        setLocale(l.antd)
    }

    return (
        <GlobalContext.Provider value={{
            changeLocale: handleChangeLocale
        }}>
            <ConfigProvider
                wave={{disabled: true}}
                theme={AntdTheme}
                locale={locale}
            >
                {children}
            </ConfigProvider>
        </GlobalContext.Provider>
    )
}

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
    <StyleProvider hashPriority={"high"} transformers={[legacyLogicalPropertiesTransformer]}>
        <WrapperApp>
            <BoxApp>
                <TransferInstance.rootProvider>
                    <App />
                </TransferInstance.rootProvider>
            </BoxApp>
        </WrapperApp>
        {/*<ConfigProvider*/}
        {/*    wave={{disabled: true}}*/}
        {/*    theme={AntdTheme}*/}
        {/*    locale={zhCN}*/}
        {/*>*/}
        {/*    <BoxApp>*/}
        {/*        <TransferInstance.rootProvider>*/}
        {/*            <App />*/}
        {/*            /!*<BrowserRouter*!/*/}
        {/*            /!*    basename={process.env.REACT_APP_BASE_PATH || '/'}>*!/*/}
        {/*            /!*    {createElement(() => useRoutes(routes))}*!/*/}
        {/*            /!*</BrowserRouter>*!/*/}
        {/*        </TransferInstance.rootProvider>*/}
        {/*    </BoxApp>*/}
        {/*</ConfigProvider>*/}
    </StyleProvider>
);