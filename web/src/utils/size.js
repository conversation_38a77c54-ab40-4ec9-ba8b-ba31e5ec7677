export function FormatFileSize(bytes) {
    let result = "0KB"
    bytes = parseInt(bytes)
    if (isNaN(bytes) || bytes < 0) {
        return result;
    }
    if (bytes < 1024) {
        result = bytes + "B"
    } else if (bytes < 1024 * 1024) {
        result = (bytes / 1024).toFixed(2) + "KB"
    } else if (bytes < 1024 * 1024 * 1024) {
        result = (bytes / 1024 / 1024).toFixed(2) + "MB"
    } else {
        result = (bytes / 1024 / 1024 / 1024).toFixed(2) + "GB"
    }
    return result;
}