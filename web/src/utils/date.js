import dayjs from "dayjs";

const DATETIME_FORMAT = "YYYY-MM-DD HH:mm:ss";
const DATE_FORMAT = "YYYY-MM-DD";
const TIME_FORMAT = "HH:mm:ss";

export function FormatToDatetime(date = null, format = DATETIME_FORMAT) {
    return dayjs(date).format(format);
}

export function FormatSecondsToDatetime(unix, format = DATETIME_FORMAT) {
    return dayjs.unix(unix).format(format)
}

export function FormatToDate(date = null, format = DATE_FORMAT) {
    return dayjs(date).format(format);
}

export function FormatToTime(date = null, format = TIME_FORMAT) {
    return dayjs(date).format(format);
}

export function WrapperDate(date = null, format = DATE_FORMAT) {
    return dayjs(date, format);
}

export function WrapperDatetime(date = null, format = DATETIME_FORMAT) {
    return dayjs(date, format);
}

export const dateUtil = dayjs;