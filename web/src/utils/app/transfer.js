import {createContext} from "react";

export class Transfer {
    routerObj
    requestInstance
    encryptKey
    basePath
    rootProvider
    rootContext

    constructor(router, requestInstance, encryptKey, basePath) {
        this.routerObj = router
        this.requestInstance = requestInstance
        this.encryptKey = encryptKey
        this.basePath = basePath
        this.rootProvider = ({children}) => children
        this.rootContext = createContext({
            GlobalLoading(loading, loadingText) {
            }
        })
    }

    initialize({routerObj, requestInstance, encryptKey, basePath, rootProvider, rootContext}) {
        this.routerObj = routerObj
        this.requestInstance = requestInstance
        this.encryptKey = encryptKey
        this.basePath = basePath
        this.rootProvider = rootProvider
        this.rootContext = rootContext
    }
}

let TransferInstance

if (!TransferInstance) {
    TransferInstance = new Transfer();
}


export default TransferInstance

export function useRouter() {
    const routerObj = TransferInstance.routerObj
    const pushFunc = routerObj?.pushFunc()
    return routerObj.initialize(pushFunc)
}