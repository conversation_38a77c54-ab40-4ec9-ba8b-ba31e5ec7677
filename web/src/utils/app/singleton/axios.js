import {isUndefined, pickBy} from "lodash";
import Axios from "axios";
import <PERSON><PERSON> from "js-cookie";

const service = Axios.create({
    withCredentials: true,
    baseURL: process.env.REACT_APP_API_SERVER_URL,
    headers: {
        common: {
            "Content-Type": "application/json;charset=UTF-8",
        },
    },
});

service.interceptors.request.use(
    async (request) => {
        if (request.headers["Content-Type"] !== "multipart/form-data") {
            if (request.data) {
                request.data = pickBy(request.data, (value) => {
                    return !isUndefined(value);
                });
            }
        }
        if (request.url.indexOf('?')>-1){
            request.url+='&locale='+Cookie.get('cnix_hk_box_locale')
        }else{
            request.url+='?locale='+Cookie.get('cnix_hk_box_locale')
        }
        return request;
    },
    (error) => {
        return Promise.reject(error);
    }
);

service.interceptors.response.use(
    (response) => {
        const {data, config} = response;
        if (typeof data.code !== "undefined") {
            const {code} = data;
            if (code && code !== 200 && code !== 20000) {
                if ([30001,30002,30003,30004,30005,30006].includes(code)){
                    window.location.href = (process.env.REACT_APP_BASE_PATH||'')+"/login"
                    return ;
                }
                return Promise.reject(
                    {
                        ...data,
                        url: config.url
                    }
                );
            }
            return data;
        }
        return response
    },
    (error) => {
        /*网络连接过程异常处理*/
        let {message} = error;
        if (message === "Network Error") {
            message = "接口异常";
        }
        if (message.includes("timeout")) {
            message = "请求超时";
        }
        if (message.includes("Request failed with status code")) {
            message = message.substr(message.length - 3) + "异常";
        }
        return Promise.reject(error);
    }
);

export default service;
