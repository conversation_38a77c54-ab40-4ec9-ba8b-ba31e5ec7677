import {createContext, useState} from "react";
import {LoadingOutlined} from "@ant-design/icons";
import {Spin} from "antd";

const defaultLoadingText = "拼命记载中..."


export const RootContext = createContext({
    GlobalLoading(loading, loadingText) {
    }
})

export function RootProvider({children}) {
    // 全局加载状态
    const [loading, setLoading] = useState(false)
    const [loadingText, setLoadingText] = useState(defaultLoadingText)
    const handleLoading = (_loading = false, _loadingText = "") => {
        if (_loading) {
            if (!_loadingText) {
                setLoadingText(defaultLoadingText)
            } else {
                setLoadingText(_loadingText)
            }
        }
        setLoading(_loading)
    }
    return (
        <RootContext.Provider value={{
            GlobalLoading: handleLoading,
        }}>
            <Spin tip={loadingText} spinning={loading} size={"large"} indicator={
                <LoadingOutlined
                    style={{
                        fontSize: 36,
                    }}
                    spin
                />
            }>
                {children}
            </Spin>
        </RootContext.Provider>
    )
}
