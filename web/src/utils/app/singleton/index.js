import RouterObj from './router'
import DevRequest from "./request";
import DevAxios from "./axios";
import {RootContext, RootProvider} from './context'

const devRequest = new DevRequest(DevAxios)


const instance = {
    routerObj: RouterObj,
    requestInstance: devRequest,
    encryptKey: "4ozcn613nu8irs5x",
    basePath: "/",
    rootProvider: RootProvider,
    rootContext: RootContext
}

export default instance