export default class Request {
    requestInstance

    constructor(requestInstance) {
        this.requestInstance = requestInstance
    }

    initialize(appId, appSecret, baseUri) {
    }

    getUri(config) {
        return this.requestInstance.getUri(config)
    }

    request(config) {
        return this.requestInstance.request(config)
    }

    get(url, config) {
        return this.requestInstance.get(url, config)
    }

    delete(url, config) {
        return this.requestInstance.delete(url, config)
    }

    head(url, config) {
        return this.requestInstance.head(url, config)
    }

    options(url, config) {
        return this.requestInstance.options(url, config)
    }

    post(url, data, config) {
        return this.requestInstance.post(url, data, config)
    }

    put(url, data, config) {
        return this.requestInstance.put(url, data, config)
    }

    patch(url, data, config) {
        return this.requestInstance.patch(url, data, config)
    }

    postForm(url, data, config) {
        return this.requestInstance.postForm(url, data, config)
    }

    putForm(url, data, config) {
        return this.requestInstance.putForm(url, data, config)
    }

    patchForm(url, data, config) {
        return this.requestInstance.patchForm(url, data, config)
    }
}