import {useNavigate} from "react-router-dom";

class ReactRouter {
    navigate

    constructor(navigateFunc) {
        this.navigate = navigateFunc
    }

    push(path) {
        this.navigate(path)
    }

    back() {
        this.navigate(-1)
    }
}

function initialize(pushFunc) {
    return new ReactRouter(pushFunc)
}

const obj = {
    pushFunc: useNavigate,
    initialize: initialize
}

export default obj