import {attempt, get, isArray} from "lodash";
import {PAGINATION} from "@/settings";

export function getTableTotalWidth(columns = []) {
    let w = 0;
    columns.forEach((t) => {
        if (typeof t.width === "number" && !isNaN(t.width)) {
            w += t.width;
        }
    });
    return w;
}

export function getTableScroll(columns = []) {
    const x = getTableTotalWidth(columns)
    console.log(x)
    return {x}
}

export function compositeSorter(sorter) {
    let sort = []
    if (isArray(sorter)) {
        sorter.forEach(s => {
            if (s.order) {
                sort.push(s.columnKey + ":" + s.order)
            }
        })
    } else {
        if (sorter.order) {
            sort.push(sorter.columnKey + ":" + sorter.order)
        }
    }
    return sort
}

export function HandleTableChange(fetcher, {pagination, filters, sorter, extra, loadFunc}) {
    let sort = get(fetcher.params, "sorter", "")
    if (extra && extra.action === "sort") {
        sort = compositeSorter(sorter).join(',')
    }
    const newParams = {
        ...fetcher.params,
        pagination: {
            [PAGINATION.currentPageKey]: pagination.current,
            [PAGINATION.pageSizeKey]: pagination.pageSize,
        },
        sorter: sort ? sort : undefined
    }
    if (newParams.pagination[PAGINATION.pageSizeKey] !== PAGINATION.defaultPageSize) {
        newParams.pagination[PAGINATION.currentPageKey] = PAGINATION.defaultCurrentPage
    }
    fetcher.setParams(newParams)
    if (loadFunc) {
        attempt(loadFunc)
    }else {
        fetcher.fetch().catch(() => {
        })
    }
}
