import {attempt, keyBy} from "lodash";
import {Divider, Tag} from "antd";
import {CheckCircleOutlined, ExclamationCircleOutlined, ClockCircleOutlined, CopyOutlined} from '@ant-design/icons'
import {FormatFileSize} from "@/utils/size";
import {useTranslation} from "react-i18next";

const SendStatuses = [
    {label: 'send waiting', value: 0, color: 'default', icon: <ClockCircleOutlined/>},
    {label: 'send success', value: 1, color: 'success', icon: <CheckCircleOutlined/>},
    {label: 'send failed', value: 2, color: 'error', icon: <ExclamationCircleOutlined/>},
]

const SendStatusesMap = keyBy(SendStatuses, "value")

export function useCreateTableColumns(
    {
        onView,
        onDelete
    }
) {
    const { t, i18n } = useTranslation();

    return [
        // {
        //     title: "批次号",
        //     key: 'send_code',
        //     width: 190,
        //     fixed: 'left',
        //     render: record => {
        //         return (
        //             <div className={'send-code'}>
        //                 {record.send_code}
        //                 {/*<span title={"复制"}*/}
        //                 {/*      className={"copy-handler"}*/}
        //                 {/*      data-value={record.send_code}><CopyOutlined/>*/}
        //                 {/*</span>*/}
        //             </div>
        //         )
        //     }
        // },
        // {
        //     title: '文件ID',
        //     key: "id",
        //     width: 150,
        //     fixed: "left",
        //     render: record => {
        //         return (
        //             <div className={"file-send-code"}>
        //                 {record.id}
        //             </div>
        //         )
        //     }
        // },
        {
            title: t('file name'),
            key: "filename",
            width: 220,
            render: record => {
                return (
                    <div className={'file-name'}>
                        {record.filename}
                    </div>
                )
            }
        },
        {
            title: t('send status'),
            key: "send_status",
            width: 120,
            align: 'center',
            render: record => {
                const v = SendStatusesMap[record.send_status]
                if (!v) {
                    return (<span>-</span>)
                }
                return (
                    <div className={'send-status'}>
                        <Tag bordered={false} color={v.color} icon={v.icon}>{t(v.label)}</Tag>
                    </div>
                )
            }
        },
        {
            title: t('file size'),
            key: 'file_size',
            width: 120,
            render: record => {
                return (
                    <div className={'file-size'}>
                        {FormatFileSize(record.file_size)}
                    </div>
                )
            }
        },
        {
            title: t('file hash'),
            key: "file_hash",
            width: 120,
            render: record => {
                return (
                    <div className={'file-hash'}>
                        {record.file_hash}
                    </div>
                )
            }
        },
        {
            title: t('sending time'),
            key: "created_at",
            width: 160,
            render: record => {
                return (
                    <div className={'timestamp'}>
                        {record.created_at}
                    </div>
                )
            }
        },
        // {
        //     title: '操作',
        //     width: 150,
        //     fixed: "right",
        //     className: "actions",
        //     align: "center",
        //     render: record => {
        //         return (
        //             <>
        //                 <a onClick={() => attempt(onView, record)}>查看</a>
        //                 <Divider type={"vertical"}/>
        //                 <a onClick={() => attempt(onDelete, record)}>删除</a>
        //             </>
        //         )
        //     }
        // },
    ]
}