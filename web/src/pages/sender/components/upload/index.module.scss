.wrapper:global{
  >.ant-modal-wrap{
    >.ant-modal{
    }
    .ant-modal-content{
      .ant-modal-body{
        .box-upload-component{
          max-height: 520px;
          overflow: hidden auto;
          >.ant-upload-wrapper{
            >.ant-upload{
              height: 360px;
              transition: all .2s;
            }
            >.ant-upload-list{
              display: flex;
              flex-wrap: wrap;
              >.ant-upload-list-item-container{
                width: 50%;
                overflow: hidden;
                >.ant-upload-list-item{
                  height: 26px;
                  margin-top: 0;
                }
                &:nth-child(1),&:nth-child(2){
                  margin-top: 8px;
                }
              }
            }
          }
        }
      }
      .ant-modal-footer{
        padding: 0 !important;
        .box-custom-footer{
          .box-custom-footer-inner{
            .watermark-info{
              margin-top: 12px;
              text-align: left;
              >.ant-alert{
                >.ant-alert-content{
                  >.ant-alert-message{}
                  >.ant-alert-description{
                    .watermark-desc{
                      font-size: 12px;
                      p{
                        margin-bottom: 8px;
                        &.watermark-global-setting{
                          color: #606266;
                        }
                      }
                      .watermark-name{
                        font-weight: bold;
                        color: #3366ff
                      }
                    }
                  }
                }
              }
              .watermark-data{
                border-top: 1px solid #adcaff;
                padding-top: 8px;
                font-size: 12px;
                >ul{
                  display: grid;
                  grid-template-columns: repeat(2, 1fr);
                  gap: 8px; /* 根据需要调整间距 */
                  margin-bottom: 0;
                  >li{
                    >.watermark-item{
                      >.watermark-item-inner{
                        display: flex;
                        >.watermark-item-label{
                          width: 80px;
                          color: #606266;
                        }
                        >.watermark-item-value{}
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}