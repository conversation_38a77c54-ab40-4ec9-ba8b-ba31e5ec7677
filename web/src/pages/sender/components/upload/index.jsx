import styles from './index.module.scss'
import Dialog from "@/components/Dialog";
import {Alert, <PERSON>pp, Button, Checkbox, Space, Typography, Upload} from "antd";
import {CloudUploadOutlined, PaperClipOutlined, DeleteOutlined, CheckCircleOutlined, SafetyOutlined} from '@ant-design/icons'
import {useImmer} from "use-immer";
import {CustomUploadFile, GetPreviewUploadWatermarkInfo} from "@/api/sender/file";
import {attempt, get, isArray} from "lodash";
import {useEffect, useRef} from "react";
import {useRouter} from "@/utils/app/transfer";
import {useTranslation} from "react-i18next";

const beforeUpload = (file, minSize, maxSize, fileTypes, t) => {
    if (isArray(fileTypes)) {
        if (!fileTypes.includes(file.type)) {
            throw new Error(JSON.stringify({
                message: `${t('file')} [${file.name}] ${t('format disallowed')}！`,
                type: "fileType",
                allowTypes: fileTypes,
                file: file
            }))
        }
    }
    if (minSize) {
        if (file.size < minSize) {
            throw new Error(JSON.stringify({
                message: `${t('file')} [${file.name}] ${t('too small')}！`,
                type: "minSize",
                allowMinSize: minSize,
                file: file
            }))
        }
    }
    if (maxSize) {
        if (file.size > maxSize) {
            throw new Error(JSON.stringify({
                message: `${t('file')} [${file.name}] ${t('too large')}！`,
                type: "maxSize",
                allowMaxSize: maxSize,
                file: file
            }))
        }
    }

    return true;
}


const allowFileMinSize = 0;
const allowFileMaxSize = 20 * 1024 * 1024;


export default function HomepageComponentUpload(
    {
        visible = false,
        onOk,
        onCancel
    }
) {
    const { t, i18n } = useTranslation();

    const router = useRouter()
    const {message} = App.useApp();
    const [state, updateState] = useImmer({
        loading: false,
        loadingTip: t('uploading waiting'),
        fileList: [],
        hasWaitUploadFile: false,
        wm:{
            enabled: false,
            data: {}
        }
    })

    const handleBeforeUpload = (file) => {
        // if (state.fileList.length >= maxFileNum) {
        //     return Promise.reject();
        // }
        for (let i = 0; i < state.fileList.length; i++) {
            const vFile = state.fileList[i]
            if (vFile.name === file.name && vFile.size === file.size && vFile.type === file.type && vFile.lastModified === file.lastModified) {
                message.error(`[${file.name}] ${t('already exist')}`)
                return Promise.reject()
            }
        }
        try {
            beforeUpload(file, allowFileMinSize, allowFileMaxSize, null, t)
            updateState(draft => {
                draft.fileList = [...draft.fileList, file]
                draft.hasWaitUploadFile = true
            })
            return Promise.reject()
        } catch (e) {
            const msg = get(JSON.parse(e.message), 'message', t('upload error'))
            message.error(msg);
            return Promise.reject()
        }
    }

    const handleRemove = (file) => {
        updateState(draft => {
            const index = state.fileList.indexOf(file);
            const newFileList = draft.fileList.slice();
            newFileList.splice(index, 1);
            let hasWaitUploadFile = false;
            for (let i = 0; i < newFileList.length; i++) {
                if (!newFileList[i].status) {
                    hasWaitUploadFile = true
                    break
                }
            }
            draft.fileList = newFileList;
            draft.hasWaitUploadFile = hasWaitUploadFile
        })
    }

    const draggerRef = useRef();
    const handleClick = () => {
        const formData = new FormData();
        state.fileList.forEach((file) => {
            if (file.status === 'done') {
                return;
            }
            formData.append('files[]', file);
        });
        updateState(draft => {
            draft.loading = true
            draft.loadingTip = t('uploading waiting')
        })
        CustomUploadFile({
            onProgress: (percent) => {
                updateState(draft => {
                    if (percent.percent === 100) {
                        draft.loadingTip = (
                            <span><span style={{fontWeight: 'bold'}}>{t('sending waiting')}</span></span>)
                    } else {
                        draft.loadingTip = (
                            <span>{t('already upload')} <span style={{fontWeight: 'bold'}}>{percent.percent}%</span></span>)
                    }
                })
            }
        }, formData, {
            wm_enabled: state.wm.enabled,
        }).then(() => {
            updateState(draft => {
                draft.loading = false
                draft.loadingTip = t('uploading waiting')
                for (let i = 0; i < draft.fileList.length; i++) {
                    draft.fileList[i].status = 'done';
                }
                draft.hasWaitUploadFile = false;
            })
            message.success(t('already success upload')).then(() => {
            })
            attempt(onOk)
        }).catch((e) => {
            console.error(e)
            message.error(get(e,'message',t('upload failed please refresh')))
            updateState(draft => {
                draft.loading = false
                draft.loadingTip = t('uploading waiting')
                for (let i = 0; i < draft.fileList.length; i++) {
                    draft.fileList[i].status = 'error';
                }
                draft.hasWaitUploadFile = false;
            })
            attempt(onCancel)
        })
    }

    useEffect(() => {
        if (!draggerRef.current) return;
        if (state.fileList.length === 0) {
            draggerRef.current.nativeElement.children[0].style.height = '360px';
            return;
        }

        const fileUnitHeight = 26;
        const maxHeight = 520;
        const maxDraggerHeight = 360;
        const minDraggerHeight = 158;

        let fileRows = parseInt(state.fileList.length / 2);
        if (state.fileList.length % 2 !== 0) {
            fileRows += 1;
        }
        const fileListHeight = fileRows * fileUnitHeight + 8;
        const totalHeight = maxDraggerHeight + fileListHeight;

        if (totalHeight > maxHeight) {
            const diff = totalHeight - maxHeight;
            const targetDraggerHeight = maxDraggerHeight - diff;
            if (targetDraggerHeight >= minDraggerHeight) {
                draggerRef.current.nativeElement.children[0].style.height = targetDraggerHeight + "px";
            }
        }

    }, [state.fileList])

    const loadWatermarkPreviewInfo = async ()=>{
        try {
            const res = await GetPreviewUploadWatermarkInfo({})
            updateState(draft => {
                draft.wm.data = res.data
                draft.wm.enabled = !!res.data.sender_up_wm_enabled
            })
        }catch (e) {
            message.error(e.message || t('operate fail'))
        }
    }

    useEffect(() => {
        if (visible) {
            updateState(draft => {
                draft.fileList = [];
                draft.hasWaitUploadFile = false;
            })
            loadWatermarkPreviewInfo()
        }
    }, [visible])

    const toggleWatermarkEnabled = (e)=>{
        updateState(draft => {
            draft.wm.enabled = e.target.checked
        })
    }

    return (
        <Dialog closable={!state.loading} className={'123'} rootClassName={styles.wrapper} title={t('file upload')} visible={visible}
                onOk={onOk} onCancel={onCancel}
                loading={state.loading}
                loadingTip={state.loadingTip}
                width={688}
                footer={<div className={'box-custom-footer'}>
                    <div className={'box-custom-footer-inner'}>
                        {state.hasWaitUploadFile ? (
                            <>
                                <Space>
                                    <Checkbox checked={state.wm.enabled} onChange={toggleWatermarkEnabled}>{t('open watermark')}</Checkbox>
                                    <Button onClick={handleClick} type={'primary'} loading={state.loading}>{t('start upload')}</Button>
                                </Space>
                                <div className={'watermark-info'}>
                                    {state.wm.enabled?(
                                        <Alert
                                            banner={true}
                                            description={
                                                <div>
                                                    <div className={'watermark-desc'}>
                                                        <p>{t('implicit watermark prefix')} <span className={'watermark-name'}><SafetyOutlined />{t('implicit watermark')}</span> {t('implicit watermark suffix')}。</p>
                                                        <p className={'watermark-global-setting'}>{t('watermark switched in watermark global settings')}，<a onClick={()=>router.push('/config/global')}>{t('click to go')}</a></p>
                                                    </div>
                                                    <div className={'watermark-data'}>
                                                        <ul>
                                                            <li>
                                                                <div className={'watermark-item'}>
                                                                    <div className={'watermark-item-inner'}>
                                                                        <div className={'watermark-item-label'}>{t('belongs agency')}：
                                                                        </div>
                                                                        <div className={'watermark-item-value'}>{get(state.wm.data,'institution')}
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div className={'watermark-item'}>
                                                                    <div className={'watermark-item-inner'}>
                                                                        <div className={'watermark-item-label'}>{t('operator')}：</div>
                                                                        <div className={'watermark-item-value'}>{get(state.wm.data,'operator')}</div>
                                                                    </div>
                                                                </div>
                                                            </li>
                                                            <li>
                                                                <div className={'watermark-item'}>
                                                                    <div className={'watermark-item-inner'}>
                                                                        <div className={'watermark-item-label'}>{t('watermark time')}：
                                                                        </div>
                                                                        <div className={'watermark-item-value'}>{t('base system time generation')}
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            }
                                            type="info"
                                            showIcon={true}
                                        />
                                    ):null}
                                </div>
                            </>
                        ) : null}
                    </div>
                </div>}
        >
            <div className={'box-upload-component'}>
                <Upload.Dragger
                    fileList={state.fileList}
                    ref={draggerRef}
                    name={'file'}
                    multiple={true}
                    beforeUpload={handleBeforeUpload}
                    onRemove={handleRemove}
                    showUploadList={{
                        showRemoveIcon: (file) => {
                            return !['done', 'uploading'].includes(file.status);
                        },
                        showDownloadIcon: false,
                        showPreviewIcon: false,
                    }}
                    itemRender={(originNode, file, fileList, actions) => {
                        return (
                            <div className="ant-upload-list-item ant-upload-list-item-undefined">
                                <div className="ant-upload-icon">
                                    {file.status === 'done' ? (
                                        <CheckCircleOutlined style={{
                                            color: "#52c41a"
                                        }}/>
                                    ) : (
                                        <PaperClipOutlined/>
                                    )}
                                </div>
                                <span className="ant-upload-list-item-name"
                                      title={file.name}><Typography.Text
                                    type={file.status === 'done' ? 'success' : undefined}>{file.name}</Typography.Text></span>
                                <span
                                    className="ant-upload-list-item-actions">
                                {!['done', 'uploading'].includes(file.status) ? (
                                    <Button onClick={actions.remove} type={'text'} title={t('delete file')}
                                            icon={<DeleteOutlined/>} className={'ant-upload-list-item-action'}></Button>
                                ) : null}
                                </span>
                            </div>
                        )
                    }}
                >
                    <p className="ant-upload-drag-icon">
                        <CloudUploadOutlined/>
                    </p>
                    <p className="ant-upload-text">{t('click or drag the file to this location')}</p>
                    <p className="ant-upload-hint">{t('support single or batch upload, with a maximum file size of 20 MB per file')}</p>
                </Upload.Dragger>
            </div>
        </Dialog>
    )
}