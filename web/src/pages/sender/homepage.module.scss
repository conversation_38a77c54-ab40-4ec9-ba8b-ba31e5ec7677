.wrapper:global {
  height: 100%;
  overflow: hidden;

  >.wrapper-inner {
    height: 100%;
    overflow: hidden;

    .page-layout {
      background: none;
      //margin: 0 -12px;

      > .page-container {
        background: none;
        padding: 12px 12px;
        //@apply container mx-auto;

        > .container-inner {
          box-shadow: 0 1px 2px 0 rgba(54, 58, 80, .22);
          //box-shadow: 0 0 20px 0 rgba(55, 99, 170, 0.1), 0 0px 20px 0 #fff;
          //max-width: 1400px;
          //margin: 0 auto;
          display: flex;
          flex-direction: column;

          > .container-top {
            border-bottom: 12px solid #eef0f4;

            > .ant-alert {
              border: none;
              background-color: #ffffff;

              > .anticon {
                font-size: 16px;
                margin-top: 4px;
              }

              > .ant-alert-content {
                > .ant-alert-message {
                  font-size: 14px;
                  color: #5e6e9c;
                }

                > .ant-alert-description {
                  > ol {
                    margin-left: 14px;
                    margin-bottom: 0;

                    > li {
                      font-size: 12px;
                      color: #5e6e9c;
                      line-height: 24px;
                    }
                  }
                }
              }
            }
          }

          > .container-header {
            background: none;
            //box-shadow: 0 2px 4px 0 rgba(54, 58, 80, .22);

            > .container-search {
              > .ant-form {
                margin-bottom: -12px;

                .ant-form-item {
                  margin-bottom: 12px;

                  .ant-form-item-label {
                    > label {
                      width: 65px;
                    }
                  }

                  .ant-picker-range {
                    width: 100%;
                  }
                }
              }

              > .actions {
                flex-direction: row;

                > .ant-btn {
                  & + .ant-btn {
                    margin-left: 12px;
                  }
                }
              }
            }

            > .container-action {
              > .container-action__main {
                > .inner {
                  > .ant-btn {
                    font-size: 13px;
                  }
                }
              }

              > .container-action__extra {
                > .extra {
                  display: flex;
                }
              }
            }
          }

          > .container-body {
            flex: 1;
            //background: #ffffff;
            //box-shadow: 0 3px 4px 0 rgba(54, 58, 80, .22);


            .ant-table-wrapper {
              .ant-table {
                &.ant-table-empty {
                  .ant-table-body {
                    overflow: hidden !important;
                  }
                }
                .ant-table-body {
                  .ant-table-placeholder {
                    .ant-table-cell {
                      border: none;
                    }
                  }

                  .empty {
                    > .empty-inner {
                      //border: 1px solid #e6e6e6;
                      display: flex;
                      flex-direction: column;
                      justify-content: center;
                      align-items: center;
                      height: 480px;

                      > .empty-icon {
                        margin-bottom: 12px;

                        > img {
                          width: 60px;
                        }
                      }

                      > .empty-text {
                        color: #888888;
                        text-align: center;
                        width: 520px;
                        margin-bottom: 16px;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    //&.no-data{
    //  >.page-layout{
    //    >.page-container{
    //      >.container-inner{
    //        >.container-header{
    //          display: none;
    //        }
    //      }
    //    }
    //  }
    //}
  }
}