import {Button, Form, Input, Table, Row, Col, Divider, App, DatePicker, Select} from "antd";
import {LoadingOutlined, SyncOutlined, PlusCircleOutlined, SearchOutlined, ReloadOutlined} from '@ant-design/icons'
import {useFetcher} from "@/components/Fetcher";
import PageLayout from '@/components/PageLayout'
import {useEffect, useRef} from "react";
import {trim, get, has, omitBy, isArray} from "lodash";
import {HandleTableChange} from "@/utils/table";
import {PAGINATION, TABLE_STICKY_OFFSET_HEADER} from "@/settings";
import styles from './homepage.module.scss'
import {useCreateTableColumns} from "./homepage.metadata";
import {useImmer} from "use-immer";
import ComponentUpload from './components/upload'
import {GetFileList} from "@/api/sender/file";
import dayjs from "dayjs";
import {FormatToDatetime} from "@/utils/date";
import {useTranslation} from "react-i18next";

let stopSearchListen = false;
export default function Homepage() {
    const { t, i18n } = useTranslation();

    const {modal} = App.useApp()
    const [state, updateState] = useImmer({
        initialized: false,
        uploadComponent: {
            visible: false
        },
        channel: {
            loading: false,
            options: [],
            selected: null,
        }
    })

    const fetcher = useFetcher();
    const layoutRef = useRef()

    const [searchState, updateSearchState] = useImmer({
        filename: "",
        // file_hash: "",
        send_code: '',
    })
    const [searchForm] = Form.useForm();
    const handleSearch = () => {
        if (!state.initialized) return;
        if (stopSearchListen) return;

        let values = searchForm.getFieldsValue(true) || {}
        if (has(values, "filename")) {
            values.filename = trim(values.filename)
        }
        // if (has(values, "file_hash")) {
        //     values.file_hash = trim(values.file_hash)
        // }
        if (has(values, "send_code")) {
            values.send_code = trim(values.send_code)
        }
        let params = {
            ...fetcher.params,
            ...values
        }
        if (has(params, "send_at")) {
            delete params.send_at
        }

        const oldFilename = get(fetcher.params, "filename", "")
        const newFilename = get(values, "filename", "")

        // const oldFileHash = get(fetcher.params, "file_hash", "")
        // const newFileHash = get(values, "file_hash", "")
        const oldSendCode = get(fetcher.params, "send_code", "")
        const newSendCode = get(values, "send_code", "")

        const oldTimeStart = get(fetcher.params, "send_at_start", "")
        const oldTimeEnd = get(fetcher.params, "send_at_end", "")

        let newTimeStart = ""
        let newTimeEnd = ""
        if (isArray(values.send_at) && values.send_at.length === 2) {
            newTimeStart = FormatToDatetime(values.send_at[0])
            newTimeEnd = FormatToDatetime(values.send_at[1].add(1, 'd'))
        }
        params["send_at_start"] = newTimeStart
        params["send_at_end"] = newTimeEnd

        if (
            oldFilename !== newFilename ||
            // oldFileHash !== newFileHash ||
            oldSendCode !== newSendCode ||
            oldTimeStart !== newTimeStart ||
            oldTimeEnd !== newTimeEnd
        ) {
            params = {
                ...params,
                pagination: {
                    [PAGINATION.currentPageKey]: PAGINATION.defaultCurrentPage,
                    [PAGINATION.pageSizeKey]: PAGINATION.defaultPageSize,
                }
            }
        }
        params = omitBy(params, value => !value)
        fetcher.setParams(params)

        loadTableData()
    }
    const searchFormFilename = Form.useWatch(["filename"], searchForm)
    useEffect(() => {
        if (typeof searchFormFilename === "undefined") return;
        const v = trim(searchFormFilename)
        updateSearchState(draft => {
            draft.filename = v
        })
        if (!v) {
            if (searchState.filename) {
                handleSearch()
            }
        }
    }, [searchFormFilename])

    const handleSearchFilenameOnChange = e => {
        if (e.type === 'click') {
            if (searchFormFilename) {
                if (!trim(e.target.value)) {
                    searchForm.setFieldValue(["filename"], '')
                }
            }
        }
    }

    const searchFormSendCode = Form.useWatch(["send_code"], searchForm)
    useEffect(() => {
        if (typeof searchFormSendCode === "undefined") return;
        const v = trim(searchFormSendCode)
        updateSearchState(draft => {
            draft.send_code = v
        })
        if (!v) {
            if (searchState.send_code) {
                handleSearch()
            }
        }
    }, [searchFormSendCode])
    const handleSearchSendCodeOnChange = e => {
        if (e.type === 'click') {
            if (searchFormSendCode) {
                if (!trim(e.target.value)) {
                    searchForm.setFieldValue(["send_code"], '')
                }
            }
        }
    }
    // const searchFormFileHash = Form.useWatch(["file_hash"], searchForm)
    // useEffect(() => {
    //     if (typeof searchFormFileHash === "undefined") return;
    //     const v = trim(searchFormFileHash)
    //     updateSearchState(draft => {
    //         draft.file_hash = v
    //     })
    //     if (!v) {
    //         if (searchState.file_hash) {
    //             handleSearch()
    //         }
    //     }
    // }, [searchFormFileHash])
    // const handleSearchFileHashOnChange = e => {
    //     if (e.type === 'click') {
    //         if (searchFormFileHash) {
    //             if (!trim(e.target.value)) {
    //                 searchForm.setFieldValue(["file_hash"], '')
    //             }
    //         }
    //     }
    // }

    const searchFormSendAt = Form.useWatch(["send_at"], searchForm)
    useEffect(() => {
        if (typeof searchFormSendAt === "undefined") return;
        handleSearch()
    }, [searchFormSendAt])

    const handleSearchFormStringPressEnter = e => {
        const val = trim(e.target.value)
        if (!val) return;
        handleSearch()
    }

    const handleSearchButtonClick = () => {
        // const val = trim(searchFormCompanyName)
        // if (!val) return;
        // handleSearch()
    }

    const handleReset = () => {
        stopSearchListen = true;

        searchForm.setFieldsValue({
            filename: '',
            // file_hash: '',
            send_code: '',
            send_at: [],
        })
        setTimeout(() => {
            stopSearchListen = false;
            handleSearch()
        }, 0)
    }

    const handleView = (record) => {
    }

    const handleDelete = (record) => {
    }

    const tableColumns = useCreateTableColumns({
        onView: handleView,
        onDelete: handleDelete
    });

    const loadTableData = async () => {
        fetcher.fetch().then(({data}) => {
            if (!data.data || data.data.length === 0) {
                let page = get(fetcher.params, 'pagination.' + PAGINATION.currentPageKey, 1)
                if (page > 1) {
                    page -= 1
                    const pageSize = get(fetcher.params, 'pagination.' + PAGINATION.pageSizeKey, 10)
                    fetcher.setParams({
                        ...fetcher.params,
                        pagination: {
                            [PAGINATION.currentPageKey]: page,
                            [PAGINATION.pageSizeKey]: pageSize,
                        }
                    })
                    loadTableData()
                    return
                }
            }
        }).catch((e) => {
            const code = get(e, 'code')
            const message = get(e, 'message')
            if (code === 500) {
                modal.warning({
                    title: message || t('system error'),
                    centered: true,
                    maskClosable: true,
                })
            }
        }).finally(() => {
            updateState(draft => {
                draft.initialized = true
            })
        })
    }

    const handleTableChange = (pagination, filters, sorter, extra) => {
        HandleTableChange(fetcher, {pagination, filters, sorter, extra, loadFunc: loadTableData})
    }

    const initialize = () => {
        fetcher.set(GetFileList, {
            pagination: {
                [PAGINATION.currentPageKey]: PAGINATION.defaultCurrentPage,
                [PAGINATION.pageSizeKey]: PAGINATION.defaultPageSize,
            }
        })
        loadTableData()
    }

    useEffect(() => {
        initialize()
    }, [])

    // ----------------------------------- upload component -----------------------------
    const handleUpload = () => {
        updateState(draft => {
            draft.uploadComponent.visible = true;
        })
    }
    const handleUploadCancel = () => {
        updateState(draft => {
            draft.uploadComponent.visible = false;
        })
        loadTableData()
    }
    const handleUploadOk = () => {
        updateState(draft => {
            draft.uploadComponent.visible = false;
        })
        loadTableData()
    }
    // ----------------------------------- upload component -----------------------------


    // ----------------------------------- channel select -----------------------------
    const handleChannelSelect = (value) => {
        updateState(draft => {
            draft.channel.selected = value;
        })
    }
    // ----------------------------------- channel select -----------------------------

    return (
        <div className={styles.wrapper}>
            <div className={"wrapper-inner " + (!fetcher.total ? " no-data" : "")}>
                <div className={'h-full'}>
                    <PageLayout header={
                        <div className={'flex items-center'}>
                            <span>{t('northbound data cross-border transmission')}</span>
                            {/*<div className={'ml-3'}>*/}
                            {/*    <Select*/}
                            {/*        options={state.channel.options}*/}
                            {/*        value={state.channel.selected}*/}
                            {/*        placeholder={'请选择通道'}*/}
                            {/*        loading={state.channel.loading}*/}
                            {/*        onChange={handleChannelSelect}*/}
                            {/*        showSearch={true}*/}
                            {/*        variant={'borderless'}*/}
                            {/*        style={{*/}
                            {/*            minWidth: 158,*/}
                            {/*            height: 22*/}
                            {/*        }}*/}
                            {/*    />*/}
                            {/*</div>*/}
                        </div>
                    } ref={layoutRef} action={
                        <>
                            <Button type={"primary"} onClick={handleUpload}
                                    icon={<PlusCircleOutlined/>}>{t('upload file')}</Button>
                        </>
                    } search={<>
                        <Form form={searchForm}  labelCol={{
                            style:{
                                width: '90px'
                            }
                        }} labelAlign={"left"} initialValues={{}}>
                            <Row gutter={18}>
                                {/*<Col sm={7} xxl={4}>*/}
                                {/*    <Form.Item name={"status"} label={"状态"} style={{}}>*/}
                                {/*        <Select options={[{value: 0, label: "全部"}].concat(CertStatuses)}/>*/}
                                {/*<Input variant={'filled'}/>*/}
                                {/*</Form.Item>*/}
                                {/*</Col>*/}
                                <Col sm={8} xxl={8}>
                                    <Form.Item name={"filename"} label={t('file name')}>
                                        <Input placeholder={t('please enter the file name')} variant={'filled'} allowClear={true}
                                               onPressEnter={handleSearchFormStringPressEnter}
                                               onChange={handleSearchFilenameOnChange}/>
                                    </Form.Item>
                                </Col>
                                <Col sm={8} xxl={8}>
                                    <Form.Item name={"send_code"} label={t('send batch')}>
                                        <Input disabled={true} placeholder={t('please enter the sending batch')} variant={'filled'} allowClear={true}
                                               onPressEnter={handleSearchFormStringPressEnter}
                                               onChange={handleSearchSendCodeOnChange}/>
                                    </Form.Item>
                                </Col>
                                <Col sm={8} xxl={8}>
                                    <Form.Item name={"send_at"} label={t('sending time')}>
                                        <DatePicker.RangePicker variant={'filled'} style={{
                                            width: "100%"
                                        }} presets={[
                                            {
                                                label: t('today'),
                                                value: [dayjs().startOf('d'), dayjs().startOf('d')],
                                            },
                                            {
                                                label: t('yesterday'),
                                                value: [dayjs().add(-1, 'd').startOf('d'), dayjs().add(-1, 'd').startOf('d')],
                                            },
                                            {
                                                label: t('last 7 days'),
                                                value: [dayjs().add(-6, 'd').startOf('d'), dayjs().startOf('d')],
                                            },
                                            {
                                                label: t('last 15 days'),
                                                value: [dayjs().add(-14, 'd').startOf('d'), dayjs().startOf('d')],
                                            },
                                        ]} disabledDate={(current) => {
                                            return current && (
                                                current > dayjs().startOf('day')
                                            )
                                        }}/>
                                    </Form.Item>
                                </Col>
                            </Row>
                        </Form>
                        <Divider type={"vertical"}/>
                        <div className={"actions"}>
                            <Button type={"primary"} icon={<SearchOutlined/>} onClick={handleSearch}>{t('query')}</Button>
                            <Button icon={<ReloadOutlined/>} onClick={handleReset}>{t('reset')}</Button>
                        </div>
                    </>} actionExtra={<Button icon={<SyncOutlined/>} title={t('refresh')} onClick={loadTableData}/>}>
                        <Table rowKey={"id"}
                               bordered={false}
                               columns={tableColumns}
                               dataSource={fetcher.rows}
                               onChange={handleTableChange}
                               loading={{
                                   spinning: fetcher.loading,
                                   indicator: (<LoadingOutlined
                                       style={{
                                           fontSize: 36,
                                       }}
                                       spin
                                   />),
                                   tip: t('loading')
                               }}
                               className={fetcher.total ? "" : " no-data"}
                               locale={{
                                   emptyText: (
                                       <div className={"empty"}>
                                           <div className={"empty-inner"}>
                                               <div className={"empty-action"}>
                                                   <Button size={'large'} className={"create-btn"} type={"primary"}
                                                           icon={<PlusCircleOutlined/>}
                                                           onClick={handleUpload}>{t('upload file')}</Button>
                                               </div>
                                           </div>
                                       </div>
                                   )
                               }}
                               sticky={{offsetHeader: TABLE_STICKY_OFFSET_HEADER}}
                               scroll={{x: 'max-content'}}
                               pagination={{
                                   ...PAGINATION,
                                   total: fetcher.total,
                                   current: get(fetcher.params, 'pagination.' + PAGINATION.currentPageKey, 1),
                                   pageSize: get(fetcher.params, 'pagination.' + PAGINATION.pageSizeKey, 10),
                                   hideOnSinglePage: true,
                                   showQuickJumper: false
                               }}
                        />
                    </PageLayout>
                </div>
            </div>
            <ComponentUpload {...state.uploadComponent} onOk={handleUploadOk} onCancel={handleUploadCancel}/>
        </div>
    )
}