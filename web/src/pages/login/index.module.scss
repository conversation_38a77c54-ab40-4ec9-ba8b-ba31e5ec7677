.wrapper:global {
  height: 100%;
  overflow: hidden;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: 50% center;

  > .wrapper-inner {
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    >.component-locale{
      position: fixed;
      top: 24px;
      right: 36px;
    }

    > .page-title {
      @apply text-5xl font-bold text-blue-500 mb-16 -mt-16;
    }

    > .login-panel {
      background-color: #ffffff;
      padding: 68px 68px;

      > .login-panel-inner {
        > .login-panel-head {
          > .login-panel-head-title {
            @apply text-3xl font-bold text-blue-500 text-center mb-20;
          }
        }

        > .login-form {
          > .ant-form-item {
            @apply mb-9;
            .ant-input-filled{
              padding-top: 11px;
              padding-bottom: 11px;
            }
            .ant-input-affix-wrapper .ant-input-prefix{
              margin-right: 8px;
            }
            &.login-form-footer-actions {
              @apply mt-20;
              .login-btn {
                height: auto;
                @apply py-3 #{!important};
              }
            }
          }
        }
      }
    }
  }
}