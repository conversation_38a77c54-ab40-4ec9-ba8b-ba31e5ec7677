import styles from "./index.module.scss"
import LoginBg from '@/assets/login_bg.7bce8874.png'
import {App, Button, Form, Input} from "antd";
import {IdcardTwoTone, LockTwoTone} from '@ant-design/icons'
import {useFormRules} from './index.metadata'
import {get, isArray} from "lodash";
import {sha256} from 'js-sha256'
import {useContext, useEffect} from "react";
import TransferInstance from "@/utils/app/transfer";
import {AuthLogin} from "@/api/auth";
import {useTranslation} from "react-i18next";
import ComponentLocale from '@/components/Locale'

export default function LoginPage() {
    const { t, i18n } = useTranslation();
    const context = useContext(TransferInstance.rootContext)
    const {message,modal} = App.useApp();
    const FormRules = useFormRules()

    const [form] = Form.useForm()

    const handleFormFinish = (values) => {
        context.GlobalLoading(true, t('login in'))
        AuthLogin({
            username: values.username,
            // password: sha256(values.password)
            password: values.password
        }).then((r) => {
            message.success(t('login success'))
            setTimeout(()=>{
                window.location.href = (process.env.REACT_APP_BASE_PATH||'/')
            }, 1000)
        }).catch(e => {
            const code = get(e, 'code')
            const message = get(e, 'message')
            if (code === 500) {
                modal.warning({
                    title: t(message || 'login fail'),
                    centered: true,
                    maskClosable: true,
                })
            }
        }).finally(() => {
            context.GlobalLoading(false)
        })
    }

    const handleFormFinishFailed = (e) => {
        let msg = t('check form')
        if (isArray(e.errorFields) && e.errorFields.length > 0) {
            const firstField = e.errorFields[0]
            if (isArray(firstField.errors) && firstField.errors.length > 0) {
                msg = firstField.errors[0]
            }
        }
        modal.warning({
            title: msg,
            centered: true,
            maskClosable: true,
        })
    }

    useEffect(()=>{
        i18n.changeLanguage(i18n.language)
    },[])

    return (
        <div className={styles.wrapper} style={{
            backgroundImage: `url(${LoginBg})`
        }}>
            <div className={"wrapper-inner"}>
                <ComponentLocale />
                <div className={'page-title'}>{t('app sub name')}</div>

                <div className={'login-panel w-[480px] shadow-lg'}>
                    <div className={'login-panel-inner'}>
                        <div className={'login-panel-head'}>
                            <h1 className={'login-panel-head-title'}>{t('welcome login')}</h1>
                        </div>
                        <Form form={form}
                              className={'login-form'}
                              onFinish={handleFormFinish}
                              onFinishFailed={handleFormFinishFailed}
                        >
                            <Form.Item name={['username']} rules={FormRules.username}>
                                <Input prefix={<IdcardTwoTone/>} variant={'filled'} size={'large'}
                                       placeholder={t('please enter the account')}
                                       onPressEnter={form.submit}
                                />
                            </Form.Item>
                            <Form.Item name={['password']} rules={FormRules.password}>
                                <Input.Password prefix={<LockTwoTone/>} variant={'filled'} size={'large'}
                                                placeholder={t('please enter the password')}
                                                onPressEnter={form.submit}
                                />
                            </Form.Item>
                            <Form.Item className={'login-form-footer-actions'}>
                                <Button onClick={form.submit} size={'large'} block={true} className={'login-btn'}
                                        type={'primary'}>{t('login immediately')}</Button>
                            </Form.Item>
                            <div className={'text-center text-gray-300'}>&copy; {new Date().getFullYear()}</div>
                        </Form>
                    </div>
                </div>
            </div>
        </div>
    )
}