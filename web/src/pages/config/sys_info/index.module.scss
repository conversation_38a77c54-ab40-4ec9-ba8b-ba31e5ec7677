.wrapper:global{
  height: 100%;
  overflow: hidden;
  >.wrapper-inner{
    height: 100%;
    overflow: hidden;

    .page-layout{
      background: none;
      >.page-container{
        background: none;
        >.container-inner{
          //box-shadow: 0 1px 2px 0 rgba(54, 58, 80, .22);
          display: flex;
          flex-direction: column;
          background: none;
          >.container-body{
            flex: 1;
            position: relative;
            padding: 0;
            .ele-card.ant-card{
              box-shadow: 0 1px 2px 0 rgba(54, 58, 80, .22);
              border: none;
              >.ant-card-head{
                border: none;
                >.ant-card-head-wrapper{
                  >.ant-card-head-title{
                    color: #333333;
                    font-size: 16px;
                  }
                }
              }
              >.ant-card-body{}
            }
          }
        }
      }
    }
  }
}