import styles from './index.module.scss'
import PageLayout from "@/components/PageLayout";
import {Card} from "antd";
// import {useImmer} from "use-immer";
import {useEffect} from "react";

import * as echarts from 'echarts/core';
import { TooltipComponent } from 'echarts/components';
import { GaugeChart } from 'echarts/charts';
import { CanvasRenderer } from 'echarts/renderers';
import {GetSysResources} from "@/api/sys";
import {debounce, get} from "lodash";
import {useTranslation} from "react-i18next";

echarts.use([TooltipComponent, GaugeChart, CanvasRenderer]);

let memoryChart = null;
let projectDiskChart = null;
let refreshInterval = null;
export default function SysInfo() {
    const { t, i18n } = useTranslation();

    // const [state, updateState] = useImmer({
    //     memory: {
    //         usedPercent: 0,
    //     },
    //     projectDist: {
    //         usedPercent: 0,
    //     }
    // })

    const initMemoryChart = () => {
        const chartDom = document.getElementById('memoryChart');
        memoryChart = echarts.init(chartDom, null, {
            height: 520
        });
        const option = {
            tooltip: {
                formatter: '{a} <br/>{b} : {c}%'
            },
            series: [
                {
                    name: t('memory'),
                    type: 'gauge',
                    radius: '90%', // 调整仪表盘大小，使其更大，减少周围空白
                    progress: {
                        show: true,
                        itemStyle: {
                            color: "#34d399"
                        }
                    },
                    detail: {
                        valueAnimation: true,
                        formatter: '{value} %',
                        color: "#34d399"
                    },
                    itemStyle: {
                        color: "#34d399"
                    },
                    data: [
                        {
                            value: 0,
                            name: t('utilization rate'),
                        }
                    ],
                }
            ]
        };
        option && memoryChart.setOption(option);
    }

    const initProjectDiskChart = () => {
        const chartDom = document.getElementById('projectDiskChart');
        projectDiskChart = echarts.init(chartDom, null, {
            height: 520
        });
        const option = {
            tooltip: {
                formatter: '{a} <br/>{b} : {c}%'
            },
            series: [
                {
                    name: t('hard disk'),
                    type: 'gauge',
                    radius: '90%', // 调整仪表盘大小，使其更大，减少周围空白
                    progress: {
                        show: true,
                        itemStyle: {
                            color: "#38bdf8"
                        }
                    },
                    detail: {
                        valueAnimation: true,
                        formatter: '{value} %',
                        color: "#38bdf8"
                    },
                    itemStyle: {
                        color: "#38bdf8"
                    },
                    data: [
                        {
                            value: 0,
                            name: t('utilization rate'),
                        }
                    ]
                }
            ]
        };
        option && projectDiskChart.setOption(option);
    }

    const refreshSysResources = ()=>{
        GetSysResources().then(({data})=>{
            let memoryUsedPercent = parseFloat(get(data,'memory.usedPercent',0).toFixed(2))
            if(memoryUsedPercent > 100){
                memoryUsedPercent = 100
            }
            let memoryChartColor = "#34d399"
            if (memoryUsedPercent>50 && memoryUsedPercent<=80){
                memoryChartColor = "#f97316"
            }
            if (memoryUsedPercent>80){
                memoryChartColor = "#ef4444"
            }

            memoryChart && memoryChart.setOption({
                series: [
                    {
                        progress: {
                            itemStyle: {
                                color: memoryChartColor
                            }
                        },
                        detail: {
                            color: memoryChartColor
                        },
                        itemStyle: {
                            color: memoryChartColor
                        },
                        data: [
                            {
                                value: memoryUsedPercent,
                            }
                        ]
                    }
                ]
            })

            let projectDiskUsedPercent = parseFloat(get(data,'projectDisk.usedPercent',0).toFixed(2))
            if(projectDiskUsedPercent > 100){
                projectDiskUsedPercent = 100
            }
            let projectDiskChartColor = "#38bdf8"
            if (projectDiskUsedPercent>50 && projectDiskUsedPercent<=80){
                projectDiskChartColor = "#f97316"
            }
            if (projectDiskUsedPercent>80){
                projectDiskChartColor = "#ef4444"
            }
            projectDiskChart && projectDiskChart.setOption({
                series: [
                    {
                        progress: {
                            show: true,
                            itemStyle: {
                                color: projectDiskChartColor
                            }
                        },
                        detail: {
                            valueAnimation: true,
                            formatter: '{value} %',
                            color: projectDiskChartColor
                        },
                        itemStyle: {
                            color: projectDiskChartColor
                        },
                        data: [
                            {
                                value: projectDiskUsedPercent,
                            }
                        ]
                    }
                ]
            })
        }).catch(()=>{})
    }

    const windowResizeHandler = debounce(() => {
        memoryChart.resize();
        projectDiskChart.resize();
    }, 300);

    useEffect(() => {
        initMemoryChart()
        initProjectDiskChart()
        refreshSysResources()
        refreshInterval = setInterval(refreshSysResources,2000)
        window.addEventListener('resize',windowResizeHandler); // 这里设置了一个300毫秒的延迟
        return () => {
            memoryChart && memoryChart.dispose();
            projectDiskChart && projectDiskChart.dispose();
            refreshInterval && clearInterval(refreshInterval)
            window.removeEventListener('resize',windowResizeHandler);
        }
    }, [])

    return (
        <div className={styles.wrapper}>
            <div className={'wrapper-inner'}>
                <div className={'h-full'}>
                    <PageLayout header={t('resource information')}>
                        <div className={'flex'}>
                            <div className={'w-1/3'}>
                                <Card className={'ele-card mx-2'} title={t('memory usage rate')}>
                                    <div id={'memoryChart'}></div>
                                </Card>
                            </div>
                            <div className={'w-1/3'}>
                                <Card className={'ele-card mx-2'} title={t('hard disk utilization rate')}>
                                    <div id={'projectDiskChart'}></div>
                                </Card>
                            </div>
                        </div>
                    </PageLayout>
                </div>
            </div>
        </div>
    )
}