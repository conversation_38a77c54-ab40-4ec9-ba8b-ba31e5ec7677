import styles from './index.module.scss'
import PageLayout from "@/components/PageLayout";
import {App, Button, Form, Switch} from "antd";
import Fieldset from "@/components/Fieldset";
import {useContext, useEffect} from "react";
import {GetSysConfig, UpdateSysConfig} from "@/api/sys_config";
import TransferInstance from "@/utils/app/transfer";
import {useImmer} from "use-immer";
import {get} from "lodash";
import {useTranslation} from "react-i18next";

export default function ConfigGlobal() {
    const { t, i18n } = useTranslation();

    const {message} = App.useApp()
    const context = useContext(TransferInstance.rootContext)
    const [state,updateState] = useImmer({
        institution: '',
        operator: ''
    })
    const [form] = Form.useForm()

    const init = async ()=>{
        context.GlobalLoading(true)
        try {
            const {data} = await GetSysConfig()
            updateState(draft => {
                draft.institution = data.institution
                draft.operator = data.operator
            })
            form.setFieldsValue({
                rev_dl_wm_enabled: data.rev_dl_wm_enabled,
                sender_up_wm_enabled: data.sender_up_wm_enabled
            })
        }catch (e) {
        }finally {
            context.GlobalLoading(false)
        }
    }

    useEffect(()=>{
        init().catch(()=>{})
    },[])

    const handleSubmit = async ()=>{
        context.GlobalLoading(true, t('submitting'))
        try {
            const values = form.getFieldsValue(true)
            await UpdateSysConfig({
                rev_dl_wm_enabled: values.rev_dl_wm_enabled,
                sender_up_wm_enabled: values.sender_up_wm_enabled
            })
            message.success(t('operate success'))
        }catch (e) {
            const msg = get(e,'message', t('operate fail'))
            message.error(msg)
        }finally {
            context.GlobalLoading(false)
        }
    }

    return (
        <div className={styles.wrapper}>
            <div className={'wrapper-inner'}>
                <div className={'h-full'}>
                    <PageLayout header={t('global watermark settings')}>
                        <Form labelCol={{
                            span: 3
                        }} form={form}>
                            <Fieldset legend={t('southbound data cross-border reception')}>
                                <Form.Item name={'rev_dl_wm_enabled'} label={t('file download watermark')} extra={
                                    <div className={'watermark-explain'}>
                                        <div className={'watermark-explain-head'}>
                                            <h2>{t('watermark global setting download explain title')}</h2>
                                            <h3>{t('watermark global setting download explain sub title')}</h3>
                                        </div>
                                        <div className={'watermark-explain-body'}>
                                            <h3>{t('watermark content information')}：</h3>
                                            <ul>
                                                <li>{t('current system belong agency')}：{state.institution}</li>
                                                <li>{t('login user when download')} {state.operator}</li>
                                                <li>{t('download file belongs to patient id')}</li>
                                                <li>{t('watermark time when download file')}（{t('base system auto generate')}）</li>
                                            </ul>
                                        </div>
                                    </div>
                                }>
                                    <Switch checkedChildren={t('open')} unCheckedChildren={t('close')}/>
                                </Form.Item>
                            </Fieldset>
                            <Fieldset legend={t('northbound data cross-border transmission')}>
                                <Form.Item name={'sender_up_wm_enabled'} label={t('file upload watermark')} extra={
                                    <div className={'watermark-explain'}>
                                        <div className={'watermark-explain-head'}>
                                            <h2>{t('watermark global setting upload explain title')}</h2>
                                            <h3>{t('watermark global setting upload explain sub title')}</h3>
                                        </div>
                                        <div className={'watermark-explain-body'}>
                                            <h3>{t('watermark content information')}：</h3>
                                            <ul>
                                                <li>{t('current system belong agency')}：{state.institution}</li>
                                                <li>{t('login user when upload')} {state.operator}</li>
                                                <li>{t('watermark time when upload file')}（{t('base system auto generate')}）</li>
                                            </ul>
                                        </div>
                                    </div>
                                }>
                                        <Switch checkedChildren={t('open')} unCheckedChildren={t('close')} />
                                    </Form.Item>
                            </Fieldset>
                            <Form.Item wrapperCol={{offset: 3}}>
                                <div className={'actions'}>
                                    <Button onClick={handleSubmit} type={'primary'} className={'act-submit'}>{t('submit')}</Button>
                                </div>
                            </Form.Item>
                        </Form>
                    </PageLayout>
                </div>
            </div>
        </div>
    )
}