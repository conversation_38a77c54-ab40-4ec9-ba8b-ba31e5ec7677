.wrapper:global{
  height: 100%;
  overflow: hidden;
  >.wrapper-inner{
    height: 100%;
    overflow: hidden;

    .page-layout{
      background: none;
      >.page-container{
        background: none;
        padding: 12px 12px;

        >.container-inner{
          //box-shadow: 0 1px 2px 0 rgba(54, 58, 80, .22);
          //box-shadow: 0 1px 2px 0 rgba(54, 58, 80, .22);
          display: flex;
          flex-direction: column;
          background: none;
          >.container-body{
            flex: 1;
            position: relative;
            //padding: 24px 48px;
            //padding-left: 100px;
            padding: 0;

            .channel-wrapper{
              >ul{
                display: flex;
                flex-direction: column;
                gap: 24px;
                >li{
                  //width: 400px;
                  >.channel-item{
                    >.channel-item-inner{
                      >.ant-card{
                        border-radius: 6px;
                        box-shadow: 0 1px 0 0 rgba(54, 58, 80, .22);
                        >.ant-card-body{
                          >.channel-head{
                            >.channel-name{
                              font-weight: bold;
                              font-size: 16px;
                            }
                            >.channel-tags{
                              margin-top: 6px;
                            }
                          }
                          >.channel-data{
                            margin-top: 24px;
                            >ul{
                              >li{
                                >.channel-data-field{
                                  display: flex;
                                  align-items: center;
                                  gap: 8px;
                                  >.label{
                                    color: #888888;
                                  }
                                  >.value{
                                  }
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }

          }
        }
      }
    }
  }
}
