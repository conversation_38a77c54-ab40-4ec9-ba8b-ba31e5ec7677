.wrapper:global {
  height: 100%;
  overflow: hidden;

  >.wrapper-inner {
    height: 100%;
    overflow: hidden;

    .page-layout {
      background: none;

      >.page-container {
        background: none;
        padding: 12px 12px;

        >.container-inner {
          box-shadow: 0 1px 2px 0 rgba(54, 58, 80, .22);
          //box-shadow: 0 1px 2px 0 rgba(54, 58, 80, .22);
          display: flex;
          flex-direction: column;

          >.container-body {
            flex: 1;
            position: relative;
            padding: 24px 48px;
            padding-left: 100px;

            >.ant-form {

              //width: 620px;
              //margin: 0 auto;
              .ant-form-item {
                .watermark-explain {
                  margin-top: 8px;
                  font-size: 12px;

                  >.watermark-explain-head {
                    >h2 {
                      font-weight: bold;
                      color: #3366ff;
                    }

                    >h3 {
                      //color: #606266;
                      //margin-left: 12px;
                    }
                  }

                  >.watermark-explain-body {
                    >h3 {
                      color: #3366ff;
                      font-weight: bold;
                    }

                    >ul {
                      list-style: circle;
                      margin-left: 18px;

                      >li {
                        line-height: 24px;
                      }
                    }
                  }
                }
              }

              .actions {
                .act-submit {
                  height: auto;
                  padding: 8px 36px;
                }
              }

              .sftp-config {
                .ant-card {

                  &.auth-config,
                  &.upload-config {
                    width: 680px;

                    >.ant-card-head {
                      border-bottom: none;
                    }
                  }

                  &.upload-config {
                    margin-top: 12px;
                  }
                }
              }

              .dicom-config {
                .ant-card {

                  &.auth-config,
                  &.upload-config {
                    width: 680px;

                    >.ant-card-head {
                      border-bottom: none;
                    }
                  }

                  &.upload-config {
                    margin-top: 12px;
                  }
                }
              }

              .ip-list {
                width: 630px;

                .ant-list-item {
                  display: flex;
                  align-items: flex-start;

                  .ant-list-item-main {
                    flex: 1;
                    min-width: 0; // 防止内容溢出
                  }

                  .ant-list-item-action {
                    flex-shrink: 0;
                    margin-left: 16px;
                  }
                }

                // IP配置项的布局样式
                .ip-config-content {
                  display: flex;
                  gap: 16px;
                  max-width: 480px;

                  .ip-field {
                    &.host-field {
                      flex: 0 0 120px;
                    }

                    &.port-field {
                      flex: 0 0 120px;
                    }

                    &.username-field,
                    &.aet-field {
                      flex: 0 0 120px;
                    }

                    .field-label {
                      font-weight: bold;
                      margin-bottom: 4px;
                    }

                    .field-value {
                      word-break: break-all;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}