import { useTranslation } from "react-i18next";
import { split, startsWith, trim } from "lodash";

export function useFormRules() {
    const { t, i18n } = useTranslation();

    return {
        SFtp: {
            Auth: {
                Host: [
                    { required: true, message: t('please input the sftp host'), trigger: "change", whitespace: true, }
                ],
                Port: [
                    { required: true, type: 'integer', message: t('please input the sftp port'), trigger: "change" }
                ],
                Username: [
                    { required: true, message: t('please input the sftp username'), trigger: "change", whitespace: true, }
                ],
                Password: [
                    { required: true, message: t('please input the sftp password'), trigger: "change", whitespace: true, }
                ],
                Timeout: [
                    { required: true, type: 'integer', message: t('please input the sftp connect timeout'), trigger: "change" }
                ],
                Type: [
                    {
                        required: true,
                        validator: (_, value) => {
                            if (value !== 0 && value !== 1) {
                                return Promise.reject(new Error(t('please select authentication method')))
                            }
                            return Promise.resolve();
                        }
                    }
                ],
                PrivateKeyType: [
                    {
                        required: true,
                        validator: (_, value) => {
                            if (value !== 0 && value !== 1) {
                                return Promise.reject(new Error(t('please select private key type')))
                            }
                            return Promise.resolve();
                        }
                    }
                ],
                PrivateKeyContent: [
                    { required: true, message: t('please input the sftp private key content'), trigger: "change", whitespace: true, }
                ],
                PrivateKeyPath: [
                    { required: true, message: t('please input the sftp private key path'), trigger: "change", whitespace: true, }
                ],
            },
            Upload: {
                dst_dir: [
                    {
                        validator: (rule, value, callback) => {
                            value = trim(value || '');
                            if (!value) {
                                return Promise.reject(new Error(t('please input the dst dir')))
                            }
                            if (!startsWith(value, '/')) {
                                return Promise.reject(new Error(t('dst dir must prefix with s')))
                            }
                            return Promise.resolve();
                        }
                    }
                ],
                AllowedFileTypes: [
                    { required: true, message: t('please input the allowed file types'), trigger: "change", whitespace: true, }
                ]
            }
        },
        Dicom: {
            Auth: {
                ServerAet: [
                    { required: true, message: t('please input the dicom server aet'), trigger: "change", whitespace: true, }
                ],
                ServerIp: [
                    { required: true, message: t('please input the dicom server ip'), trigger: "change", whitespace: true, }
                ],
                ServerPort: [
                    { required: true, type: 'integer', message: t('please input the dicom server port'), trigger: "change" }
                ],
            },
            Upload: {
                AllowedFileTypes: [
                    { required: true, message: t('please input the allowed file types'), trigger: "change", whitespace: true, }
                ],
                UploadAddress: [
                    { required: true, message: t('please input the upload address'), trigger: "change", whitespace: true, }
                ]
            }
        }
    }
}
