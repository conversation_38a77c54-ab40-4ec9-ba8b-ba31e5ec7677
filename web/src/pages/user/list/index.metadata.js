import {attempt, get, keyBy} from "lodash";
import {Divider, Tag} from "antd";
import {CheckCircleOutlined, ExclamationCircleOutlined, ClockCircleOutlined, CopyOutlined} from '@ant-design/icons'
import {useTranslation} from "react-i18next";

const UserStatuses = [
    {label: 'unknown', value: 0, color: 'default', icon: <ClockCircleOutlined/>},
    {label: 'enabled', value: 1, color: 'success', icon: <CheckCircleOutlined/>},
    {label: 'disabled', value: -1, color: 'error', icon: <ExclamationCircleOutlined/>},
]

const UserStatusesMap = keyBy(UserStatuses, "value")

export function useCreateTableColumns(
    {
        onToggleStatus,
        onUpdate,
        onDelete,
        currentUser
    }
) {
    const { t, i18n } = useTranslation();

    return [
        {
            title: t('username'),
            key: 'username',
            width: 160,
            fixed: 'left',
            render: record => {
                return (
                    <div className={'username'}>
                        {record.username}
                    </div>
                )
            }
        },
        {
            title: t('real name'),
            key: "real_name",
            width: 160,
            render: record => {
                return (
                    <div className={'real-name'}>
                        {record.real_name || '-'}
                    </div>
                )
            }
        },
        {
            title: t('id number'),
            key: "id_number",
            width: 200,
            render: record => {
                return (
                    <div className={'id-number'}>
                        {record.id_number || '-'}
                    </div>
                )
            }
        },
        {
            title: t('email'),
            key: "email",
            width: 160,
            render: record => {
                return (
                    <div className={'email'}>
                        {record.email || '-'}
                    </div>
                )
            }
        },
        {
            title: t('mobile'),
            key: "phone",
            width: 120,
            render: record => {
                return (
                    <div className={'phone'}>
                        {record.phone || '-'}
                    </div>
                )
            }
        },
        {
            title: t('status'),
            key: "status",
            width: 120,
            align: 'center',
            render: record => {
                const v = UserStatusesMap[record.status]
                if (!v) {
                    return (<span>-</span>)
                }
                return (
                    <div className={'user-status'}>
                        <Tag bordered={false} color={v.color} icon={v.icon}>{t(v.label)}</Tag>
                    </div>
                )
            }
        },
        {
            title: t('created time'),
            key: "created_at",
            width: 160,
            render: record => {
                return (
                    <div className={'timestamp'}>
                        {record.created_at}
                    </div>
                )
            }
        },
        {
            title: t('updated time'),
            key: "updated_at",
            width: 160,
            render: record => {
                return (
                    <div className={'timestamp'}>
                        {record.updated_at}
                    </div>
                )
            }
        },
        {
            title: t('action'),
            width: 150,
            fixed: "right",
            className: "actions",
            align: "center",
            render: record => {
                if (get(currentUser,'is_admin')){
                    if (record.is_admin) {
                        return (
                            <>
                                <a onClick={() => attempt(onUpdate, record)}>{t('modify')}</a>
                            </>
                        )
                    }
                    return (
                        <>
                            {record.status===1 ? (
                                <a onClick={() => attempt(onToggleStatus, record)}>{t('disable')}</a>
                            ): null}
                            {record.status===-1? (
                                <a onClick={() => attempt(onToggleStatus, record)}>{t('enable')}</a>
                            ): null}
                            <Divider type={"vertical"}/>
                            <a onClick={() => attempt(onUpdate, record)}>{t('modify')}</a>
                            <Divider type={"vertical"}/>
                            <a onClick={() => attempt(onDelete, record)}>{t('delete')}</a>
                        </>
                    )
                }else{
                    if (record.username===get(currentUser,'username')) {
                        return (
                            <>
                                {/*{record.status===1 ? (*/}
                                {/*    <a onClick={() => attempt(onToggleStatus, record)}>停用</a>*/}
                                {/*): null}*/}
                                {/*{record.status===-1? (*/}
                                {/*    <a onClick={() => attempt(onToggleStatus, record)}>启用</a>*/}
                                {/*): null}*/}
                                {/*<Divider type={"vertical"}/>*/}
                                <a onClick={() => attempt(onUpdate, record)}>{t('modify')}</a>
                                {/*<Divider type={"vertical"}/>*/}
                                {/*<a onClick={() => attempt(onDelete, record)}>删除</a>*/}
                            </>
                        )
                    }else{
                        return (<></>)
                    }
                }
                return (
                    <>
                        {record.status===1 ? (
                            <a onClick={() => attempt(onToggleStatus, record)}>{t('disable')}</a>
                        ): null}
                        {record.status===-1? (
                            <a onClick={() => attempt(onToggleStatus, record)}>{t('enable')}</a>
                        ): null}
                        <Divider type={"vertical"}/>
                        <a onClick={() => attempt(onUpdate, record)}>{t('modify')}</a>
                        <Divider type={"vertical"}/>
                        <a onClick={() => attempt(onDelete, record)}>{t('delete')}</a>
                    </>
                )
            }
        },
    ]
}