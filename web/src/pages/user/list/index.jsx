import {Button, Form, Input, Table, Row, Col, Divider, App, DatePicker, Select} from "antd";
import {
    LoadingOutlined,
    SyncOutlined,
    PlusCircleOutlined,
    SearchOutlined,
    ReloadOutlined,
    ExclamationCircleFilled
} from '@ant-design/icons'
import {useFetcher} from "@/components/Fetcher";
import PageLayout from '@/components/PageLayout'
import {useContext, useEffect, useRef} from "react";
import {trim, get, has, omitBy, isArray} from "lodash";
import {HandleTableChange} from "@/utils/table";
import {PAGINATION, TABLE_STICKY_OFFSET_HEADER} from "@/settings";
import styles from './index.module.scss'
import {useCreateTableColumns} from "./index.metadata";
import {useImmer} from "use-immer";
import dayjs from "dayjs";
import {FormatToDatetime} from "@/utils/date";
import {CreateU<PERSON>, DeleteUser, FindUser, GetUserList, ToggleUserStatus, UpdateUser} from "@/api/user";
import InfoFormComponent from './components/info_form'
import {RootContext} from "@/provider/root";
import {useTranslation} from "react-i18next";

let stopSearchListen = false;
export default function Homepage() {
    const { t, i18n } = useTranslation();

    const rootContext = useContext(RootContext)
    const {modal, message} = App.useApp()
    const [state, updateState] = useImmer({
        initialized: false,
        info: {
            visible: false,
            title: '',
            loading: false,
            initialValues: {},
            isEdit: false,
            isAdmin: false,
            allowUpdateStatus: false
        }
    })

    const fetcher = useFetcher();
    const layoutRef = useRef()

    const handleDelete = (record) => {
        modal.confirm({
            title: t('tip'),
            icon: <ExclamationCircleFilled />,
            content: t('delete user determination'),
            okType: 'danger',
            onOk() {
                return new Promise((resolve, reject) => {
                    DeleteUser({
                        username: record.username,
                    }).then(() => {
                        message.success(t('operate success'))
                    }).catch((e) => {
                        modal.error({
                            title: t('error'),
                            content: e.message || t('operate fail'),
                            centered: true,
                            maskClosable: false,
                        })
                    }).finally(()=>{
                        loadTableData()
                        resolve()
                    })
                })
            },
            onCancel() {},
        });
    }

    const handleToggleStatus = (record) => {
        modal.confirm({
            title: t('tip'),
            icon: <ExclamationCircleFilled />,
            content: `${t('determination')}${record.status===1?t('disable'):t('enable')}${t('this account query')}`,
            onOk() {
                return new Promise((resolve, reject) => {
                    ToggleUserStatus({
                        username: record.username,
                        status: record.status===1?-1:1
                    }).then(() => {
                        message.success(t('operate success'))
                    }).catch((e) => {
                        modal.error({
                            title: t('error'),
                            content: e.message || t('operate fail'),
                            centered: true,
                            maskClosable: false,
                        })
                    }).finally(()=>{
                        loadTableData()
                        resolve()
                    })
                })
            },
            onCancel() {},
        });
    }


    const loadTableData = async () => {
        fetcher.fetch().then(({data}) => {
            if (!data.data || data.data.length === 0) {
                let page = get(fetcher.params, 'pagination.' + PAGINATION.currentPageKey, 1)
                if (page > 1) {
                    page -= 1
                    const pageSize = get(fetcher.params, 'pagination.' + PAGINATION.pageSizeKey, 10)
                    fetcher.setParams({
                        ...fetcher.params,
                        pagination: {
                            [PAGINATION.currentPageKey]: page,
                            [PAGINATION.pageSizeKey]: pageSize,
                        }
                    })
                    loadTableData()
                    return
                }
            }
            updateState(draft => {
                draft.initialized = true
            })
        }).catch((e) => {
            const code = get(e, 'code')
            const message = get(e, 'message')
            if (code === 500) {
                modal.warning({
                    title: message || t('system error'),
                    centered: true,
                    maskClosable: true,
                })
            }
        }).finally(() => {
        })
    }

    const handleTableChange = (pagination, filters, sorter, extra) => {
        HandleTableChange(fetcher, {pagination, filters, sorter, extra, loadFunc: loadTableData})
    }

    const initialize = () => {
        fetcher.set(GetUserList, {
            pagination: {
                [PAGINATION.currentPageKey]: PAGINATION.defaultCurrentPage,
                [PAGINATION.pageSizeKey]: PAGINATION.defaultPageSize,
            }
        })
        loadTableData()
    }

    useEffect(() => {
        initialize()
    }, [])

    // ----------------------------------- upload component -----------------------------
    const handleNewUser = () => {
        updateState(draft => {
            draft.info.loading = false
            draft.info.visible = true;
            draft.info.title = t('new User')
            draft.info.isEdit = false
            draft.info.isAdmin = false
            draft.info.allowUpdateStatus = true
            draft.info.initialValues = {
                enabled: true
            }
        })
    }
    const handleEditUser = (record) => {
        updateState(draft => {
            draft.info.isEdit = true
            draft.info.loading = true
            draft.info.visible = true
            draft.info.title = `编辑用户 ${record.username}`
            draft.info.isAdmin = record.is_admin
            draft.info.allowUpdateStatus = get(rootContext.user,'is_admin')
        })
        FindUser({username: record.username}).then(({data}) => {
            data.enabled = data.status===1
            updateState(draft => {
                draft.info.initialValues = data
                draft.info.loading = false
            })
        }).catch(e => {
            modal.error({
                title: t('error'),
                content: e.message || t('operate fail'),
                centered: true,
                maskClosable: false,
            })
        })
    }

    const handleInfoFormCancel = () => {
        updateState(draft => {
            draft.info.visible = false;
        })
    }
    const handleInfoFormOk = (values) => {
        updateState(draft => {
            draft.info.loading = true
        })
        if (values.id) {
            UpdateUser(values).then(() => {
                message.success(t('operate success'))
                updateState(draft => {
                    draft.info.visible = false
                    draft.info.loading = false
                })
                loadTableData()
            }).catch((e) => {
                updateState(draft => {
                    draft.info.loading = false
                })
                modal.error({
                    title: t('error'),
                    content: e.message || t('operate fail'),
                    centered: true,
                    maskClosable: false,
                })
            })
        } else {
            CreateUser(values).then(() => {
                message.success(t('operate success'))
                updateState(draft => {
                    draft.info.visible = false
                    draft.info.loading = false
                })
                loadTableData()
            }).catch((e) => {
                updateState(draft => {
                    draft.info.loading = false
                })
                modal.error({
                    title: t('error'),
                    content: e.message || t('operate fail'),
                    centered: true,
                    maskClosable: false,
                })
            })
        }
    }
    // ----------------------------------- upload component -----------------------------

    // ----------------------------------- search -----------------------------
    const [searchState, updateSearchState] = useImmer({
        account: ""
    })
    const [searchForm] = Form.useForm();
    const handleSearch = () => {
        console.log("state.initialized:",state.initialized)
        if (!state.initialized) return;
        if (stopSearchListen) return;

        let values = searchForm.getFieldsValue(true) || {}
        if (has(values, "account")) {
            values.account = trim(values.account)
        }
        let params = {
            ...fetcher.params,
            ...values
        }
        if (has(params, "created_at")) {
            delete params.created_at
        }

        const oldAccount = get(fetcher.params, "account", "")
        const newAccount = get(values, "account", "")

        const oldTimeStart = get(fetcher.params, "created_at_start", "")
        const oldTimeEnd = get(fetcher.params, "created_at_end", "")

        let newTimeStart = ""
        let newTimeEnd = ""
        if (isArray(values.created_at) && values.created_at.length === 2) {
            newTimeStart = FormatToDatetime(values.created_at[0])
            newTimeEnd = FormatToDatetime(values.created_at[1].add(1, 'd'))
        }
        params["created_at_start"] = newTimeStart
        params["created_at_end"] = newTimeEnd

        if (
            oldAccount !== newAccount ||
            oldTimeStart !== newTimeStart ||
            oldTimeEnd !== newTimeEnd
        ) {
            params = {
                ...params,
                pagination: {
                    [PAGINATION.currentPageKey]: PAGINATION.defaultCurrentPage,
                    [PAGINATION.pageSizeKey]: PAGINATION.defaultPageSize,
                }
            }
        }
        params = omitBy(params, value => !value)
        fetcher.setParams(params)

        loadTableData()
    }
    const searchFormAccount = Form.useWatch(["account"], searchForm)
    useEffect(() => {
        if (typeof searchFormAccount === "undefined") return;
        const v = trim(searchFormAccount)
        updateSearchState(draft => {
            draft.account = v
        })
        if (!v) {
            if (searchState.account) {
                handleSearch()
            }
        }
    }, [searchFormAccount])
    const handleSearchFormAccountOnChange = e => {
        if (e.type === 'click') {
            if (searchFormAccount) {
                if (!trim(e.target.value)) {
                    searchForm.setFieldValue(["account"], '')
                }
            }
        }
    }

    const searchFormCreatedAt = Form.useWatch(["created_at"], searchForm)
    useEffect(() => {
        if (typeof searchFormCreatedAt === "undefined") return;
        handleSearch()
    }, [searchFormCreatedAt])

    const handleSearchFormStringPressEnter = e => {
        const val = trim(e.target.value)
        if (!val) return;
        handleSearch()
    }

    const handleSearchFormReset = () => {
        stopSearchListen = true;

        searchForm.setFieldsValue({
            account: '',
            created_at: [],
        })
        setTimeout(() => {
            stopSearchListen = false;
            handleSearch()
        }, 0)
    }
    // ----------------------------------- search -----------------------------

    const tableColumns = useCreateTableColumns({
        onDelete: handleDelete,
        onUpdate: handleEditUser,
        onToggleStatus: handleToggleStatus,
        currentUser: rootContext.user
    });

    return (
        <div className={styles.wrapper}>
            <div className={"wrapper-inner " + (!fetcher.total ? " no-data" : "")}>
                <div className={'h-full'}>
                    <PageLayout header={
                        <div className={'flex items-center'}>
                            <span>{t('user Management')}</span>
                        </div>
                    } ref={layoutRef} action={
                        <>
                            <Button type={"primary"} onClick={handleNewUser}
                                    icon={<PlusCircleOutlined/>}>{t('new User')}</Button>
                        </>
                    } search={<>
                        <Form form={searchForm}  labelCol={{
                            style:{
                                width: '80px'
                            }
                        }} labelAlign={"left"} initialValues={{}}>
                            <Row gutter={18}>
                                <Col span={8}>
                                    <Form.Item name={"account"} label={t('account')}>
                                        <Input placeholder={`${t('username')}、${t('mobile')}、${t('mail')}`} variant={'filled'} allowClear={true}
                                               onPressEnter={handleSearchFormStringPressEnter}
                                               onChange={handleSearchFormAccountOnChange}/>
                                    </Form.Item>
                                </Col>
                                <Col span={8}>
                                    <Form.Item name={"created_at"} label={t('created time')}>
                                        <DatePicker.RangePicker variant={'filled'} style={{
                                            width: "100%"
                                        }} presets={[
                                            {
                                                label: t('today'),
                                                value: [dayjs().startOf('d'), dayjs().startOf('d')],
                                            },
                                            {
                                                label: t('yesterday'),
                                                value: [dayjs().add(-1, 'd').startOf('d'), dayjs().add(-1, 'd').startOf('d')],
                                            },
                                            {
                                                label: t('last 7 days'),
                                                value: [dayjs().add(-6, 'd').startOf('d'), dayjs().startOf('d')],
                                            },
                                            {
                                                label: t('last 15 days'),
                                                value: [dayjs().add(-14, 'd').startOf('d'), dayjs().startOf('d')],
                                            },
                                        ]} disabledDate={(current) => {
                                            return current && (
                                                current > dayjs().startOf('day')
                                            )
                                        }}/>
                                    </Form.Item>
                                </Col>
                            </Row>
                        </Form>
                        <Divider type={"vertical"}/>
                        <div className={"actions"}>
                            <Button type={"primary"} icon={<SearchOutlined/>} onClick={handleSearch}>{t('query')}</Button>
                            <Button icon={<ReloadOutlined/>} onClick={handleSearchFormReset}>{t('reset')}</Button>
                        </div>
                    </>} actionExtra={<Button icon={<SyncOutlined/>} title={t('refresh')} onClick={loadTableData}/>}>
                        <Table rowKey={"id"}
                               bordered={false}
                               columns={tableColumns}
                               dataSource={fetcher.rows}
                               onChange={handleTableChange}
                               loading={{
                                   spinning: fetcher.loading,
                                   indicator: (<LoadingOutlined
                                       style={{
                                           fontSize: 36,
                                       }}
                                       spin
                                   />),
                                   tip: t('loading')
                               }}
                               className={fetcher.total ? "" : " no-data"}
                               locale={{
                                   emptyText: (
                                       <div className={"empty"}>
                                           <div className={"empty-inner"}>
                                               <div className={"empty-action"}>
                                                   <Button size={'large'} className={"create-btn"} type={"primary"}
                                                           icon={<PlusCircleOutlined/>}
                                                           onClick={handleNewUser}>{t('new User')}</Button>
                                               </div>
                                           </div>
                                       </div>
                                   )
                               }}
                               sticky={{offsetHeader: TABLE_STICKY_OFFSET_HEADER}}
                               scroll={{x: 'max-content'}}
                               pagination={{
                                   ...PAGINATION,
                                   total: fetcher.total,
                                   current: get(fetcher.params, 'pagination.' + PAGINATION.currentPageKey, 1),
                                   pageSize: get(fetcher.params, 'pagination.' + PAGINATION.pageSizeKey, 10),
                                   hideOnSinglePage: true,
                                   showQuickJumper: false
                               }}
                        />
                    </PageLayout>
                </div>
            </div>
            <InfoFormComponent {...state.info} onOk={handleInfoFormOk} onCancel={handleInfoFormCancel}/>
        </div>
    )
}