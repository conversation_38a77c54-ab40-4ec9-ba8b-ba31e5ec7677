import {trim} from "lodash";
import {EmailIsValid, IdCardNumberIsValid, MobileIsValid} from "@/utils/validator";
import {useTranslation} from "react-i18next";

export function useFormRules() {
    const { t, i18n } = useTranslation();

    return {
        username: [
            {
                validator: (rule, value, callback) => {
                    value = trim(value || '');
                    if (!value) return Promise.reject(new Error(t('username cannot empty')))
                    return Promise.resolve()
                }
            }
        ],
        password: [
            {
                validator: (rule, value, callback) => {
                    value = trim(value || '');
                    if (!value) return Promise.reject(new Error(t('password cannot empty')))
                    return Promise.resolve()
                }
            }
        ],
        id_number: [
            {
                validator: (rule, value, callback) => {
                    value = trim(value || '');
                    if (!value) return Promise.resolve();
                    if (!IdCardNumberIsValid(value)) return Promise.reject(new Error(t('id number illegal')))
                    return Promise.resolve()
                }
            }
        ],
        email: [
            {
                validator: (rule, value, callback) => {
                    value = trim(value || '');
                    if (!value) return Promise.resolve();
                    if (!EmailIsValid(value)) return Promise.reject(new Error(t('email illegal')))
                    return Promise.resolve()
                }
            }
        ],
        phone: [
            {
                validator: (rule, value, callback) => {
                    value = trim(value || '');
                    if (!value) return Promise.resolve();
                    if (!MobileIsValid(value)) return Promise.reject(new Error(t('mobile illegal')))
                    return Promise.resolve()
                }
            }
        ]
    }
}