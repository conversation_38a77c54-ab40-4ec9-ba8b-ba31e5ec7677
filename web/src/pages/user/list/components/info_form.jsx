import styles from "./info_form.module.scss";
import {App, Button, Col, Form, Input, Row, Space, Switch} from "antd";
import Dialog from "@/components/Dialog";
import {useFormRules} from './info_form.metadata'
import {useEffect} from "react";
import {isArray} from "lodash";
import {useTranslation} from "react-i18next";

export default function InfoForm({
                                     visible = false,
                                     title,
                                     loading = false,
                                     onOk,
                                     onCancel,
                                     initialValues = {},
                                     isEdit = false,
    isAdmin = false,
    allowUpdateStatus = false
                                 }) {
    const { t, i18n } = useTranslation();
    const FormRules = useFormRules()

    const {message, modal} = App.useApp();

    const [form] = Form.useForm()

    const handleFinish = (values) => {
        onOk(values)
    }

    const handleFinishFailed = (e) => {
        let msg = t('check form')
        if (isArray(e.errorFields) && e.errorFields.length > 0) {
            const firstField = e.errorFields[0]
            if (isArray(firstField.errors) && firstField.errors.length > 0) {
                msg = firstField.errors[0]
            }
        }
        modal.warning({
            title: t('tip'),
            content: msg,
            centered: true,
            maskClosable: true,
        })
    }

    useEffect(() => {
        if (!visible) return;
        form.resetFields()
        form.setFieldsValue(initialValues)
    }, [visible, initialValues])

    return (
        <Dialog closable={!loading} className={'123'} rootClassName={styles.wrapper} title={title} visible={visible}
                onOk={form.submit} onCancel={onCancel}
                loading={loading}
                width={580}
        >
            <Form form={form} onFinish={handleFinish} onFinishFailed={handleFinishFailed}
                  layout={'vertical'}
                  labelAlign={"right"}
            >
                <Row gutter={12}>
                    <Col span={12}>
                        <Form.Item label={t('username')} name={'username'} rules={FormRules.username} required={true}>
                            <Input variant={'filled'} disabled={isEdit} placeholder={t('please enter the username')}/>
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        {isEdit ? (
                            <Form.Item label={t('password')} name={'password'}>
                                <Input.Password variant={'filled'} placeholder={t('unchanged no change')}/>
                            </Form.Item>
                        ) : (
                            <Form.Item label={t('password')} name={'password'} rules={FormRules.password} required={true}>
                                <Input.Password variant={'filled'} placeholder={t('please enter the password')}/>
                            </Form.Item>
                        )}
                    </Col>
                    {!isAdmin? (
                        <>
                            <Col span={12}>
                                <Form.Item label={t('real name')} name={"real_name"}>
                                    <Input variant={'filled'} placeholder={t('please enter the real name')}/>
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item label={t('id number')} name={"id_number"} rules={FormRules.id_number}>
                                    <Input variant={'filled'} placeholder={t('please enter the id number')}/>
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item label={t('email')} name={"email"} rules={FormRules.email}>
                                    <Input variant={'filled'} placeholder={t('please enter the email')}/>
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item label={t('mobile')} name={"phone"} rules={FormRules.phone}>
                                    <Input variant={'filled'} placeholder={t('please enter the mobile')}/>
                                </Form.Item>
                            </Col>
                            {allowUpdateStatus?(
                                <Col span={12}>
                                    <Form.Item label={t('status')} name={"enabled"} valuePropName={'checked'}>
                                        <Switch checkedChildren={t('enable')} unCheckedChildren={t('disable')} />
                                    </Form.Item>
                                </Col>
                            ):null}
                        </>
                    ): null}
                </Row>
                <Form.Item name={"id"} hidden={true}>
                    <Input type={'hidden'} />
                </Form.Item>
            </Form>
        </Dialog>
    )
}