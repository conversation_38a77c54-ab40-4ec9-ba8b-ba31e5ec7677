import {Button, Form, Input, Table, Row, Col, Divider, App, DatePicker, Select, Flex, Tag} from "antd";
import {LoadingOutlined, SyncOutlined, LockOutlined, SearchOutlined, ReloadOutlined} from '@ant-design/icons'
import {useFetcher} from "@/components/Fetcher";
import PageLayout from '@/components/PageLayout'
import {useContext, useEffect, useRef} from "react";
import {trim, get, has, omitBy, isArray, uniq, keyBy} from "lodash";
import {HandleTableChange} from "@/utils/table";
import {PAGINATION, TABLE_STICKY_OFFSET_HEADER} from "@/settings";
import styles from './homepage.module.scss'
import {useCreateTableColumns} from "./homepage.metadata";
import {useImmer} from "use-immer";
import {
    DownloadChildFile,
    GetChildFileLatestDownloadRecord,
    GetChildFiles,
    GetFileList, GetPreviewChildFileWatermarkInfo,
    ToggleArchived
} from "@/api/rev/file";
import ClipboardJS from "clipboard";
import TransferInstance from "@/utils/app/transfer";
import {FormatFileSize} from "@/utils/size";
import {RootContext} from "@/provider/root";
import InputFilePwd from "./components/InputFilePwd";
import dayjs from "dayjs";
import {FormatToDatetime} from "@/utils/date";
import {GetSenderListAll} from "@/api/rev/sender";
import {useTranslation} from "react-i18next";

let stopSearchListen = false;
export default function Homepage() {
    const { t, i18n } = useTranslation();

    const {message, modal} = App.useApp()
    const context = useContext(TransferInstance.rootContext)
    const rootContext = useContext(RootContext)

    const [state, updateState] = useImmer({
        initialized: false,
        childFiles: {},
        expandedRowKeys: [],
        rowSelection: {
            selectedRowKeys: []
        },
        inputFilePwd: {
            visible: false,
            loading: false,
            recordId: '',
            fileId: '',
            needPassword: false,
            sysConfig:{}
        },
        search: {
            sender: {
                options: [
                    {label: t('all'), value: ''}
                ]
            }
        }
    })

    const fetcher = useFetcher();
    const layoutRef = useRef()

    const [searchState, updateSearchState] = useImmer({
        filename: "",
        // file_hash: "",
        // sender: "",
        send_code: '',
        // send_at: [],
        // archived: false,
    })
    const [searchForm] = Form.useForm();
    const handleSearch = () => {
        if (!state.initialized) return;
        if (stopSearchListen) return;
        let values = searchForm.getFieldsValue(true) || {}
        if (has(values, "filename")) {
            values.filename = trim(values.filename)
        }
        // if (has(values, "file_hash")) {
        //     values.file_hash = trim(values.file_hash)
        // }
        // if (has(values, "sender")) {
        //     values.sender = trim(values.sender)
        // }
        if (has(values, "send_code")) {
            values.send_code = trim(values.send_code)
        }
        let params = {
            ...fetcher.params,
            ...values
        }
        if (has(params, "send_at")) {
            delete params.send_at
        }

        const oldFilename = get(fetcher.params, "filename", "")
        const newFilename = get(values, "filename", "")

        // const oldFileHash = get(fetcher.params, "file_hash", "")
        // const newFileHash = get(values, "file_hash", "")

        const oldSender = get(fetcher.params, "sender", "")
        const newSender = get(values, "sender", "")

        const oldSendCode = get(fetcher.params, "send_code", "")
        const newSendCode = get(values, "send_code", "")

        const oldTimeStart = get(fetcher.params, "send_at_start", "")
        const oldTimeEnd = get(fetcher.params, "send_at_end", "")


        let newTimeStart = ""
        let newTimeEnd = ""
        if (isArray(values.send_at) && values.send_at.length === 2) {
            newTimeStart = FormatToDatetime(values.send_at[0])
            newTimeEnd = FormatToDatetime(values.send_at[1].add(1, 'd'))
        }
        params["send_at_start"] = newTimeStart
        params["send_at_end"] = newTimeEnd

        const oldArchived = get(fetcher.params, "archived", false)
        const newArchived = get(values, "archived", false)

        if (
            oldFilename !== newFilename ||
            // oldFileHash !== newFileHash ||
            oldSender !== newSender ||
            oldSendCode !== newSendCode ||
            oldTimeStart !== newTimeStart ||
            oldTimeEnd !== newTimeEnd ||
            oldArchived !== newArchived
        ) {
            params = {
                ...params,
                pagination: {
                    [PAGINATION.currentPageKey]: PAGINATION.defaultCurrentPage,
                    [PAGINATION.pageSizeKey]: PAGINATION.defaultPageSize,
                }
            }
        }
        params = omitBy(params, value => !value)
        fetcher.setParams(params)

        loadTableData()
    }

    const searchFormFilename = Form.useWatch(["filename"], searchForm)
    useEffect(() => {
        if (typeof searchFormFilename === "undefined") return;
        const v = trim(searchFormFilename)
        updateSearchState(draft => {
            draft.filename = v
        })
        if (!v) {
            if (searchState.filename) {
                handleSearch()
            }
        }
    }, [searchFormFilename])
    const handleSearchFilenameOnChange = e => {
        if (e.type === 'click') {
            if (searchFormFilename) {
                if (!trim(e.target.value)) {
                    searchForm.setFieldValue(["filename"], '')
                }
            }
        }
    }

    // const searchFormFileHash = Form.useWatch(["file_hash"], searchForm)
    // useEffect(() => {
    //     if (typeof searchFormFileHash === "undefined") return;
    //     const v = trim(searchFormFileHash)
    //     updateSearchState(draft => {
    //         draft.file_hash = v
    //     })
    //     if (!v) {
    //         if (searchState.file_hash) {
    //             handleSearch()
    //         }
    //     }
    // }, [searchFormFileHash])
    // const handleSearchFileHashOnChange = e => {
    //     if (e.type === 'click') {
    //         if (searchFormFileHash) {
    //             if (!trim(e.target.value)) {
    //                 searchForm.setFieldValue(["file_hash"], '')
    //             }
    //         }
    //     }
    // }

    const searchFormSender = Form.useWatch(["sender"], searchForm)
    useEffect(() => {
        if (typeof searchFormSender === "undefined") return;
        // const v = trim(searchFormSender)
        // updateSearchState(draft => {
        //     draft.sender = v
        // })
        // if (!v) {
        //     if (searchState.sender) {
                handleSearch()
            // }
        // }
    }, [searchFormSender])
    // const handleSearchSenderOnChange = e => {
    //     if (e.type === 'click') {
    //         if (searchFormSender) {
    //             if (!trim(e.target.value)) {
    //                 searchForm.setFieldValue(["sender"], '')
    //             }
    //         }
    //     }
    // }

    const searchFormSendCode = Form.useWatch(["send_code"], searchForm)
    useEffect(() => {
        if (typeof searchFormSendCode === "undefined") return;
        const v = trim(searchFormSendCode)
        updateSearchState(draft => {
            draft.send_code = v
        })
        if (!v) {
            if (searchState.send_code) {
                handleSearch()
            }
        }
    }, [searchFormSendCode])
    const handleSearchSendCodeOnChange = e => {
        if (e.type === 'click') {
            if (searchFormSendCode) {
                if (!trim(e.target.value)) {
                    searchForm.setFieldValue(["send_code"], '')
                }
            }
        }
    }

    const searchFormSendAt = Form.useWatch(["send_at"], searchForm)
    useEffect(() => {
        if (typeof searchFormSendAt === "undefined") return;
        handleSearch()
    }, [searchFormSendAt])

    const handleSearchFormStringPressEnter = e => {
        const val = trim(e.target.value)
        if (!val) return;
        handleSearch()
    }

    const searchFormArchived = Form.useWatch(["archived"], searchForm)
    useEffect(() => {
        if (typeof searchFormArchived === "undefined") return;
        handleSearch()
    }, [searchFormArchived])

    const handleSearchButtonClick = () => {
        // const val = trim(searchFormCompanyName)
        // if (!val) return;
        // handleSearch()
    }

    const handleReset = () => {
        stopSearchListen = true;
        searchForm.setFieldsValue({
            filename: "",
            // file_hash: "",
            sender: "",
            send_code: '',
            send_at: [],
            archived: false,
        })
        setTimeout(() => {
            stopSearchListen = false;
            handleSearch()
        }, 0)
    }

    const handleToggleExpand = (record) => {
        const idx = state.expandedRowKeys.indexOf(record.id)
        if (idx > -1) {
            updateState(draft => {
                draft.expandedRowKeys.splice(idx, 1)
            })
        } else {
            handleChildFileRowExpandedChange(record, true);
            updateState(draft => {
                draft.expandedRowKeys.push(record.id)
            })
        }
    }

    const handleToggleArchived = (record) => {
        if (record.archived_at) {
            modal.confirm({
                title: t('tip'),
                content: t('cancel file archive determination'),
                onOk() {
                    return new Promise((resolve, reject) => {
                        ToggleArchived({
                            package_id: record.id,
                            archived: false
                        }).then(() => {
                            // 延迟执行，后端ES更新数据需要1秒左右时间，避免请求太快
                            setTimeout(() => {
                                message.success(t('operate success')).then(() => {
                                })
                                resolve()
                                loadTableData()
                            }, 1500)
                        }).catch((e) => {
                            message.error(e.message || t('operate fail')).then(() => {
                            })
                            resolve()
                        })
                    })
                }
            })
        } else {
            modal.confirm({
                title: t('tip'),
                content: t('file archive determination'),
                onOk() {
                    return new Promise((resolve, reject) => {
                        ToggleArchived({
                            package_id: record.id,
                            archived: true
                        }).then(() => {
                            // 延迟执行，后端ES更新数据需要1秒左右时间，避免请求太快
                            setTimeout(() => {
                                message.success(t('operate success')).then(() => {
                                })
                                resolve()
                                loadTableData()
                            }, 1500)
                        }).catch((e) => {
                            message.error(e.message || t('operate fail')).then(() => {
                            })
                            resolve()
                        })
                    })
                }
            })
        }
    }

    const batchToggleArchivedLoadingKey = "batchArchivedLoading"

    const handleBatchToggleArchived = () => {
        if (searchFormArchived) {
            modal.confirm({
                title: t('tip'),
                content: t('cancel archive determination'),
                onOk() {
                    message.loading({
                        content: t('cancel archiving'),
                        key: batchToggleArchivedLoadingKey,
                        duration: 0
                    })
                    return new Promise(async (resolve) => {
                        try {
                            for (const recordId of state.rowSelection.selectedRowKeys) {
                                await ToggleArchived({
                                    package_id: recordId,
                                    archived: false
                                })
                            }
                            // 延迟执行，后端ES更新数据需要1秒左右时间，避免请求太快
                            setTimeout(() => {
                                message.open({
                                    key: batchToggleArchivedLoadingKey,
                                    type: 'success',
                                    content: t('already cancel archive'),
                                    duration: 2,
                                });
                                resolve()
                                loadTableData()
                            }, 1500)
                        } catch (e) {
                            setTimeout(() => {
                                message.open({
                                    key: batchToggleArchivedLoadingKey,
                                    type: 'error',
                                    content: e.message || t('operate fail'),
                                    duration: 2,
                                });
                                resolve()
                                loadTableData()
                            }, 1500)
                        }
                    })
                }
            })
        } else {
            modal.confirm({
                title: t('tip'),
                content: t('archive determination'),
                onOk() {
                    message.loading({
                        content: t('archiving'),
                        key: batchToggleArchivedLoadingKey,
                        duration: 0
                    })
                    return new Promise(async (resolve) => {
                        try {
                            for (const recordId of state.rowSelection.selectedRowKeys) {
                                await ToggleArchived({
                                    package_id: recordId,
                                    archived: true
                                })
                            }
                            // 延迟执行，后端ES更新数据需要1秒左右时间，避免请求太快
                            setTimeout(() => {
                                message.open({
                                    key: batchToggleArchivedLoadingKey,
                                    type: 'success',
                                    content: t('archived'),
                                    duration: 2,
                                });
                                resolve()
                                loadTableData()
                            }, 1500)
                        } catch (e) {
                            setTimeout(() => {
                                message.open({
                                    key: batchToggleArchivedLoadingKey,
                                    type: 'error',
                                    content: e.message || t('operate fail'),
                                    duration: 2,
                                });
                                resolve()
                                loadTableData()
                            }, 1500)
                        }
                    })
                }
            })
        }
    }

    const tableColumns = useCreateTableColumns({
        onToggleExpand: handleToggleExpand,
        onToggleArchived: handleToggleArchived,
        isArchived: searchFormArchived
    });

    const loadTableData = async () => {
        try {
            const {data} = await fetcher.fetch()
            if (!data.data || data.data.length === 0) {
                let page = get(fetcher.params, 'pagination.' + PAGINATION.currentPageKey, 1)
                if (page > 1) {
                    page -= 1
                    const pageSize = get(fetcher.params, 'pagination.' + PAGINATION.pageSizeKey, 10)
                    fetcher.setParams({
                        ...fetcher.params,
                        pagination: {
                            [PAGINATION.currentPageKey]: page,
                            [PAGINATION.pageSizeKey]: pageSize,
                        }
                    })
                    await loadTableData()
                    return
                }
            }
            // 每次获取数据时，更新发送方列表
            const senderListResponse = await GetSenderListAll()
            const senderList = get(senderListResponse, "data.data", [])
            updateState(draft => {
                draft.expandedRowKeys = [];
                draft.rowSelection.selectedRowKeys = [];
                draft.childFiles = {};
                draft.search.sender.options = [
                    {sender_name: t('all'), sender_id: ''},
                    ...senderList
                ]
            })
        } catch (e) {
            const code = get(e, 'code')
            const msg = get(e, 'message')
            if (code && code !== 200) {
                message.warning(msg || t('system error'))
            }
            return
        }
        updateState(draft => {
            draft.initialized = true
        })

        // fetcher.fetch().then(({data}) => {
        //     if (!data.data || data.data.length === 0) {
        //         let page = get(fetcher.params, 'pagination.' + PAGINATION.currentPageKey, 1)
        //         if (page > 1) {
        //             page -= 1
        //             const pageSize = get(fetcher.params, 'pagination.' + PAGINATION.pageSizeKey, 10)
        //             fetcher.setParams({
        //                 ...fetcher.params,
        //                 pagination: {
        //                     [PAGINATION.currentPageKey]: page,
        //                     [PAGINATION.pageSizeKey]: pageSize,
        //                 }
        //             })
        //             loadTableData()
        //             return
        //         }
        //     }
        //     updateState(draft => {
        //         draft.expandedRowKeys = [];
        //         draft.rowSelection.selectedRowKeys = [];
        //         draft.childFiles = {};
        //     })
        //     // setTimeout(()=>{
        //     //     rootContext.scrollTo({top: 0})
        //     // },0)
        // }).catch((e) => {
        //     const code = get(e, 'code')
        //     const msg = get(e, 'message')
        //     if (code && code !== 200) {
        //         message.warning(msg || '系统错误')
        //     }
        // }).finally(() => {
        //     updateState(draft => {
        //         draft.initialized = true
        //     })
        // })
    }

    const handleTableChange = (pagination, filters, sorter, extra) => {
        HandleTableChange(fetcher, {pagination, filters, sorter, extra, loadFunc: loadTableData})
    }

    const initialize = () => {
        fetcher.set(GetFileList, {
            pagination: {
                [PAGINATION.currentPageKey]: PAGINATION.defaultCurrentPage,
                [PAGINATION.pageSizeKey]: PAGINATION.defaultPageSize,
            }
        })
        loadTableData()
    }

    useEffect(() => {
        initialize()
    }, [])

    // const rebuildRefreshPin = ()=>{
    //     if (layoutRef.current!=null){
    //         const layoutRect = layoutRef.current.getBoundingClientRect()
    //         document.getElementById('refreshPin').style.left = layoutRect.right + 'px'
    //     }
    // }

    useEffect(() => {
        const clipboard = new ClipboardJS('.copy-handler', {
            text: function (trigger) {
                return trigger.getAttribute('data-value');
            },
        });
        clipboard.on('success', function (e) {
            message.success(t('copy success'))
            e.clearSelection();
        });

        clipboard.on('error', function (e) {
            message.error(t('copy fail'))
        });

        // rebuildRefreshPin()
        // window.addEventListener('load', rebuildRefreshPin)
        // window.addEventListener('resize',rebuildRefreshPin)
        return () => {
            clipboard.destroy()
            // window.removeEventListener('load', rebuildRefreshPin)
            // window.removeEventListener('resize',rebuildRefreshPin)
        }
    }, [])

    const downloadLoadingKey = "downloadLoading"

    const refreshDownloadRecordStatus = (packageId, fileId) => {
        GetChildFileLatestDownloadRecord({
            package_id: packageId,
            file_id: fileId
        }).then(({data}) => {
            const childFileIdx = state.childFiles[packageId].findIndex(item => item.id === fileId)
            if (childFileIdx > -1) {
                updateState(draft => {
                    draft.childFiles[packageId][childFileIdx].latest_download_result = data.download_result
                    draft.childFiles[packageId][childFileIdx].latest_download_at = data.download_at
                    draft.childFiles[packageId][childFileIdx].latest_download_ip = data.download_ip
                    draft.childFiles[packageId][childFileIdx].latest_download_consistency_compare_result = data.download_consistency_compare_result
                    draft.childFiles[packageId][childFileIdx].compare_success = data.download_result===1
                })
            }
        }).catch(() => {
        })
    }

    const doHandleDownloadZipFile = ({recordId, fileId, password, wm_enabled = false}) => {
        message.loading({
            content: t('downloading'),
            key: downloadLoadingKey,
            duration: 0
        })
        return new Promise((resolve, reject) => {
            DownloadChildFile({
                package_id: recordId,
                file_id: fileId,
                password: password,
                wm_enabled: wm_enabled,
            }).then((res) => {
                setTimeout(() => {
                    // 加个延时，这个延时毫无意义，避免因下载过快导致点击后立刻被下载，没有下载效果
                    if (res.data.type === 'application/json') {
                        res.data.text().then((v) => {
                            const vv = JSON.parse(v)
                            message.open({
                                key: downloadLoadingKey,
                                type: 'error',
                                content: t(vv.message || 'download fail'),
                                duration: 2,
                            });
                        }).catch((e) => {
                            message.open({
                                key: downloadLoadingKey,
                                type: 'error',
                                content: t('download fail'),
                                duration: 2,
                            });
                        }).finally(() => {
                            refreshDownloadRecordStatus(recordId, fileId)
                            reject()
                        })
                    } else {

                        const disposition = res.headers["content-disposition"] || "";
                        let fileName = ""; // 默认名

                        // 先尝试 filename*= 形式（RFC 5987）
                        const matchesEncoded = disposition.match(/filename\*\s*=\s*[^']*''([^;\r\n]*)/i);
                        if (matchesEncoded) {
                            fileName = decodeURIComponent(matchesEncoded[1]);
                        }
                        if (!fileName){
                            // 再尝试普通的 filename=
                            const matchesAscii = disposition.match(/filename\s*=\s*["']?([^;"'\r\n]*)["']?/i);
                            if (matchesAscii) {
                                fileName = matchesAscii[1];
                            }
                        }
                        if (!fileName){
                            fileName = res.headers["content-disposition"].match(/filename\*?=['"]?(?:UTF-\d['"]*)?([^;\r\n"']*)['"]?;?/)[1]
                        }

                        const blob = new Blob([res.data]);
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = fileName;
                        a.click();
                        window.URL.revokeObjectURL(url);

                        setTimeout(() => {
                            message.open({
                                key: downloadLoadingKey,
                                type: 'success',
                                content: t('already download'),
                                duration: 2,
                            });
                        }, 800)
                        refreshDownloadRecordStatus(recordId, fileId)
                        resolve()
                    }
                }, 500)
            }).catch((e) => {
                message.open({
                    key: downloadLoadingKey,
                    type: 'error',
                    content: t(e.message || 'download fail'),
                    duration: 2,
                });
                refreshDownloadRecordStatus(recordId, fileId)
                reject(e)
            }).finally(() => {
            })
        })
    }

    const handleDownloadZipFile = async (record, file) => {
        // if (!file.is_encrypted) {
        //     context.GlobalLoading(true)
        //     doHandleDownloadZipFile(record.id, file.id).then(() => {
        //
        //     }).catch(() => {
        //     }).finally(() => {
        //         context.GlobalLoading(false)
        //     })
        //     return
        // }

        updateState(draft => {
            draft.inputFilePwd.loading = true
        })
        try {
            const {data} = await GetPreviewChildFileWatermarkInfo({
                package_id: record.id,
                file_id: file.id
            })
            updateState(draft => {
                draft.inputFilePwd.recordId = record.id;
                draft.inputFilePwd.fileId = file.id;
                draft.inputFilePwd.visible = true;
                draft.inputFilePwd.loading = false;
                draft.inputFilePwd.needPassword = !!file.is_encrypted;
                draft.inputFilePwd.sysConfig = data;
            })
        }catch (e) {
            message.error(t(get(e,'message') || 'operate fail'))
            updateState(draft => {
                draft.inputFilePwd.loading = false
            })
        }
    }

    const handleInputFilePwdOk = (result) => {
        updateState(draft => {
            draft.inputFilePwd.loading = true;
        })
        doHandleDownloadZipFile({
            recordId:result.recordId, fileId:result.fileId, password:result.password,
            wm_enabled: !!result.wm_enabled
        }).then(() => {
            updateState(draft => {
                draft.inputFilePwd.visible = false;
                draft.inputFilePwd.loading = false;
            })
        }).catch(() => {
            updateState(draft => {
                draft.inputFilePwd.loading = false;
            })
        })
    }

    const handleInputFilePwdCancel = () => {
        updateState(draft => {
            draft.inputFilePwd.visible = false;
        })
    }

    const handleChildFileRowExpandedChange = (record, expanded) => {
        if (!expanded) return;
        if (isArray(state.childFiles[record.id])) return;
        context.GlobalLoading(true)
        GetChildFiles({
            package_id: record.id
        }).then(({data}) => {
            updateState(draft => {
                draft.childFiles[record.id] = data;
            })
        }).catch(e => {
            message.error(get(e, 'message'))
        }).finally(() => {
            context.GlobalLoading(false)
        })
    }

    const handleChildFileExpandAll = async () => {
        context.GlobalLoading(true, t('expanding all files'))
        try {
            const values = {}
            const keys = []
            for (const record of fetcher.rows) {
                keys.push(record.id)
                if (state.childFiles[record.id]) {
                    continue
                }
                const {data} = await GetChildFiles({
                    package_id: record.id
                })
                values[record.id] = data;
            }
            updateState(draft => {
                Object.keys(values).forEach(k => {
                    draft.childFiles[k] = values[k];
                })
                draft.expandedRowKeys = keys
            })
        } catch (e) {
            message.error(e.message || t('operate fail'))
        }
        context.GlobalLoading(false)
    }

    const handleChildFileCollapseAll = () => {
        updateState(draft => {
            draft.expandedRowKeys = []
        })
    }

    const handleRowSelectionChange = (selectedRowKeys, selectedRows, info) => {
        updateState(draft => {
            draft.rowSelection.selectedRowKeys = selectedRowKeys;
        })
    }

    return (
        <div className={styles.wrapper}>
            <div className={"wrapper-inner " + (!fetcher.total ? " no-data" : "")}>
                <div className={'h-full'}>
                    <PageLayout header={t('southbound data cross-border reception')} ref={layoutRef} search={<>
                        <Form form={searchForm}  labelCol={{
                            style:{
                                width: '110px'
                            }
                        }} labelAlign={"left"} initialValues={{
                            archived: false,
                            sender: ''
                        }}>
                            <Row gutter={18}>
                                {/*<Col sm={7} xxl={4}>*/}
                                {/*    <Form.Item name={"status"} label={"状态"} style={{}}>*/}
                                {/*        <Select options={[{value: 0, label: "全部"}].concat(CertStatuses)}/>*/}
                                {/*<Input variant={'filled'}/>*/}
                                {/*</Form.Item>*/}
                                {/*</Col>*/}
                                <Col sm={8} xxl={8}>
                                    <Form.Item labelCol={{
                                        style:{
                                            width: '110px'
                                        }
                                    }} name={"filename"} label={t('file/batch name')}>
                                        <Input placeholder={t('please enter the file/batch Name')} variant={'filled'} allowClear={true}
                                               onPressEnter={handleSearchFormStringPressEnter}
                                               onChange={handleSearchFilenameOnChange}/>
                                    </Form.Item>
                                </Col>
                                {/*<Col sm={8} xxl={6}>*/}
                                {/*    <Form.Item name={"file_hash"} label={"文件哈希"}>*/}
                                {/*        <Input placeholder={"请输入文件哈希"} variant={'filled'} allowClear={true}*/}
                                {/*               onPressEnter={handleSearchFormStringPressEnter}*/}
                                {/*               onChange={handleSearchFileHashOnChange}/>*/}
                                {/*    </Form.Item>*/}
                                {/*</Col>*/}
                                <Col sm={8} xxl={8}>
                                    <Form.Item name={"send_code"} label={t('send batch')}>
                                        <Input placeholder={t('please enter the sending batch')} variant={'filled'} allowClear={true}
                                               onPressEnter={handleSearchFormStringPressEnter}
                                               onChange={handleSearchSendCodeOnChange}/>
                                    </Form.Item>
                                </Col>
                                <Col sm={8} xxl={8}>
                                    <Form.Item name={"send_at"} label={t('send time')}>
                                        <DatePicker.RangePicker variant={'filled'} style={{
                                            width: "100%"
                                        }} presets={[
                                            {
                                                label: t('today'),
                                                value: [dayjs().startOf('d'), dayjs().startOf('d')],
                                            },
                                            {
                                                label: t('yesterday'),
                                                value: [dayjs().add(-1, 'd').startOf('d'), dayjs().add(-1, 'd').startOf('d')],
                                            },
                                            {
                                                label: t('last 7 days'),
                                                value: [dayjs().add(-6, 'd').startOf('d'), dayjs().startOf('d')],
                                            },
                                            {
                                                label: t('last 15 days'),
                                                value: [dayjs().add(-14, 'd').startOf('d'), dayjs().startOf('d')],
                                            },
                                        ]} disabledDate={(current) => {
                                            return current && (
                                                current > dayjs().startOf('day')
                                            )
                                        }}/>
                                    </Form.Item>
                                </Col>
                                <Col sm={8} xxl={8}>
                                    <Form.Item name={"sender"} label={t('sender')}>
                                        {/*<Input placeholder={"请输入发送方"} variant={'filled'} allowClear={true}*/}
                                        {/*       onPressEnter={handleSearchFormStringPressEnter}*/}
                                        {/*       onChange={handleSearchSenderOnChange}/>*/}
                                        <Select allowClear={false} showSearch={true} placeholder={t('place select')}
                                                options={state.search.sender.options}
                                                fieldNames={{
                                                    label: 'sender_name',
                                                    value: 'sender_id'
                                                }}
                                                filterOption={(input, option) =>
                                                    (option?.sender_name ?? '').toLowerCase().includes(input.toLowerCase()) ||
                                                    (option?.sender_name_py ?? '').toLowerCase().includes(input.toLowerCase())
                                                }/>
                                    </Form.Item>
                                </Col>
                                <Col sm={8} xxl={8}>
                                    <Form.Item wrapperCol={{
                                        span: 12,
                                    }} name={'archived'} label={t('status')}>
                                        <Select options={[
                                            {label: t('normal'), value: false},
                                            {label: t('archived'), value: true}
                                        ]}/>
                                    </Form.Item>
                                </Col>
                            </Row>
                        </Form>
                        <Divider type={"vertical"}/>
                        <div className={"actions"}>
                            <Button loading={fetcher.loading} type={"primary"} icon={<SearchOutlined/>}
                                    onClick={handleSearch}>{t('query')}</Button>
                            <Button loading={fetcher.loading} icon={<ReloadOutlined/>} onClick={handleReset}
                                    variant={'filled'} color={'default'}>{t('reset')}</Button>
                        </div>
                    </>} action={
                        <Flex gap="small" wrap>
                            <Button
                                disabled={!fetcher.rows.length || fetcher.rows.length === state.expandedRowKeys.length}
                                color="primary" variant="link" onClick={handleChildFileExpandAll}>{t('expand all')}</Button>
                            <Button disabled={!fetcher.rows.length || !state.expandedRowKeys.length} color="primary"
                                    variant="link" onClick={handleChildFileCollapseAll}>{t('collapse all')}</Button>
                            <Button disabled={!state.rowSelection.selectedRowKeys.length} color="primary" variant="link"
                                    onClick={handleBatchToggleArchived}>{searchFormArchived ? t('batch cancel archive') : t('batch archive')}</Button>
                        </Flex>
                    } actionExtra={
                        <>
                            <Button loading={fetcher.loading} title={t('refresh')} icon={<SyncOutlined/>}
                                    onClick={loadTableData} variant={'outlined'} color={'primary'}>{t('refresh')}</Button>
                        </>
                    }>
                        {/*<div className={'refresh-pin'} id={'refreshPin'}>*/}
                        {/*    <Button loading={fetcher.loading} color={'primary'} variant={'solid'} icon={<SyncOutlined />} onClick={loadTableData}/>*/}
                        {/*</div>*/}
                        <Table rowKey={"id"}
                               bordered={false}
                               columns={tableColumns}
                               dataSource={fetcher.rows}
                               onChange={handleTableChange}
                               loading={{
                                   spinning: fetcher.loading,
                                   indicator: (<LoadingOutlined
                                       style={{
                                           fontSize: 36,
                                       }}
                                       spin
                                   />),
                                   tip: t('loading')
                               }}
                               className={fetcher.total ? "" : " no-data"}
                               locale={{
                                   emptyText: (
                                       <div className={"empty"}>
                                           <div className={"empty-inner"}>
                                               <div className={"empty-action"}>
                                                   {t('no content')}
                                               </div>
                                           </div>
                                       </div>
                                   )
                               }}
                               sticky={{offsetHeader: TABLE_STICKY_OFFSET_HEADER}}
                               scroll={{x: 'max-content'}}
                               pagination={{
                                   ...PAGINATION,
                                   total: fetcher.total,
                                   current: get(fetcher.params, 'pagination.' + PAGINATION.currentPageKey, 1),
                                   pageSize: get(fetcher.params, 'pagination.' + PAGINATION.pageSizeKey, 15),
                                   hideOnSinglePage: true,
                                   showQuickJumper: false,
                               }}
                               expandable={{
                                   expandedRowClassName: 'ant-table-expanded-wrapper',
                                   rowExpandable: record => record.zip_file_count > 0,
                                   expandedRowRender: (record) => (
                                       <div className={'child-table'}>
                                           <div>
                                               <div
                                                   className={'child-file-items grid grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-4 mx-12'}>
                                                   {(state.childFiles[record.id] || []).map((v) => (
                                                       <div key={v.id}
                                                            className={'child-file-item relative  bg-white border border-stone-100 shadow-sm p-4 text-black transition hover:shadow'}>
                                                           {typeof v.compare_success !== "undefined" ? (
                                                               <div
                                                                   className={'absolute compare-result'
                                                                       + (v.compare_success ? ' compare-result-success' : ' compare-result-error')}>
                                                                   <div className={'relative inner'}>
                                                                       <div className={'result-text'}>
                                                                           {v.compare_success ? t('download success') : t('download fail')}
                                                                       </div>
                                                                   </div>
                                                               </div>
                                                           ) : null}
                                                           <div
                                                               className={'child-file-item-inner h-full flex flex-col justify-between'}>
                                                               <div className={'child-file-head'}>
                                                                   <div
                                                                       className={'child-file-title font-medium text-[14px]'}>
                                                                       {v.is_encrypted ? (<LockOutlined
                                                                           className={'text-green-600 text-[14px] mr-1'}
                                                                           title={t('encrypted')}/>) : null}
                                                                       <span>{v.filename}</span>
                                                                   </div>
                                                               </div>
                                                               <div className={'child-file-body mt-3'}>
                                                                   <div className={'child-file-body-inner'}>
                                                                       <div className={'field flex'}>
                                                                           <div
                                                                               className={'field-label text-stone-400'}>{t('consistency comparison results')}：
                                                                           </div>
                                                                           <div
                                                                               className={'field-value text-stone-800'}>
                                                                               {v.latest_download_consistency_compare_result === 1 ? (
                                                                                   <Tag bordered={true}
                                                                                        color={'success'}>{t('consistent')}</Tag>) : null}
                                                                               {v.latest_download_consistency_compare_result === -1 ? (
                                                                                   <Tag bordered={true}
                                                                                        color={'error'}>{t('inconsistent')}</Tag>) : null}
                                                                               {v.latest_download_consistency_compare_result === 0 ? ('-') : null}
                                                                           </div>
                                                                       </div>
                                                                       <div className={'field flex'}>
                                                                           <div
                                                                               className={'field-label text-stone-400'}>{t('download results')}：
                                                                           </div>
                                                                           <div
                                                                               className={'field-value text-stone-800'}>
                                                                               {v.latest_download_result === 1 ? (
                                                                                   <Tag bordered={true}
                                                                                        color={'success'}>{t('success')}</Tag>) : null}
                                                                               {v.latest_download_result === -1 ? (
                                                                                   <Tag bordered={true}
                                                                                        color={'error'}>{t('fail')}</Tag>) : null}
                                                                               {v.latest_download_result === 0 ? ('-') : null}
                                                                           </div>
                                                                       </div>
                                                                       <div className={'field flex'}>
                                                                           <div
                                                                               className={'field-label text-stone-400'}>{t('download time')}：
                                                                           </div>
                                                                           <div
                                                                               className={'field-value text-stone-800'}>{v.latest_download_at || '-'}</div>
                                                                       </div>
                                                                   </div>
                                                               </div>
                                                               <div className={'child-file-footer mt-3'}>
                                                                   <div
                                                                       className={'child-file-footer-inner flex justify-between items-center'}>
                                                                       <span>{FormatFileSize(v.filesize)}</span>
                                                                       {v.download_disabled ? null : (
                                                                           <Button color={'primary'}
                                                                                   variant={'filled'}
                                                                                   onClick={() => handleDownloadZipFile(record, v)}>{t('download')}</Button>
                                                                       )}
                                                                   </div>
                                                               </div>
                                                           </div>
                                                       </div>
                                                   ))}
                                               </div>
                                           </div>
                                       </div>
                                   ),
                                   onExpand: (expanded, record) => {
                                       handleChildFileRowExpandedChange(record, expanded)
                                   },
                                   expandedRowKeys: state.expandedRowKeys,
                                   onExpandedRowsChange: (expandedRows) => {
                                       updateState(draft => {
                                           draft.expandedRowKeys = expandedRows;
                                       })
                                   },
                                   showExpandColumn: false
                               }}
                               rowSelection={{
                                   selectedRowKeys: state.rowSelection.selectedRowKeys,
                                   onChange: handleRowSelectionChange,
                                   columnWidth: "52px",
                               }}
                        />
                    </PageLayout>
                </div>
            </div>
            <InputFilePwd visible={state.inputFilePwd.visible}
                          recordId={state.inputFilePwd.recordId}
                          fileId={state.inputFilePwd.fileId}
                          loading={state.inputFilePwd.loading}
                          needPassword={state.inputFilePwd.needPassword}
                          sysConfig={state.inputFilePwd.sysConfig}
                          onCancel={handleInputFilePwdCancel}
                          onOk={handleInputFilePwdOk}
            />
        </div>
    )
}