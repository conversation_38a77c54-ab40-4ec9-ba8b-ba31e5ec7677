import {attempt} from "lodash";
import {App, Divider, Tag} from "antd";
import {CopyOutlined} from '@ant-design/icons'
import {FormatFileSize} from "@/utils/size";
import {useTranslation} from "react-i18next";

export function useCreateTableColumns(
    {
        onToggleExpand,
        onToggleArchived,
        isArchived = false
    }
) {
    const {message} = App.useApp();
    const { t, i18n } = useTranslation();

    const handleToggleExpand = (record)=>{
        if (isArchived) {
            message.warning(t('expand disabled on archived'))
            return;
        }
        attempt(onToggleExpand, record)
    }

    const columns = [
        {
            title: t('batch number'),
            key: 'send_code',
            width: 200,
            fixed: 'left',
            render: record => {
                return (
                    <div className={'send-code'}>
                        {record.send_code}
                        <span title={t('copy')}
                              className={"copy-handler"}
                              data-value={record.send_code}><CopyOutlined/>
                        </span>
                    </div>
                )
            }
        },
        {
            title: t('batch name'),
            key: "filename",
            width: 220,
            render: record => {
                return (
                    <div className={'file-name'}>
                        <span onClick={() => attempt(handleToggleExpand, record)}>{record.filename}</span>
                    </div>
                )
            }
        },
        {
            title: t('file number'),
            key: "zip_file_count",
            width: 120,
            align: 'center',
            render: record => {
                return (<Tag bordered={false} onClick={() => attempt(handleToggleExpand, record)} className={'zip-file-count'}
                             color={'processing'}>{record.zip_file_count}</Tag>)
            }
        },
        {
            title: t('file size'),
            key: 'filesize',
            width: 120,
            sorter: true,
            render: record => {
                return (
                    <div className={'file-size'}>
                        {FormatFileSize(record.filesize)}
                    </div>
                )
            }
        },
        // {
        //     title: '文件哈希',
        //     key: "zip_file_hash",
        //     width: 100,
        //     ellipsis: true,
        //     render: record => {
        //         return (
        //             <div className={'file-hash'}>
        //                 {record.zip_file_hash}
        //                 <span title={"复制"}
        //                       className={"copy-handler"}
        //                       data-value={record.zip_file_hash}><CopyOutlined/>
        //                 </span>
        //             </div>
        //         )
        //     }
        // },
        {
            title: t('sender'),
            key: "sender_id",
            width: 180,
            ellipsis: true,
            render: record => {
                return (
                    <div className={'sender-id'}>{record.sender_name}</div>
                )
            }
        },
        {
            title: t('send time'),
            key: "send_at",
            width: 160,
            sorter: true,
            render: record => {
                return (
                    <div className={'timestamp'}>
                        {record.send_at}
                    </div>
                )
            }
        },
    ]
    if (isArchived){
        columns.push({
            title: t('archive time'),
            key: "archived_at",
            width: 160,
            sorter: true,
            render: record => {
                return (
                    <div className={'timestamp'}>
                        {record.archived_at}
                    </div>
                )
            }
        })
    }
    columns.push({
        title: t('action'),
        width: 120,
        fixed: "right",
        className: "actions",
        align: "center",
        render: record => {
            if (record.download_disabled){
                return (
                    <>
                        <a onClick={() => attempt(onToggleExpand, record)}>{t('view')}</a>
                    </>
                )
            }
            return (
                <>
                    {!isArchived? (
                        <>
                            <a onClick={() => attempt(onToggleExpand, record)}>{t('view')}</a>
                            <Divider type={"vertical"}/>
                        </>
                    ) : null}
                    <a onClick={() => attempt(onToggleArchived, record)}>{record.archived_at ? t('cancel archive') : t('archive')}</a>
                </>
            )
        }
    })
    return columns
}