.wrapper:global{
  >.ant-modal-wrap{
    >.ant-modal{
    }
    .ant-modal-content{
      .ant-modal-body{
        .ant-form{
          margin-top: 24px;
          margin-bottom: 24px;
        }
        .watermark-info{
          margin-top: 12px;
          text-align: left;
          >.ant-alert{
            >.ant-alert-content{
              >.ant-alert-message{}
              >.ant-alert-description{
                .watermark-desc{
                  font-size: 12px;
                  p{
                    margin-bottom: 8px;
                    &.watermark-global-setting{
                      color: #606266;
                    }
                  }
                  .watermark-name{
                    font-weight: bold;
                    color: #3366ff
                  }
                }
              }
            }
          }
          .watermark-data{
            border-top: 1px solid #adcaff;
            padding-top: 8px;
            font-size: 12px;
            >ul{
              display: grid;
              grid-template-columns: repeat(2, 1fr);
              gap: 8px; /* 根据需要调整间距 */
              margin-bottom: 0;
              >li{
                >.watermark-item{
                  >.watermark-item-inner{
                    display: flex;
                    >.watermark-item-label{
                      width: 80px;
                      color: #606266;
                    }
                    >.watermark-item-value{}
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}