import styles from './index.module.scss'
import Dialog from "@/components/Dialog";
import {Alert, Checkbox, Form, Input} from "antd";
import {useEffect} from "react";
import {useImmer} from "use-immer";
import {SafetyOutlined} from "@ant-design/icons";
import {useRouter} from "@/utils/app/transfer";
import {get} from "lodash";
import {useTranslation} from "react-i18next";

export default function InputFilePwd(
    {
        onOk,
        onCancel,
        visible = false,
        loading = false,
        recordId,
        fileId,
        needPassword = false,
        sysConfig = {}
    }) {
    const { t, i18n } = useTranslation();

    const router = useRouter()
    const [form] = Form.useForm();
    useEffect(() => {
        if (visible){
            form.resetFields()
            form.setFieldsValue({
                wm_enabled: !!get(sysConfig,'rev_dl_wm_enabled')
            })
        }
    }, [visible]);

    const onFinish = (values) => {
        onOk({
            ...values,
            recordId,
            fileId
        })
    }

    const wmEnabled = Form.useWatch('wm_enabled', form)

    const [state,updateState] = useImmer({
        wm:{
            enabled: false,
            data: {}
        }
    })

    return (
        <Dialog width={580} loading={loading} title={t('download file')} rootClassName={styles.wrapper} visible={visible} onOk={form.submit}
                onCancel={onCancel}>
            <Form form={form} onFinish={onFinish}>
                {needPassword ? (
                    <Form.Item name={'password'} rules={[
                        {
                            required: true,
                            message: t('please enter the sms authorization code')
                        }
                    ]}>
                        <Input style={{
                            fontSize: '14px'
                        }} variant={'filled'} size={'large'} placeholder={t('file protected by password please enter the password')}/>
                    </Form.Item>
                ): null}
                <Form.Item name={'wm_enabled'} valuePropName={'checked'}>
                    <Checkbox>{t('open watermark')}</Checkbox>
                </Form.Item>
            </Form>
            <div className={'watermark-info'}>
                {wmEnabled ? (
                    <Alert
                        banner={false}
                        description={
                            <div>
                                <div className={'watermark-desc'}>
                                    <p>{t('implicit watermark prefix')} <span
                                        className={'watermark-name'}><SafetyOutlined/>{t('implicit watermark')}</span> {t('implicit watermark suffix')}。
                                    </p>
                                    <p className={'watermark-global-setting'}>{t('watermark switched in watermark global settings')}，<a
                                        onClick={()=>router.push('/config/global')}>{t('click to go')}</a></p>
                                </div>
                                <div className={'watermark-data'}>
                                    <ul>
                                        <li>
                                            <div className={'watermark-item'}>
                                                <div className={'watermark-item-inner'}>
                                                    <div className={'watermark-item-label'}>{t('belongs agency')}：
                                                    </div>
                                                    <div className={'watermark-item-value'}>{get(sysConfig,'institution')}
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                        <li>
                                            <div className={'watermark-item'}>
                                                <div className={'watermark-item-inner'}>
                                                    <div className={'watermark-item-label'}>{t('operator')}：</div>
                                                    <div className={'watermark-item-value'}>{get(sysConfig,'operator')}</div>
                                                </div>
                                            </div>
                                        </li>
                                        <li>
                                            <div className={'watermark-item'}>
                                                <div className={'watermark-item-inner'}>
                                                    <div className={'watermark-item-label'}>{t('patient id')}：</div>
                                                    <div className={'watermark-item-value'}>{get(sysConfig,'person_id','-')}</div>
                                                </div>
                                            </div>
                                        </li>
                                        <li>
                                            <div className={'watermark-item'}>
                                                <div className={'watermark-item-inner'}>
                                                    <div className={'watermark-item-label'}>{t('watermark time')}：
                                                    </div>
                                                    <div className={'watermark-item-value'}>{t('base system time generation')}
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        }
                        type="info"
                        showIcon={false}
                    />
                ) : null}
            </div>
        </Dialog>)
}