.wrapper:global {
  height: 100%;
  overflow: hidden;

  >.wrapper-inner {
    height: 100%;
    overflow: hidden;

    .page-layout {
      background: none;
      //margin: 0 -12px;

      > .page-container {
        background: none;
        padding: 12px 12px;
        //@apply container mx-auto;

        > .container-inner {
          box-shadow: 0 1px 2px 0 rgba(54, 58, 80, .22);
          //box-shadow: 0 0 20px 0 rgba(55, 99, 170, 0.1), 0 0px 20px 0 #fff;
          //max-width: 1400px;
          //margin: 0 24px;
          display: flex;
          flex-direction: column;

          > .container-top {
            border-bottom: 12px solid #eef0f4;

            > .ant-alert {
              border: none;
              background-color: #ffffff;

              > .anticon {
                font-size: 16px;
                margin-top: 4px;
              }

              > .ant-alert-content {
                > .ant-alert-message {
                  font-size: 14px;
                  color: #5e6e9c;
                }

                > .ant-alert-description {
                  > ol {
                    margin-left: 14px;
                    margin-bottom: 0;

                    > li {
                      font-size: 12px;
                      color: #5e6e9c;
                      line-height: 24px;
                    }
                  }
                }
              }
            }
          }

          > .container-header {
            background: none;
            //box-shadow: 0 2px 4px 0 rgba(54, 58, 80, .22);

            > .container-search {
              margin-bottom: 18px;
              > .ant-form {
                margin-bottom: -12px;

                .ant-form-item {
                  margin-bottom: 12px;

                  .ant-form-item-label {
                    > label {
                      width: 65px;
                    }
                  }

                  .ant-picker-range {
                    width: 100%;
                  }
                }
              }

              > .actions {
                flex-direction: column;

                > .ant-btn {
                  & + .ant-btn {
                    margin-top: 12px;
                  }
                }
              }
            }

            > .container-action {
              > .container-action__main {
                > .inner {
                  > .ant-btn {
                    font-size: 13px;
                  }
                }
              }

              > .container-action__extra {
                > .extra {
                  display: flex;
                }
              }
            }
          }

          > .container-body {
            flex: 1;
            padding-left: 0;
            padding-right: 0;
            //background: #ffffff;
            //box-shadow: 0 3px 4px 0 rgba(54, 58, 80, .22);
            position: relative;
            >.refresh-pin{
              position: fixed;
              top: 258px;
              z-index: 9;
            }


            .ant-table-wrapper {
              .ant-table {
                &.ant-table-empty {
                  .ant-table-body {
                    overflow: hidden !important;
                  }
                }
                .ant-table-sticky-scroll{
                  display: none;
                }
                .ant-table-body {
                  .ant-table-placeholder {
                    .ant-table-cell {
                      border: none;
                    }
                  }

                  .empty {
                    > .empty-inner {
                      //border: 1px solid #e6e6e6;
                      display: flex;
                      flex-direction: column;
                      justify-content: center;
                      align-items: center;
                      height: 480px;

                      > .empty-icon {
                        margin-bottom: 12px;

                        > img {
                          width: 60px;
                        }
                      }

                      > .empty-text {
                        color: #888888;
                        text-align: center;
                        width: 520px;
                        margin-bottom: 16px;
                      }
                    }
                  }

                  .copy-handler{
                    margin-left: 10px;
                    color: #cccccc;
                    cursor: pointer;
                    &:hover{
                      color: #4f46e5;
                    }
                  }
                  .file-hash{
                    width: 238px;
                    white-space: normal;
                  }
                  .sender-id{
                    max-width: 228px;
                    white-space: normal;
                  }
                  .file-name{
                    >span{
                      cursor: pointer;
                      color: #3366ff;
                      font-weight: 500;
                      &:hover{
                        color: #4f46e5;
                      }
                    }
                  }
                  .zip-file-count{
                    cursor: pointer;
                  }
                  .ant-table-expanded-wrapper{
                    >.ant-table-cell{
                      background: #f5f5f5 !important;
                    }
                    .child-file-items{
                      .child-file-item{
                        .child-file-item-inner{
                          >.child-file-head{
                            &-inner{}
                          }
                          >.child-file-body{
                            >.child-file-body-inner{
                              >.field{
                                font-size: 12px;
                                >.field-label{
                                  width: 100px;
                                }
                                >.field-value{
                                  flex: 1;
                                  white-space: break-spaces;
                                  overflow: hidden;
                                }
                                &+.field{
                                  margin-top: 6px;
                                }
                              }
                            }
                          }
                          >.child-file-footer{
                            &-inner{}
                          }
                        }
                      }
                    }
                  }

                  .ant-table-tbody{
                    >.ant-table-row{
                      >.ant-table-cell{}
                    }
                  }
                }
                .compare-result{
                  top: 0;
                  right: 0;
                  display: none;
                  >.inner{
                    position: relative;
                    >.result-text{
                      color: #ffffff;
                      font-size: 11px;
                      position: relative;
                      z-index: 1;
                      padding: 2px 6px;
                    }
                    &::after{
                      content: '';
                      position: absolute;
                      top: 0;
                      right: 0;
                      z-index: 0;
                      width: 0;
                      height: 0;
                      border-left: 10px solid transparent;
                      border-right: 56px solid transparent;
                      border-top: 24px solid transparent;
                    }
                  }
                  &.compare-result-success{
                    display: block;
                    >.inner{
                      &:after{
                        border-right-color: #52c41a;
                        border-top-color: #52c41a;
                      }
                    }
                  }
                  &.compare-result-error{
                    display: block;
                    >.inner{
                      &:after{
                        border-right-color: #f44336;
                        border-top-color: #f44336;
                        //border-right-width: 64px;
                      }
                    }
                  }
                }
              }
              .ant-pagination{
                display: flex;
                justify-content: center;
              }
            }
          }
        }
      }
    }

    //&.no-data{
    //  >.page-layout{
    //    >.page-container{
    //      >.container-inner{
    //        >.container-header{
    //          display: none;
    //        }
    //      }
    //    }
    //  }
    //}
  }
}