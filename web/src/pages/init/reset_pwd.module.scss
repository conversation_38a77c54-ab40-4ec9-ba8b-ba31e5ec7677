.wrapper:global{
  height: 100%;
  overflow: hidden;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: 50% center;
  >.wrapper-inner{
    height: 100%;
    overflow: hidden auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    >.component-locale{
      position: fixed;
      top: 24px;
      right: 36px;
    }
    >.ant-card{
      border: none;
      border-radius: 0;
      border-top: 4px solid #3366ff;
      margin: 24px 0;
      &.app-form-card{
        >.ant-card-head{
          border-bottom: none;
          >.ant-card-head-wrapper{
            >.ant-card-head-title{
              >.app-form-card-head-title{
                text-align: center;
                font-weight: bold;
                color: #454545;
                >span{
                  border-bottom: 4px solid #3366ff;
                  padding-bottom: 3px;
                }
              }
            }
          }
        }
        >.ant-card-body{
          padding: 24px 36px;
          >.ant-form{
            .app-fieldset{
              >.ant-card-head{
                margin-left: -36px !important;
              }
              .ant-form-item{
              }
            }
            .app-form-card-actions{
              margin-top: 12px;
              margin-bottom: 0;
              .ant-form-item-control-input-content{
                display: flex;
                justify-content: center;
                .ant-btn{
                  &.app-form-submit-btn, &.app-form-cancel-btn{
                    padding: 8px 52px;
                    height: auto;
                  }
                  &.app-form-cancel-btn{
                    margin-left: 6px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}