import styles from "./result.module.scss";
import LoginBg from "@/assets/login_bg.7bce8874.png";
import {App, <PERSON><PERSON>, Card, Result, Typography} from "antd";

const {Paragraph, Text} = Typography;
import {CloseCircleOutlined, ClockCircleOutlined, ExclamationCircleFilled} from '@ant-design/icons'
import {attempt, get} from "lodash";
import {AuthInitCancel, AuthInitResetPWD} from "@/api/s";
import {useImmer} from "use-immer";
import InitForm from "@/pages/init/form";
import InitResetPwd from "@/pages/init/reset_pwd";
import {useContext, useEffect} from "react";
import TransferInstance from "@/utils/app/transfer";
import {useTranslation} from "react-i18next";
import ComponentLocale from "@/components/Locale";

export default function InitResult({user}) {
    const context = useContext(TransferInstance.rootContext)
    const {modal, message} = App.useApp()

    const { t, i18n } = useTranslation();


    const [state, updateState] = useImmer({
        reApply: false,
        resetPwd:false
    })
    
    const cancelAudit = () => {
        modal.confirm({
            title: t('tip'),
            icon: <ExclamationCircleFilled/>,
            content: t('confirm cancel need input relative information again'),
            okType: 'danger',
            onOk() {
                return new Promise((resolve, reject) => {
                    AuthInitCancel().then(() => {
                        message.success(t('operate success'))
                        setTimeout(() => {
                            window.location.reload()
                        }, 1000)
                    }).catch((e) => {
                        modal.error({
                            title: t('error'),
                            content: e.message || t('operate fail'),
                            centered: true,
                            maskClosable: false,
                        })
                    }).finally(() => {
                        resolve()
                    })
                })
            },
            onCancel() {
            },
        });
    }

    const toInitForm = () => {
        updateState(draft => {
            draft.reApply = true
        })
    }

    const toResetPwd = () => {
        updateState(draft => {
            draft.resetPwd = true
        })
    }

    const updateUserStatus = ()=>{
        return new Promise((resolve, reject) => {
            context.GlobalLoading(true)
            AuthInitResetPWD({
            }).then(()=>{
                context.GlobalLoading(false)
                resolve()
            }).catch(e=>{
                context.GlobalLoading(false)
                modal.warning({
                    title: t('tip'),
                    content: e.message,
                    centered: true,
                    maskClosable: true,
                })
                reject(e)
            })
        })
    }

    const toHomepage = () => {
        updateUserStatus().then(()=>{
            window.location.href='/'
        }).catch(()=>{})
    }

    const refreshListener = e=>{
        if (get(user, 'audit_result') === '审核通过'){
            e.preventDefault(); // 标准的写法
            updateUserStatus().then(()=>{
            }).catch(()=>{})
        }
    }

    useEffect(()=>{
        window.addEventListener('beforeunload', refreshListener);
        return ()=>{
            window.removeEventListener('beforeunload', refreshListener)
        }
    },[])


    if (state.reApply) {
        return (
            <InitForm user={user} showCancel={true} onCancel={()=>{
                updateState(draft => {
                    draft.reApply = false
                })
            }}/>
        )
    }

    if (state.resetPwd){
        return (
            <InitResetPwd onCancel={()=>{
                updateState(draft => {
                    draft.resetPwd = false
                })
            }} onOk={()=>{
                window.location.href='/'
            }} />
        )
    }
    
    return (
        <div className={styles.wrapper} style={{
            backgroundImage: `url(${LoginBg})`
        }}>
            <div className={"wrapper-inner"}>
                <ComponentLocale />

                <Card className={"app-form-card w-[580px] shadow-lg"}>
                    {get(user, 'audit_result') === '待审核' ? (
                        <Result
                            status={'warning'}
                            icon={<ClockCircleOutlined/>}
                            title={t('service checking')}
                            subTitle={t('service checking sub title')}
                        />
                    ) : null}
                    {get(user, 'audit_result') === '审核通过' ? (
                        <Result
                            status="success"
                            title={t('service approved')}
                            subTitle={t('service approved sub title')}
                            extra={[
                                <Button type="primary" key="console" onClick={toResetPwd}>{t('modify password')}</Button>,
                                <Button key="buy" onClick={toHomepage}>{t('enter immediate')}</Button>,
                            ]}
                        />
                    ) : null}
                    {get(user, 'audit_result') === '审核不通过' ? (
                        <Result
                            status="error"
                            title={t('service rejected')}
                            subTitle={t('service rejected sub title')}
                            extra={[
                                <Button type="primary" key="console" onClick={toInitForm}>
                                    {t('modify information')}
                                </Button>,
                                <Button key="buy" onClick={cancelAudit}>{t('abort apply')}</Button>,
                            ]}
                        >
                            <div className="desc">
                                <Paragraph>
                                    <Text
                                        strong
                                        style={{
                                            fontSize: 16,
                                        }}
                                    >
                                        {t('service rejected reason')}：
                                    </Text>
                                </Paragraph>
                                <Paragraph>
                                    <CloseCircleOutlined
                                        className="site-result-demo-error-icon"/> {get(user, 'audit_comment', t('unknown reason'))}
                                </Paragraph>
                            </div>
                        </Result>
                    ) : null}
                </Card>
            </div>
        </div>
    )
}