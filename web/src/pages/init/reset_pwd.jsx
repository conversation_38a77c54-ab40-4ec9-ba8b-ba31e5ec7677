import styles from './reset_pwd.module.scss'
import LoginBg from '@/assets/login_bg.7bce8874.png'
import {App, <PERSON><PERSON>, Card, Col, Form, Input, Row} from "antd";
import {useFormRules} from './reset_pwd.metadata'
import {attempt, get, isArray} from "lodash";
import {useContext, useEffect} from "react";
import TransferInstance from "@/utils/app/transfer";
import {AuthInitResetPWD} from "@/api/s";
import {useTranslation} from "react-i18next";
import ComponentLocale from "@/components/Locale";

export default function InitResetPwd({onOk,onCancel}) {
    const { t, i18n } = useTranslation();

    const FormRules = useFormRules()

    const context = useContext(TransferInstance.rootContext)
    const {message,modal} = App.useApp();

    const [form] = Form.useForm()

    const handleFormFinished = (values) => {
        context.GlobalLoading(true)
        AuthInitResetPWD({
            pwd: values.pwd
        }).then(()=>{
            if (values.pwd){
                message.success(t('operate success'))
            }
            setTimeout(()=>{
                attempt(onOk, values)
                context.GlobalLoading(false)
            }, 1000)
        }).catch(e=>{
            context.GlobalLoading(false)
            modal.warning({
                title: t('tip'),
                content: e.message,
                centered: true,
                maskClosable: true,
            })
        })
    }

    const handleFormFailed = (e) => {
        let msg = t('check form')
        if (isArray(e.errorFields) && e.errorFields.length > 0) {
            const firstField = e.errorFields[0]
            if (isArray(firstField.errors) && firstField.errors.length > 0) {
                msg = firstField.errors[0]
            }
        }
        modal.warning({
            title: t('tip'),
            content: msg,
            centered: true,
            maskClosable: true,
        })
    }

    useEffect(()=>{
        form.resetFields()
    },[])

    return (
        <div className={styles.wrapper} style={{
            backgroundImage: `url(${LoginBg})`
        }}>
            <div className={"wrapper-inner"}>
                <ComponentLocale />

                <Card className={"app-form-card w-[580px] shadow-lg"} title={
                    <div className={"app-form-card-head-title"}>
                        <span>{t('modify password')}</span>
                    </div>
                }>
                    <Form form={form}
                          layout={'vertical'}
                          labelAlign={"left"}
                          onFinish={handleFormFinished}
                          onFinishFailed={handleFormFailed}
                    >
                        <Form.Item style={{
                            marginTop: 24,
                            marginBottom: 48
                        }} name={"pwd"} rules={FormRules.password}>
                            <Input.Password size={'large'} variant={'filled'} placeholder={t('please enter the password')}/>
                        </Form.Item>
                        <Form.Item className={'app-form-card-actions'}>
                            <Button type={"primary"} className={"app-form-submit-btn"} onClick={form.submit}>{t('confirm')}</Button>
                            <Button className={'app-form-cancel-btn'} onClick={()=>attempt(onCancel)}>{t('cancel')}</Button>
                        </Form.Item>
                    </Form>
                </Card>
            </div>
        </div>
    )
}

