import {trim} from "lodash";
import {IdCardNumberIsValid, EmailIsValid, MobileIsValid} from '@/utils/validator'
import {useTranslation} from "react-i18next";

export function useInitFormRules() {
    const { t, i18n } = useTranslation();

    return {
        company_id: [
            {required: true, message: t('enterprise id cannot empty')}
        ],
        institution: [
            {required: true, message: t('agency name cannot empty')}
        ],
        industry: [
            {required: true, message: t('industry cannot empty')}
        ],
        access_addr: [
            {required: true, message: t('access location cannot empty')}
        ],
        belonging_region: [
            {required: true, message: t('area cannot empty')}
        ],
        real_name: [
            {required: true, message: t('real name cannot empty')}
        ],
        id_number: [
            {
                validator: (rule, value, callback) => {
                    value = trim(value || "")
                    if (!value) return Promise.reject(new Error(t('id number cannot empty')))
                    if (!IdCardNumberIsValid(value)) return Promise.reject(new Error(t('id number illegal')))
                    return Promise.resolve()
                },
            }
        ],
        email: [
            {
                validator: (rule, value, callback) => {
                    value = trim(value || "")
                    if (!value) return Promise.reject(new Error(t('email cannot empty')))
                    if (!EmailIsValid(value)) return Promise.reject(new Error(t('email illegal')))
                    return Promise.resolve()
                }
            }
        ],
        phone: [
            {
                validator: (rule, value, callback) => {
                    value = trim(value || "")
                    if (!value) return Promise.reject(new Error(t('mobile cannot empty')))
                    if (!MobileIsValid(value)) return Promise.reject(new Error(t('mobile illegal')))
                    return Promise.resolve()
                }
            }
        ],
    }
}
