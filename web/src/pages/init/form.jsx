import styles from './form.module.scss'
import LoginBg from '@/assets/login_bg.7bce8874.png'
import {App, Button, Card, Col, Form, Input, Row} from "antd";
import Fieldset from "@/components/Fieldset";
import {useInitFormRules} from './form.metadata'
import {attempt, get, isArray} from "lodash";
import {useContext, useEffect} from "react";
import TransferInstance from "@/utils/app/transfer";
import {AuthInit} from "@/api/s";
import {useTranslation} from "react-i18next";
import ComponentLocale from "@/components/Locale";

export default function InitForm({showCancel = false, user, onCancel}) {
    const { t, i18n } = useTranslation();

    const FormRules = useInitFormRules()

    const context = useContext(TransferInstance.rootContext)
    const {message, modal} = App.useApp();

    const [form] = Form.useForm()

    const handleFormFinished = (values) => {
        const postData = {
            // company_id: get(values, 'company_id'),
            institution: get(values, 'institution'),
            industry: get(values, 'industry'),
            access_addr: get(values, 'access_addr'),
            belonging_region: get(values, 'belonging_region'),
            real_name: get(values, 'real_name'),
            id_number: get(values, 'id_number'),
            email: get(values, 'email'),
            phone: get(values, 'phone'),
        }
        context.GlobalLoading(true)
        AuthInit(postData).then(() => {
            context.GlobalLoading(false)
            message.success(t('already submit'))
            setTimeout(() => {
                window.location.reload()
            }, 1000)
        }).catch((e) => {
            context.GlobalLoading(false)
            modal.warning({
                title: t('tip'),
                content: e.message,
                centered: true,
                maskClosable: true,
            })
        })
    }

    const handleFormFailed = (e) => {
        let msg = t('check form')
        if (isArray(e.errorFields) && e.errorFields.length > 0) {
            const firstField = e.errorFields[0]
            if (isArray(firstField.errors) && firstField.errors.length > 0) {
                msg = firstField.errors[0]
            }
        }
        modal.warning({
            title: t('tip'),
            content: msg,
            centered: true,
            maskClosable: true,
        })
    }

    useEffect(() => {
        form.resetFields()
        form.setFieldsValue({
            company_id: get(user, 'company_id'),
            institution: get(user, 'institution'),
            industry: get(user, 'industry'),
            access_addr: get(user, 'access_addr'),
            belonging_region: get(user, 'belonging_region'),
            real_name: get(user, 'real_name'),
            id_number: get(user, 'id_number'),
            email: get(user, 'email'),
            phone: get(user, 'phone'),
        })
    }, [user])

    return (
        <div className={styles.wrapper} style={{
            backgroundImage: `url(${LoginBg})`
        }}>
            <div className={"wrapper-inner"}>
                <ComponentLocale />

                <Card className={"app-form-card w-[580px] shadow-lg"} title={
                    <div className={"app-form-card-head-title"}>
                        <span>{t('system initialization')}</span>
                    </div>
                }>
                    <Form form={form}
                          layout={'vertical'}
                          labelAlign={"left"}
                          onFinish={handleFormFinished}
                          onFinishFailed={handleFormFailed}
                    >
                        <Fieldset legend={t('enterprise information')}>
                            <Row gutter={12}>
                                {/*<Col span={12}>*/}
                                {/*    <Form.Item label={"企业ID"} name={"company_id"} rules={FormRules.company_id}>*/}
                                {/*        <Input variant={'filled'} placeholder={"请输入企业ID"}/>*/}
                                {/*    </Form.Item>*/}
                                {/*</Col>*/}
                                <Col span={12}>
                                    <Form.Item label={t('agency name')} name={"institution"} rules={FormRules.institution}>
                                        <Input variant={'filled'} placeholder={t('please enter the agency name')}/>
                                    </Form.Item>
                                </Col>
                                <Col span={12}>
                                    <Form.Item label={t('belong industry')} name={"industry"} rules={FormRules.industry}>
                                        <Input variant={'filled'} placeholder={t('please enter the belong industry')}/>
                                    </Form.Item>
                                </Col>
                                <Col span={12}>
                                    <Form.Item label={t('access location')} name={"access_addr"} rules={FormRules.access_addr}>
                                        <Input variant={'filled'} placeholder={t('please enter the access location')}/>
                                    </Form.Item>
                                </Col>
                                <Col span={12}>
                                    <Form.Item label={t('belong area')} name={"belonging_region"}
                                               rules={FormRules.belonging_region}>
                                        <Input variant={'filled'} placeholder={t('please enter the belong area')}/>
                                    </Form.Item>
                                </Col>
                            </Row>
                        </Fieldset>
                        <Fieldset legend={t('contact person')}>
                            <Row gutter={12}>
                                <Col span={12}>
                                    <Form.Item label={t('real name')} name={"real_name"} rules={FormRules.real_name}>
                                        <Input variant={'filled'} placeholder={t('please enter the real name')}/>
                                    </Form.Item>
                                </Col>
                                <Col span={12}>
                                    <Form.Item label={t('id number')} name={"id_number"} rules={FormRules.id_number}
                                               required={true}>
                                        <Input variant={'filled'} placeholder={t('please enter the id number')}/>
                                    </Form.Item>
                                </Col>
                                <Col span={12}>
                                    <Form.Item label={t('email')} name={"email"} rules={FormRules.email}
                                               required={true}>
                                        <Input variant={'filled'} placeholder={t('please enter the email')}/>
                                    </Form.Item>
                                </Col>
                                <Col span={12}>
                                    <Form.Item label={t('mobile')} name={"phone"} rules={FormRules.phone}
                                               required={true}>
                                        <Input variant={'filled'} placeholder={t('please enter the mobile')}/>
                                    </Form.Item>
                                </Col>
                            </Row>
                        </Fieldset>
                        <Form.Item className={'app-form-card-actions'}>
                            <Button type={"primary"} className={"app-form-submit-btn"}
                                    onClick={form.submit}>{t('confirm')}</Button>
                            {showCancel ? (
                                <Button className={'app-form-cancel-btn'}
                                        onClick={() => attempt(onCancel)}>{t('cancel')}</Button>
                            ) : null}
                        </Form.Item>
                    </Form>
                </Card>
            </div>
        </div>
    )
}

