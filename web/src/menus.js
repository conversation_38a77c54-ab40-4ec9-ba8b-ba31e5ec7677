import {DeliveredProcedureOutlined, Container<PERSON>utlined, UserOutlined, <PERSON><PERSON><PERSON><PERSON>utlined, SettingOutlined, <PERSON>actionOutlined, CloudServerOutlined} from '@ant-design/icons'
// import {useTranslation} from "react-i18next";

const menus = [
  {
    "key": "rev",
    "label": "southbound data cross-border reception",
    "path": "/rev",
    "type": "link",
    "icon": <ContainerOutlined />
  },
  {
    "key": "sender",
    "label": "northbound data cross-border transmission",
    "path": "/sender",
    "type": "link",
    "icon": <DeliveredProcedureOutlined />
  },
  {
    "key": "config",
    "label": 'system config',
    "type": "group",
    "children": [
      {
        key: 'config_global',
        label: 'global watermark settings',
        path: '/config/global',
        "type": "link",
        "icon": <SettingOutlined />
      },
      {
        "key": "config_remote_upload",
        "label": "remote upload settings",
        "path": "/config/remote-upload",
        "type": "link",
        "icon": <InteractionOutlined />
      },
      {
        "key": "config_transport_channel",
        "label": "transport channel",
        "path": "/config/transport-channel",
        "type": "link",
        "icon": <CloudServerOutlined />
      },
      {
        "key": "config_user",
        "label": "user Management",
        "path": "/config/user",
        "type": "link",
        "icon": <UserOutlined />
      },
      {
        "key": "config_sys_info",
        "label": "resource information",
        "path": "/config/sys_info",
        "type": "link",
        "icon": <PieChartOutlined />
      },
    ]
  }
]

// export function useMenus() {
//   const { t, i18n } = useTranslation();
//
//   return menus.map(menu => {
//     menu.label = t(menu.label)
//     if (menu.children) {
//       menu.children = menu.children.map(child => {
//         child.label = t(child.label)
//         return child
//       })
//     }
//     return menu
//   })
// }
export default menus