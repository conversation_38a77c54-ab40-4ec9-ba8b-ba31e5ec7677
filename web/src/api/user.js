import request from '@/utils/http/request'

/**
 * 获取用户列表
 *
 * @param data
 * @returns {Promise}
 * @constructor
 */
export function GetUserList(data = {}) {
    return request.post("/user/list", data)
}

/**
 * 查找用户
 *
 * @param data
 * @returns {Promise}
 * @constructor
 */
export function FindUser(data = {}) {
    return request.post("/user/detail", data)
}

/**
 * 创建用户
 *
 * @param data
 * @returns {Promise}
 * @constructor
 */
export function CreateUser(data = {}) {
    return request.post("/user/create", data)
}

/**
 * 更新用户
 *
 * @param data
 * @returns {Promise}
 * @constructor
 */
export function UpdateUser(data = {}) {
    return request.post("/user/update", data)
}

/**
 * 重置密码
 *
 * @param data
 * @returns {Promise}
 * @constructor
 */
export function ResetUserPwd(data = {}) {
    return request.post("/user/reset-pwd", data)
}

/**
 * 切换用户状态
 *
 * @param data
 * @returns {Promise}
 * @constructor
 */
export function ToggleUserStatus(data = {}) {
    return request.post("/user/toggle-status", data)
}

/**
 * 删除用户
 *
 * @param data
 * @returns {Promise}
 * @constructor
 */
export function DeleteUser(data = {}) {
    return request.post("/user/delete", data)
}
