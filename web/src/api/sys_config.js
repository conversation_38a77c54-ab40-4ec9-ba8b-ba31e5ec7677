import request from "@/utils/http/request";

/**
 * 获取系统配置
 *
 * @returns {Promise}
 * @constructor
 */
export function GetSysConfig() {
    return request.get("/sys-config/get")
}

/**
 * 更新系统配置
 *
 * @param data
 * @returns {Promise}
 * @constructor
 */
export function UpdateSysConfig(data = {}) {
    return request.put("/sys-config/update", data)
}

/**
 * 获取远程分发配置
 *
 * @returns {Promise}
 * @constructor
 */
export function GetSysRemoteUploadConfig() {
    return request.get("/sys-config/get-remote-upload-config")
}


/**
 * 更新远程分发配置
 *
 * @param data
 * @returns {Promise}
 * @constructor
 */
export function UpdateSysRemoteUploadConfig({data = {}}) {
    return request.put("/sys-config/update-remote-upload-config", data)
}