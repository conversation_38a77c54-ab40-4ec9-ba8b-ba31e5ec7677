import request from "@/utils/http/request";

const basePath = "/rev"

/**
 * 获取文件列表
 *
 * @param data
 * @param params
 * @returns {Promise}
 * @constructor
 */
export function GetFileList(data = {}, params = {}) {
    return request.post(`${basePath}/file/list`, data, {
        params
    })
}

/**
 * 预览子文件下载水印信息
 *
 * @param data
 * @returns {Promise}
 * @constructor
 */
export function GetPreviewChildFileWatermarkInfo(data = {}) {
    return request.post(`${basePath}/file/preview-child-file-watermark-info`, data)
}

/**
 * 获取文件子文件
 *
 * @param data
 * @param params
 * @returns {Promise}
 * @constructor
 */
export function GetChildFiles(data = {}, params = {}) {
    return request.post(`${basePath}/file/child-files`, data, {
        params
    })
}

/**
 * 下载子文件
 *
 * @param data
 * @returns {Promise}
 * @constructor
 */
export function DownloadChildFile(data = {}) {
    return request.post(`${basePath}/file/child-file-download`, data, {
        responseType: "blob"
    })
}

/**
 * 切换文档的归档状态
 *
 * @param data
 * @returns {Promise}
 * @constructor
 */
export function ToggleArchived(data = {}) {
    return request.post(`${basePath}/file/toggle-archived`, data)
}

/**
 * 获取子文件最新下载记录
 *
 * @param data
 * @returns {Promise}
 * @constructor
 */
export function GetChildFileLatestDownloadRecord(data = {}) {
    return request.post(`${basePath}/file/child-file-download/latest-record`, data)
}
