import TransferInstance from "@/utils/app/transfer";

/**
 * 退出
 *
 * @param data
 * @returns {Promise}
 * @constructor
 */
export function AuthLogout(data = {}) {
    return TransferInstance.requestInstance.post("/auth/logout", data);
}

/**
 * 获取系统资源详情
 *
 * @returns {Promise}
 * @constructor
 */
export function GetSysResources() {
    return TransferInstance.requestInstance.get('/sys/resources')
}

/**
 * 获取当前登录的用户信息
 *
 * @returns {Promise}
 * @constructor
 */
export function GetMe() {
    return TransferInstance.requestInstance.post("/auth/me")
}

/**
 * 初始化
 *
 * @param data
 * @returns {Promise}
 * @constructor
 */
export function AuthInit(data = {}) {
    return TransferInstance.requestInstance.post("/auth/init", data)
}

/**
 * 取消初始化
 *
 * @param data
 * @returns {Promise}
 * @constructor
 */
export function AuthInitCancel(data = {}) {
    return TransferInstance.requestInstance.post("/auth/init-cancel", data)
}

/**
 * 重置密码
 *
 * @param data
 * @returns {Promise}
 * @constructor
 */
export function AuthInitResetPWD(data = {}) {
    return TransferInstance.requestInstance.post("/auth/init/reset-pwd", data)
}