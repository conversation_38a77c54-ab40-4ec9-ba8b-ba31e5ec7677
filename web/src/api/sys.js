import request from "@/utils/http/request";

/**
 * 获取系统资源详情
 *
 * @returns {Promise}
 * @constructor
 */
export function GetSysResources() {
    return request.get('/sys/resources')
}

/**
 * 获取通道信息
 *
 * @returns {Promise}
 * @constructor
 */
export function GetTransportChannelInfo() {
    return request.get("/sys/transport-channel-info")
}

/**
 * 获取SFTP服务状态
 *
 * @returns {Promise}
 * @constructor
 */
export function GetSftpStatus() {
    return request.get("/sys/sftp-status")
}

/**
 * 获取DICOM服务状态
 *
 * @returns {Promise}
 * @constructor
 */
export function GetDicomStatus() {
    return request.get("/sys/dicom-status")
}