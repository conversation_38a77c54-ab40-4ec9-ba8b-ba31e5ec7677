import request from "@/utils/http/request";
import {startsWith} from "lodash";

const basePath = "/sender"

/**
 * 上传文件
 *
 * @param option
 * @returns {Promise}
 * @constructor
 */
export function UploadFile(option) {
    const formData = new FormData();
    formData.append(option.filename || 'file', option.file);
    let uri = `${basePath}/file/upload`
    if (option.action) {
        if (startsWith(option.action, "http") || startsWith(option.action, "//")) {
            uri = option.action
        } else {
            uri = option.action
        }
    }
    return request.post(uri, {
        [option.filename || 'file']: option.file
    }, {
        headers: {
            "Content-Type": "multipart/form-data",
            ...option.headers
        },
        onUploadProgress: function (progressEvent) {
            let progress = (progressEvent.progress || 0) * 100
            option.onProgress({percent: parseInt(String(progress), 10)})
        }
    })
}

/**
 * @param option
 * @param formData
 * @param params
 * @returns {Promise}
 * @constructor
 */
export function CustomUploadFile(option, formData, params = {}) {
    let uri = `${basePath}/file/upload`
    if (option.action) {
        if (startsWith(option.action, "http") || startsWith(option.action, "//")) {
            uri = option.action
        } else {
            uri = option.action
        }
    }
    return request.post(uri, formData, {
        headers: {
            "Content-Type": "multipart/form-data",
            ...option.headers
        },
        onUploadProgress: function (progressEvent) {
            let progress = (progressEvent.progress || 0) * 100
            option.onProgress({percent: parseInt(String(progress), 10)})
        },
        params
    })
}

/**
 * 获取文件列表
 *
 * @param data
 * @param params
 * @returns {Promise}
 * @constructor
 */
export function GetFileList(data = {}, params = {}) {
    return request.post(`${basePath}/file/list`, data, {
        params
    })
}

/**
 * 预览上传水印信息
 *
 * @param data
 * @returns {Promise}
 * @constructor
 */
export function GetPreviewUploadWatermarkInfo(data = {}) {
    return request.post(`${basePath}/file/preview-upload-watermark-info`, data)
}