export const PAGINATION = {
    currentPageKey: "page",
    pageSizeKey: "size",
    showTotal: (total, range) => {
        const end = range[1]>total?total:range[1];
        return <span style={{
            color: "#888888"
        }}>共 {total} 项，当前展示 {range[0]} 至 {end} 项</span>
    },
    hideOnSinglePage: false,
    showQuickJumper: true,
    defaultCurrentPage: 1,
    defaultPageSize: 15
}

export const TABLE_STICKY_OFFSET_HEADER = -12

export const TABLE_SCROLL_X = "max-content"

export const TREE_ROOT_ID = 0

export const PRIMARY_KEY = "id"
