import {createElement, useEffect} from "react";
import {<PERSON><PERSON><PERSON><PERSON>out<PERSON>, useLocation, useRoutes} from "react-router-dom";
import routes from "@/router";
import InitForm from "@/pages/init/form";
import InitResult from "@/pages/init/result";
import {get, trim} from "lodash";
import <PERSON><PERSON> from "js-cookie";
import {useImmer} from "use-immer";
import {Spin, App as BoxApp} from "antd";
import {LoadingOutlined} from "@ant-design/icons";
import {GetMe} from "@/api/s";

export default function App() {
    const {modal, message} = BoxApp.useApp();

    const [state, updateState] = useImmer({
        initialized: false,
        user: null,
        isLoginPage: false,
    })

    const init = async () => {
        if (window.location.pathname.startsWith("/login")){
            updateState(draft => {
                draft.initialized = true;
                draft.isLoginPage = true
            })
            return
        }
        const uid = trim(Cookie.get('m_auth_uid') || '')
        if (!uid) {
            window.location.href = (process.env.REACT_APP_BASE_PATH || '') + "/login"
            return;
        }
        GetMe().then(({data}) => {
            updateState(draft => {
                draft.initialized = true
                draft.isLoginPage = false
                draft.user = data
            })
        }).catch(e => {
            modal.error({
                title: "提示",
                content: e.message,
                centered: true,
                maskClosable: true,
            })
        })
    }

    useEffect(() => {
        init().catch(() => {
        })
    }, [])

    if (!state.initialized) {
        return (
            <Spin size={'large'} spinning={true} indicator={
                <LoadingOutlined
                    style={{
                        fontSize: 36,
                    }}
                    spin
                />
            }/>
        )
    }

    if (!state.isLoginPage){
        if (!state.user) {
            return (
                <Spin size={'large'} spinning={true} indicator={
                    <LoadingOutlined
                        style={{
                            fontSize: 36,
                        }}
                        spin
                    />
                }/>
            )
        }

        if (state.user.is_admin){
            if (!state.user.audit_result) {
                return (
                    <InitForm />
                )
            }

            if (!state.user.status) {
                return (
                    <InitResult user={state.user} />
                )
            }
        }
    }

    return (
        <BrowserRouter
            basename={process.env.REACT_APP_BASE_PATH || '/'}>
            {createElement(() => useRoutes(routes))}
        </BrowserRouter>
    )
}