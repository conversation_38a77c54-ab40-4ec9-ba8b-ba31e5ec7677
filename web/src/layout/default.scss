#__LAYOUT_DEFAULT__{
  position: relative;
  height: 100%;
  overflow: hidden;

  >.ant-layout-header{
    //background-color: #ffffff;
    z-index: 9;
    padding-left: 24px;
    padding-right: 24px;
    @apply bg-blue-900;
    .top-warning{
      justify-content: center;
      .ant-alert-content{
        flex: none;
      }
    }
    .box-header-inner{
      display: flex;
      justify-content: space-between;
      >.box-header-left{
        >.box-header-left-inner{
          height: 100%;
          >.box-header-brand{
            height: 100%;
            display: flex;
            align-items: center;
            .box-header-brand-logo{
              width: 52px;
              height: 52px;
            }
            >.box-header-brand-name{
              line-height: 1;
              margin-left: 8px;
              position: relative;
              >.box-header-brand-name__main{
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
              }
              >.box-header-brand-name__sub{
                color: #dddddd;
                font-weight: 300;
                font-size: 12px;
                margin-top: 8px;
              }
              >.version-tag{
                border-width: 1px;
                border-style: solid;
                //border-color: #0042ff;
                @apply border-indigo-400;
                //color: transparent;
                //background: #0042ff;
                //@apply bg-indigo-100;
                //background-clip: text;
                @apply text-indigo-100;
                font-size: 10px;
                padding: 3px 6px;
                border-radius: 4px;
                line-height: 1;
                position: absolute;
                top: -2px;
                left: 108px;
                width: max-content;
              }
            }
          }
        }
      }
      >.box-header-right{
        >.box-header-right-inner{
          display: flex;
          >.component-locale{
            margin-right: 12px;
            >.wrapper-inner{
              >.ant-select{
                >.ant-select-selector{
                  color: #ffffff;
                  background: none;
                  border-color: #ffffff !important;
                  .ant-select-selection-item{
                    color: #ffffff !important;
                  }
                }
                >.ant-select-arrow{
                  color: #ffffff;
                }
              }
            }
          }
        }
      }
    }
  }

  >.ant-layout{
    flex-direction: row;
    >.ant-layout-sider{
      @apply bg-blue-900;
      >.ant-layout-sider-children{
        >.ant-card{
          height: 100%;
          overflow: hidden;
          display: flex;
          flex-direction: column;
          border: none;
          background: none;
          border-radius: 0;
          >.ant-card-head{
            color: rgba(255, 255, 255, 0.65);
            border-color: #0a297e;
          }
          >.ant-card-body{
            flex: 1;
            padding: 0;
            overflow: hidden auto;
            >.ant-menu{
              background: none;
            }
          }
        }
      }
    }
    >.ant-layout-content{
      overflow: hidden;
      .box-content-wrapper{
        position: relative;
        height: 100%;
        overflow: hidden auto;

        .box-footer-content{
          padding: 24px 0;
          .box-footer-copyright{
            text-align: center;
            color: #aaaaaa;
          }
        }
      }
    }
  }
}