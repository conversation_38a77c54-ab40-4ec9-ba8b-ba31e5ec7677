import {Outlet, useLocation} from "react-router-dom";
import './default.scss'
import {App, Image, Layout, Dropdown, Space, Card, Menu, Alert} from "antd";
import {DownOutlined, ExclamationCircleFilled} from '@ant-design/icons'
import Logo from '@/assets/logo.svg'
import SimpleBar from "simplebar-react";
import {useImmer} from "use-immer";
import {useEffect, useRef} from "react";
import Cookie from 'js-cookie'
import {cloneDeep, get, groupBy, isArray, keyBy, map, trim, trimEnd} from "lodash";
import {AuthLogout, GetMe} from "@/api/s";
import MenusJson from "@/menus.js";
import {getPathByKey, toPlainArray} from "@/utils/tree";
import {useRouter} from "@/utils/app/transfer";
import {RootContext} from "@/provider/root";
import {GetSysResources} from "@/api/s";
import {useTranslation} from "react-i18next";
import ComponentLocale from '@/components/Locale'

let refreshSysResourceInterval = null;

export default function Default() {
    const {modal, message} = App.useApp();
    const {pathname} = useLocation()
    const router = useRouter()
    const scrollbarRef = useRef()
    const { t, i18n } = useTranslation();

    const handleScrollTo = (target) => {
        scrollbarRef.current.scrollTo({
            top: target,
            behavior: 'smooth'
        })
    }

    const [state, updateState] = useImmer({
        user: null,
        menus: {
            treeData: [],
            menusTreeData: [],
            keyIndexMenusTreeData: {},
            plainTreeData: [],
            groupByPathTreeData: {},
            data: [],
            keyIndexData: {},
            openKeys: [],
            selectedKey: undefined
        },
        alertWarning: '',
        initialized: false,
    })

    const handleRightDropdownMenuClick = ({key}) => {
        switch (key) {
            case 'logout':
                modal.confirm({
                    title: t('tip'),
                    icon: <ExclamationCircleFilled/>,
                    content: t('logout determination'),
                    onOk() {
                        return new Promise((resolve, reject) => {
                            AuthLogout().then(() => {
                                message.success(t('already logout'))
                                setTimeout(() => {
                                    window.location.href = (process.env.REACT_APP_BASE_PATH || '') + "/login"
                                }, 1000)
                            }).catch(() => {
                                message.error(t('logout fail'))
                            })
                        })
                    },
                    onCancel() {
                    },
                })
                break
        }
    }


    // ----------------------------- aside menus ------------------------------------
    const handleMenus = (menus = [], basePath = "") => {
        const _menuOpenKeys = []
        const loop = (menusChildren = []) => {
            return menusChildren.filter((child) => {
                child._label = child.label
                child.label = t(child.label)
                if (child.children) {
                    child.children = loop(child.children)
                    if (child.type === 'group' || child.type === 'submenu') {
                        if (!_menuOpenKeys.length) {
                            _menuOpenKeys.push(child.key)
                        }
                    }
                }
                if (child.path){
                    if (child.path.startsWith("/")) {
                        child.path = basePath + child.path
                    }
                    if (child.path.endsWith("/")) {
                        child.path = trimEnd(child.path, "/")
                    }
                }
                return true
            })
        }
        return {
            menus: loop(cloneDeep(menus)),
            openKeys: _menuOpenKeys
        }
    }
    const handleTreeDataMenus = (menus = []) => {
        const loop = (menusChildren = []) => {
            return menusChildren.filter((child) => {
                if (child.children && (child.type === 'group' || child.type === 'submenu')) {
                    child.children = loop(child.children)
                } else {
                    if (typeof child.children !== "undefined") {
                        delete child.children
                    }
                }
                return true
            })
        }
        return loop(cloneDeep(menus))
    }
    const findSelectedMenu = (menus, idIndexMenusTreeData) => {
        let selectedMenu
        let selectedParentsKeys = []
        const menuParents = getPathByKey("path", "children", pathname, cloneDeep(menus), true)
        if (menuParents) {
            const reversedMenuParents = menuParents.reverse();
            for (let i = 0; i < reversedMenuParents.length; i++) {
                const node = reversedMenuParents[i]
                if (idIndexMenusTreeData[node.key]) {
                    selectedMenu = node
                    break
                }
            }
            selectedParentsKeys = map(reversedMenuParents, 'key')
            if (selectedParentsKeys.length) {
                selectedParentsKeys.shift()
            }
        }
        return {selectedMenu, selectedParentsKeys}
    }
    const findFirstLink = (menus) => {
        let first
        const loop = (menusChildren = []) => {
            if (first) return;
            for (let i = 0; i < menusChildren.length; i++) {
                const child = menusChildren[i]
                if (child.type === 'link') {
                    first = child
                    return
                }
                if (isArray(child.children)) {
                    loop(child.children)
                }
            }
        }
        loop(cloneDeep(menus))
        return first
    }

    const handleMenuOpenChange = (openKeys) => {
        updateState(draft => {
            draft.menus.openKeys = openKeys
        })
    }
    const handleMenuClick = ({key}) => {
        updateState(draft => {
            draft.menus.selectedKey = key
        })

        const menu = state.menus.keyIndexData[key]
        switch (menu.type) {
            case "link":
                if (menu.path !== "") {
                    // const menuPath = get(state.products.current, 'path') + menu.path
                    if (menu.path !== pathname) {
                        router.push(menu.path)
                    } else {
                        // window.location.reload()
                    }
                }
                break;
            default:
        }
    }
    // ----------------------------- aside menus ------------------------------------

    const refreshSysResources = ()=>{
        GetSysResources().then(({data})=>{
            // let memoryUsedPercent = parseFloat(get(data,'memory.usedPercent',0).toFixed(2))
            // if(memoryUsedPercent > 100){
            //     memoryUsedPercent = 100
            // }
            //
            let projectDiskUsedPercent = parseFloat(get(data,'projectDisk.usedPercent',0).toFixed(2))
            if(projectDiskUsedPercent > 100){
                projectDiskUsedPercent = 100
            }
            const threshold = 80
            if (projectDiskUsedPercent>=threshold){
                updateState(draft => {
                    draft.alertWarning = `硬盘告警：磁盘剩余空间不足，使用率已超过${threshold}%，请及时处理！`
                })
            }else{
                updateState(draft => {
                    draft.alertWarning = ""
                })
            }
        }).catch(()=>{})
    }

    const init = async ()=>{
        const uid = trim(Cookie.get('m_auth_uid') || '')
        if (!uid) {
            window.location.href = (process.env.REACT_APP_BASE_PATH || '') + "/login"
            return;
        }

        let user
        try {
            const {data} = await GetMe()
            user = data
        }catch (e) {
            modal.warning({
                title: t('tip'),
                content: e.message,
                centered: true,
                maskClosable: true,
            })
            return
        }

        const treeData = MenusJson
        const {menus, openKeys} = handleMenus(cloneDeep(treeData), "")
        const menusTreeData = handleTreeDataMenus(menus)
        const keyIndexMenusTreeData = keyBy(toPlainArray(menusTreeData, "children"), "key")

        const plainTreeData = toPlainArray(cloneDeep(menus))
        const groupByPathTreeData = groupBy(plainTreeData, "path")
        // const data = toPlainArray(cloneDeep(treeData), "children")
        const keyIndexData = keyBy(plainTreeData, "key")

        const {selectedMenu, selectedParentsKeys} = findSelectedMenu(menus, keyIndexMenusTreeData)
        updateState(draft => {
            draft.menus.treeData = menus
            draft.menus.menusTreeData = menusTreeData
            draft.menus.keyIndexMenusTreeData = keyIndexMenusTreeData
            draft.menus.plainTreeData = plainTreeData
            draft.menus.groupByPathTreeData = groupByPathTreeData
            draft.menus.data = plainTreeData
            draft.menus.keyIndexData = keyIndexData
            draft.menus.openKeys = selectedParentsKeys && selectedParentsKeys.length ? selectedParentsKeys : openKeys
            draft.menus.selectedKey = selectedMenu ? selectedMenu.key : undefined
        })
        if (process.env.NODE_ENV === 'development'){
            console.log("selectedMenu:", selectedMenu)
        }
        if (!selectedMenu) {
            const firstLinkMenu = findFirstLink(menus)
            if (firstLinkMenu) {
                router.push(firstLinkMenu.path)
            }
        }

        updateState(draft => {
            draft.user = user
        })
    }

    useEffect(() => {
        init().then(()=>{
            updateState(draft => {
                draft.initialized = true
            })
        }).catch(()=>{})
    }, [])

    useEffect(() => {
        if (!state.menus.treeData.length) {
            return
        }
        const {selectedMenu} = findSelectedMenu(state.menus.treeData, state.menus.keyIndexMenusTreeData)
        if (process.env.NODE_ENV === 'development'){
            console.log("pathname change:", pathname)
            console.log("selectedMenu1:", selectedMenu)
        }
        updateState(draft => {
            draft.menus.selectedKey = selectedMenu ? selectedMenu.key : undefined
        })
    }, [pathname])

    useEffect(()=>{
        // refreshSysResourceInterval = setInterval(()=>{
        //     refreshSysResources()
        // }, 1000 * 5)
        return ()=>{
            refreshSysResourceInterval && clearInterval(refreshSysResourceInterval)
        }
    },[])

    const updateMenu = ()=>{
        const loop = (children)=>{
            return children.filter(child=>{
                child.label = t(child._label)
                if (child.children){
                    child.children = loop(child.children)
                }
                return true
            })
        }
        updateState(draft => {
            draft.menus.menusTreeData = loop(cloneDeep(state.menus.menusTreeData))
        })
    }

    useEffect(() => {
        if (!state.initialized) return;
        updateMenu()
    }, [i18n.language]);

    return (
        get(state.user,'username') ? (
            <RootContext.Provider value={{
                user: state.user,
            }}>
                <Layout id={"__LAYOUT_DEFAULT__"}>
                    <Layout.Header className={'shadow'} style={{
                        padding: 0,
                        height: 'auto'
                    }}>
                        <div className={'h-full'}>
                            {state.alertWarning? (
                                <Alert className={'top-warning'} showIcon={true} banner={true} type={'warning'} message={state.alertWarning} />
                            ): null}
                            <div className={"box-header-inner px-6"}>
                                <div className={'box-header-left'}>
                                    <div className={'box-header-left-inner'}>
                                        <a href={'/'} className={'box-header-brand'}>
                                            <Image preview={false} src={Logo} className={'box-header-brand-logo'}/>
                                            <div className={'box-header-brand-name'}>
                                                <div className={'box-header-brand-name__main'}>{t('app main name')}</div>
                                                <div className={'box-header-brand-name__sub'}>{t('app sub name')}
                                                </div>
                                                <div className={'version-tag'}>{t('hk hong hong client')}</div>
                                            </div>
                                        </a>
                                    </div>
                                </div>
                                <div className={'box-header-right'}>
                                    <div className={'box-header-right-inner'}>
                                        <ComponentLocale  />
                                        {get(state.user,'username') ? (
                                            <Dropdown
                                                menu={{
                                                    items: [
                                                        {
                                                            key: 'logout',
                                                            label: t('logout')
                                                        }
                                                    ],
                                                    onClick: handleRightDropdownMenuClick
                                                }}
                                            >
                                                <a onClick={(e) => e.preventDefault()} style={{
                                                    color: "#ffffff"
                                                }}>
                                                    {t('welcome')}，
                                                    <Space>
                                                        {get(state.user,'username')}
                                                        <DownOutlined/>
                                                    </Space>
                                                </a>
                                            </Dropdown>
                                        ) : null}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </Layout.Header>
                    <Layout>
                        <Layout.Sider>
                            <Card title={t('console')}>
                                <Menu
                                    mode={'inline'}
                                    theme={'dark'}
                                    openKeys={state.menus.openKeys}
                                    items={state.menus.menusTreeData}
                                    onOpenChange={handleMenuOpenChange}
                                    onClick={handleMenuClick}
                                    selectedKeys={state.menus.selectedKey ? [state.menus.selectedKey] : []}
                                />
                            </Card>
                        </Layout.Sider>
                        <Layout.Content>
                            <Outlet/>

                            {/*<SimpleBar id={'BoxScrollWrapper'} scrollableNodeProps={{ref: scrollbarRef}} className={'box-content-wrapper'} autoHide={false}>*/}
                            {/*    <RootContext.Provider value={{*/}
                            {/*        scrollTo: handleScrollTo*/}
                            {/*    }}>*/}
                            {/*        <Outlet/>*/}
                            {/*    </RootContext.Provider>*/}
                            {/*    /!*<div className={'box-footer-content'}>*!/*/}
                            {/*    /!*    <div*!/*/}
                            {/*    /!*        className={'box-footer-copyright'}>Copyright &copy; {new Date().getFullYear()} 深港数据跨境安全便捷客户端*!/*/}
                            {/*    /!*        All Rights Reserved.*!/*/}
                            {/*    /!*    </div>*!/*/}
                            {/*    /!*</div>*!/*/}
                            {/*</SimpleBar>*/}
                        </Layout.Content>
                    </Layout>
                </Layout>
            </RootContext.Provider>
        ) : null
    )
}