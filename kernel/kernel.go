package kernel

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"

	"code.ixdev.cn/cnix/cbdv/hk-box-be/core"
	config2 "code.ixdev.cn/cnix/cbdv/hk-box-be/core/config"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/core/config/pkg"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/core/metadata"
	setup2 "code.ixdev.cn/cnix/cbdv/hk-box-be/core/setup"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/router"
	http2 "code.ixdev.cn/liush/gin"
	"code.ixdev.cn/liush/gin/config"
	"code.ixdev.cn/liush/id-gen/idgen"

	// "github.com/elastic/elastic-transport-go/v8/elastictransport"
	// "github.com/elastic/go-elasticsearch/v8"
	"github.com/gin-gonic/gin"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	"github.com/mitchellh/mapstructure"
	"github.com/redis/go-redis/v9"
	"github.com/spf13/viper"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// Initialize 初始化
func Initialize(ctx context.Context) error {
	conf := new(config2.Config)
	err := viper.Unmarshal(conf, func(d *mapstructure.DecoderConfig) {
		d.TagName = "yaml"
	})
	if err != nil {
		return err
	}

	c := new(core.ContainerS)
	c.Conf = conf
	c.Pkg = new(core.PkgS)

	development := c.Conf.App.Env == metadata.EnvDev
	debug := c.Conf.App.Debug
	isPreCompile, _ := ctx.Value("APP_PROJECT_PRE_COMPILE").(bool)

	var defaultLogger *zap.SugaredLogger
	// initialize logger
	for _, l := range c.Conf.Pkg.Logger {
		logger, err := setup2.InitLogger(ctx, development, debug, l)
		if err != nil {
			return fmt.Errorf("init logger err: %v", err)
		}
		if c.Pkg.Logger == nil {
			c.Pkg.Logger = map[string]*zap.SugaredLogger{}
		}
		sLog := logger.Sugar()
		if l.Name == core.DefaultKey {
			defaultLogger = sLog
		}
		c.Pkg.Logger[l.Name] = sLog
	}

	// initialize database
	if c.Conf.Pkg.Database != nil && !isPreCompile {
		var zapGormLogger *setup2.ZapGormLogger
		if defaultLogger != nil {
			zapGormLogger = &setup2.ZapGormLogger{
				ZapLogger: defaultLogger,
			}
		}
		for _, database := range c.Conf.Pkg.Database {
			db, err := setup2.InitDatabase(ctx, database, zapGormLogger)
			if err != nil {
				return fmt.Errorf("init database err: %v", err)
			}
			if c.Pkg.Database == nil {
				c.Pkg.Database = map[string]*gorm.DB{}
			}
			if debug {
				db = db.Debug()
			}
			c.Pkg.Database[database.Name] = db
		}
	}

	// initialize redis
	redisConfigMap := make(map[string]*pkg.Redis)
	if c.Conf.Pkg.Redis != nil && !isPreCompile {
		for _, r := range c.Conf.Pkg.Redis {
			redisClient, err := setup2.InitRedis(ctx, r)
			if err != nil {
				return fmt.Errorf("init redis err: %v", err)
			}
			if c.Pkg.Redis == nil {
				c.Pkg.Redis = map[string]redis.UniversalClient{}
				c.Pkg.RedisPool = map[string]*redsync.Redsync{}
			}
			c.Pkg.Redis[r.Name] = redisClient
			c.Pkg.RedisPool[r.Name] = redsync.New(goredis.NewPool(redisClient))
			redisConfigMap[r.Name] = r
		}
	}

	//// initialize id generator
	var idGeneratorWorkerIdOptions *idgen.IdGeneratorWorkerIdOptions
	if c.Conf.App.IdGenerator != nil && !isPreCompile {
		if c.Conf.App.IdGenerator.RedisName != "" {
			redisConf, ok := redisConfigMap[c.Conf.App.IdGenerator.RedisName]
			if !ok {
				return fmt.Errorf("id generator redis name [%s] not exist", c.Conf.App.IdGenerator.RedisName)
			}
			idGeneratorWorkerIdOptions = &idgen.IdGeneratorWorkerIdOptions{
				Address:         strings.Join(redisConf.Addrs, ","),
				Password:        redisConf.Password,
				DB:              redisConf.DB,
				MasterName:      redisConf.MasterName,
				MinWorkerId:     0,
				LifeTimeSeconds: 0,
				WorkerIdPrefix:  c.Conf.App.IdGenerator.WorkerIdPrefix,
			}
		}
	}

	//if idGeneratorWorkerIdOptions == nil {
	//	return fmt.Errorf("id generator require redis")
	//}
	baseTime, _ := time.ParseInLocation(time.DateOnly, "2024-11-11", time.Local)
	c.Pkg.Flake = idgen.NewIdGenerator(&idgen.IdGeneratorOptions{
		Method:            0,
		BaseTime:          baseTime.UnixMilli(),
		WorkerIdBitLength: 0,
		SeqBitLength:      0,
		MaxSeqNumber:      0,
		MinSeqNumber:      0,
		TopOverCostCount:  0,
	}, idGeneratorWorkerIdOptions)
	if err := c.Pkg.Flake.Initialize(); err != nil {
		return fmt.Errorf("id generator err: %v", err)
	}

	// initialize kafka
	// if c.Conf.Pkg.Kafka != nil && !isPreCompile {
	// 	// initialize kafka producers
	// 	if c.Conf.Pkg.Kafka.Producer != nil {
	// 		for _, s := range c.Conf.Pkg.Kafka.Producer {
	// 			p, err := kafka.NewProducer(s.ConfigMap)
	// 			if err != nil {
	// 				return fmt.Errorf("initialize kafka producer %s fialed: %v", s.Name, err)
	// 			}
	// 			if c.Pkg.KafkaProducers == nil {
	// 				c.Pkg.KafkaProducers = map[string]*kafka.Producer{}
	// 			}
	// 			c.Pkg.KafkaProducers[s.Name] = p
	// 		}
	// 	}
	// 	// initialize kafka consumers
	// 	if c.Conf.Pkg.Kafka.Consumer != nil {
	// 		for _, s := range c.Conf.Pkg.Kafka.Consumer {
	// 			consumer, err := kafka.NewConsumer(s.ConfigMap)
	// 			if err != nil {
	// 				return fmt.Errorf("initialize kafka consumer %s failed: %v", s.Name, err)
	// 			}
	// 			if c.Pkg.KafkaConsumers == nil {
	// 				c.Pkg.KafkaConsumers = map[string]*kafka.Consumer{}
	// 			}
	// 			c.Pkg.KafkaConsumers[s.Name] = consumer
	// 		}
	// 	}
	// }

	// elasticsearch
	// if c.Conf.Pkg.Elasticsearch != nil && !isPreCompile {
	// 	for _, e := range c.Conf.Pkg.Elasticsearch {
	// 		esCfg := e.Config
	// 		esCfg.Logger = &elastictransport.ColorLogger{
	// 			Output:             os.Stdout,
	// 			EnableRequestBody:  true,
	// 			EnableResponseBody: false,
	// 		}
	// 		tc, err := elasticsearch.NewTypedClient(esCfg)
	// 		if err != nil {
	// 			return fmt.Errorf("initialize elasticsearch client %s failed: %v", e.Name, err)
	// 		}

	//pkgIndex := (&model.RevPackage{}).EsIndexName()
	//idxExistRes, err := tc.Indices.Exists(pkgIndex).Do(ctx)
	//if err != nil {
	//	return err
	//}
	//if !idxExistRes {
	//	res, err := tc.Indices.Create(pkgIndex).
	//		Request(&create.Request{
	//			Mappings: &types.TypeMapping{
	//				Properties: map[string]types.Property{
	//					"id": types.TextProperty{
	//						Fields: map[string]types.Property{
	//							"keyword": types.KeywordProperty{IgnoreAbove: pointer.Int(256)},
	//						},
	//					},
	//					"send_code": types.KeywordProperty{
	//						IgnoreAbove: pointer.Int(256),
	//					},
	//					"filename": types.KeywordProperty{
	//						IgnoreAbove: pointer.Int(256),
	//					},
	//					"sender_id": types.KeywordProperty{
	//						IgnoreAbove: pointer.Int(256),
	//					},
	//					"archived_at": types.TypeMapping{
	//						Properties: map[string]types.Property{
	//							"Time":  types.DateProperty{},
	//							"Valid": types.BooleanProperty{},
	//						},
	//					},
	//					"deleted_at":        types.DateProperty{},
	//					"tar_hash_verified": types.BooleanProperty{},
	//					"zip_hash_verified": types.BooleanProperty{},
	//				},
	//			},
	//		}).
	//		Do(nil)
	//	if err != nil {
	//		return err
	//	}
	//	log.Printf("create index in elasticsearch result %v\n", res.Acknowledged)
	//}
	//
	//fileUploadIndex := (&model.FileUpload{}).EsIndexName()
	//fileUploadIdxExistRes, err := tc.Indices.Exists(fileUploadIndex).Do(ctx)
	//if err != nil {
	//	return err
	//}
	//if !fileUploadIdxExistRes {
	//	res, err := tc.Indices.Create(fileUploadIndex).
	//		Request(&create.Request{
	//			Mappings: &types.TypeMapping{
	//				Properties: map[string]types.Property{
	//					"id": types.TextProperty{
	//						Fields: map[string]types.Property{
	//							"keyword": types.KeywordProperty{IgnoreAbove: pointer.Int(256)},
	//						},
	//					},
	//				},
	//			},
	//		}).
	//		Do(nil)
	//	if err != nil {
	//		return err
	//	}
	//	log.Printf("create index in elasticsearch result %v\n", res.Acknowledged)
	//}

	// 		if c.Pkg.Elasticsearchs == nil {
	// 			c.Pkg.Elasticsearchs = map[string]*elasticsearch.TypedClient{}
	// 		}

	// 		c.Pkg.Elasticsearchs[e.Name] = tc
	// 	}
	// }

	if c.Pkg.Middlewares == nil {
		c.Pkg.Middlewares = make([]gin.HandlerFunc, 0)
	}
	// setup cors
	corsMiddleware, _ := setup2.InitCors(ctx, c.Conf.Pkg.Cors)
	c.Pkg.Middlewares = append(c.Pkg.Middlewares, corsMiddleware)

	authMiddleware, _ := setup2.InitAuth(ctx, c.Conf.App.Router)
	c.Pkg.Middlewares = append(c.Pkg.Middlewares, authMiddleware)

	core.Container = c

	return nil
}

func Run(ctx context.Context) error {
	logger, err := core.GetLogger()
	if err != nil {
		return err
	}
	c := core.Container

	locker := &config.Locker{}
	if c.Conf.App.Locker != nil {
		locker = &config.Locker{
			Disabled:  c.Conf.App.Locker.Disabled,
			RedisName: c.Conf.App.Locker.RedisName,
			Times:     c.Conf.App.Locker.Times,
			Timeout:   c.Conf.App.Locker.Timeout,
		}
	} else {
		locker = nil
	}

	if core.GetConf().App.Env == metadata.EnvProd {
		gin.SetMode(gin.ReleaseMode)
	}

	engine := gin.New()
	engine.Use(gin.Recovery())
	//engine.Use(gin.Logger())
	engine.Use(func(c *gin.Context) {
		start := time.Now()
		c.Next()
		logger.Debugf("StatusCode: %d | Time: %d ms | ClientIP: %s | Method: %s | Path: %s", c.Writer.Status(), time.Now().Sub(start).Milliseconds(), c.ClientIP(), c.Request.Method, c.Request.URL.Path)
	})
	//_ = engine.SetTrustedProxies(nil)
	engine.RedirectTrailingSlash = false
	for _, middleware := range c.Pkg.Middlewares {
		engine.Use(middleware)
	}
	serverC := c.Conf.App.Server
	// setup assets
	if serverC.Assets != nil {
		for _, asset := range serverC.Assets {
			if asset.Path != "" && asset.Root != "" {
				engine.Static(asset.Path, asset.Root)
			}
		}
	}

	isPreCompile, _ := ctx.Value("APP_PROJECT_PRE_COMPILE").(bool)
	isBuilt, _ := ctx.Value("APP_PROJECT_BUILT").(bool)

	httpStack := http2.Http(config.Config{
		Logger:    logger.Desugar(),
		RedisPool: c.Pkg.RedisPool,
		Locker:    locker,
	})
	httpStack.RegisterService(router.MountServices()...)
	httpStack.RegisterRoutes(router.MountRoutes)

	engine, err = httpStack.Wrap(ctx, engine, isPreCompile, isBuilt)
	if err != nil {
		return err
	}
	if isPreCompile {
		logger.Info("precompile finished!")
		return nil
	}

	go func() {
		if err := engine.Run(fmt.Sprintf("%s:%d", serverC.SocketAddress, serverC.SocketPort)); err != nil {
			logger.Error(err)
			os.Exit(0)
			return
		}
	}()

	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	<-sigChan
	logger.Infof("stop server")
	c.Pkg.Flake.UnRegister()
	// if c.Pkg.KafkaProducers != nil {
	// 	for _, producer := range c.Pkg.KafkaProducers {
	// 		producer.Close()
	// 	}
	// }
	// if c.Pkg.KafkaConsumers != nil {
	// 	for _, consumer := range c.Pkg.KafkaConsumers {
	// 		_ = consumer.Close()
	// 	}
	// }
	if c.Pkg.Logger != nil {
		for _, sugaredLogger := range c.Pkg.Logger {
			_ = sugaredLogger.Sync()
		}
	}
	return nil
}
