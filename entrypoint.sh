#!/bin/bash

# 获取当前用户信息
CURRENT_UID=$(id -u)
CURRENT_GID=$(id -g)

echo "Running as UID:$CURRENT_UID GID:$CURRENT_GID"

# 如果不是 root 用户，设置 HOME 环境变量
if [ "$CURRENT_UID" != "0" ]; then
    export HOME=/tmp
    # 为当前用户创建 SSH 目录
    mkdir -p $HOME/.ssh
    cp /.ssh/id_rsa $HOME/.ssh/ 2>/dev/null || true
    cp /.ssh/id_rsa.pub $HOME/.ssh/ 2>/dev/null || true
    chmod 600 $HOME/.ssh/id_rsa 2>/dev/null || true
    chmod 644 $HOME/.ssh/id_rsa.pub 2>/dev/null || true
fi

# 创建 nginx 需要的临时目录
mkdir -p /tmp/nginx
mkdir -p /tmp/client_body_temp
mkdir -p /tmp/proxy_temp
mkdir -p /tmp/fastcgi_temp
mkdir -p /tmp/uwsgi_temp
mkdir -p /tmp/scgi_temp

# 1. 后台运行业务服务
./server APP_PROJECT_BUILT &

# 2. 启动 nginx（指定所有路径到 /tmp）
nginx -g "error_log /tmp/nginx_error.log; pid /tmp/nginx.pid; daemon off;"