app:
  env: dev
  debug: true
  domain: ha-50.hk-agent.datasecchk.net
  cookie:
    path: /
    max_age: 2592000
    secure: false
    http_only: false
    same_site: 1
  locker:
    disabled: false
    redis_name: default
    times: 3
    timeout: 5s
  server:
    socket_address: 0.0.0.0
    socket_port: 8999
    assets:
      - path: /res
        root: src/http/res
  router:
    white_list:
      - /api/v1/auth/login
      - /api/v1/rev/mock/sender-provider/get
      - /api/v1/sys/resources
      - /api/v1/platform/audit_notify
  sender:
    upload:
      dst: sender/upload
      remote_url: http://host.docker.internal:30002/api/v1/trans_files
  rev:
    fisco:
      tars:
        src: rev/tars
        archive: rev/archive
      sign_server_url: http://cb_gateway_sdk_bridge:8888
      sign_server_weid: did:weid:101:0xb0f1816a9cdc68a378ce97bb24eacf4991e09e3a
      sign_server_password: 123456
      webhook:
        - addr: http://host.docker.internal:30002/api/v1/receive_notify
          enabled: true
      watermark_api:
        temp_dir: watermark
        addr: http://host.docker.internal:30002/api/v1/add_watermark
        enabled: true
  platform:
    report_url: https://sz-op.datasecchk.net/api/v1/platform/audit_submit
    local_ip: ************
    local_port: 8080
    enabled_report: true
  transport_channel:
    api_addr: http://host.docker.internal:30002/api/v1/channel/channel_info
pkg:
  database:
    - name: default
      driver: mysql #数据库驱动
      host: hk_box_mysql #服务器地址
      port: 3306 #服务器端口
      user: root #数据库用户
      pwd: ChangeMe #数据库密码
      dbname: hk_box
      charset: utf8mb4 
      collation: utf8mb4_general_ci 
  redis:
    - name: default
      addrs:
        - "hk_box_redis:6379"
      db: 1
      username: ~
      password: 123456
      sentinel_password: ~
      max_retries: 3
      pool_fifo: false
      pool_size: 0
      min_idle_conns: 0
      max_redirects: 0
      readonly: false
      route_by_latency: false
      route_randomly: false
      master_mame: ~
      check: true
  cors:
    allowed_origins:
    allowed_methods:
      - "GET"
      - "POST"
      - "PUT"
      - "PATCH"
      - "DELETE"
      - "HEAD"
    allowed_headers:
      - "Origin"
      - "Content-Length"
      - "Content-Type"
      - "X-Requested-With"
      - "Authorization"
      - "Withcredentials"
      - "*"
    allow_credentials: true
    expose_headers:
      - "Content-Disposition"
    max_age: 86400
    allow_websockets: true
  logger:
    - name: default
      file_log:
        filpath: