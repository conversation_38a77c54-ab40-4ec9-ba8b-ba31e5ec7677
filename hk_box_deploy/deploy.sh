#!/usr/bin/env bash
set -euo pipefail

# ===== 可改参数 =====
# DOMAIN
DOMAIN="${DOMAIN:-ha-50.hk-agent.datasecchk.net}"

# MYSQL密码
MYSQL_ROOT_PASSWORD="${MYSQL_ROOT_PASSWORD:-ChangeMe}"

# ===== 预配置 =====
# 资源获取路径
REGISTRY_HOST="${REGISTRY_HOST:-registry.datasecchk.net}"
ARTIFACT_SQL_IMAGE="${ARTIFACT_SQL_IMAGE:-$REGISTRY_HOST/artifacts/hk_box_sql}"
SQL_DIR_IN_IMAGE="${SQL_DIR_IN_IMAGE:-/artifacts/hk_box.sql}"                              

# 项目路径
PROJECT_DIR="${PROJECT_DIR:-$PWD}"
INFRA_DIR="${INFRA_DIR:-$PROJECT_DIR/infra}"

# compose 文件名
PROJECT_COMPOSE="${PROJECT_COMPOSE:-$PROJECT_DIR/docker-compose.yml}"
INFRA_COMPOSE="${INFRA_COMPOSE:-$INFRA_DIR/docker-compose.yml}"

# 当前用户 UID/GID
USER_UID="${USER_UID:-$(id -u)}"
USER_GID="${USER_GID:-$(id -g)}"

# 镜像仓库
REGISTRY_HOST="${REGISTRY_HOST:-registry.datasecchk.net}"

# ===== 工具与日志函数 =====
info() { echo -e "\033[1;32m[info]\033[0m $*"; }
warn() { echo -e "\033[1;33m[warn]\033[0m $*"; }
die()  { echo -e "\033[1;31m[err ]\033[0m $*" >&2; exit 1; }


# ===== 依赖检测 =====
command -v docker >/dev/null 2>&1 || die "未找到 docker 命令"
if docker compose version >/dev/null 2>&1; then
  COMPOSE="docker compose"
elif docker-compose version >/dev/null 2>&1; then
  COMPOSE="docker-compose"
else
  die "未找到 docker compose。请安装 Docker Compose v2 或旧版 docker-compose。"
fi

# ===== 通过制品镜像拉取并解包（首次） =====
bootstrap_from_artifacts() {
  local target="$INFRA_DIR/mysql/initdb.d"
  mkdir -p "$target"

  info "拉取制品镜像： $ARTIFACT_SQL_IMAGE"
  docker pull "$ARTIFACT_SQL_IMAGE"

  info "解包 sql 制品到: $target ..."
  docker run --rm -v "$target":/out "$ARTIFACT_SQL_IMAGE" \
    sh -c "cp $SQL_DIR_IN_IMAGE /out/hk_box.sql"

  info "制品解包完成"
}

# 若本地尚未具备项目文件，则自动引导
if [ ! -e "$INFRA_DIR/mysql/initdb.d/hk_box.sql" ] ; then
  info "未发现完整项目文件，开始通过制品镜像引导..."
  bootstrap_from_artifacts
fi

# ===== 确保 external 网络存在 =====
NETWORK_NAME="cnix"
SUBNET="172.30.0.0/24"
GATEWAY="172.30.0.1"

if ! docker network inspect "$NETWORK_NAME" >/dev/null 2>&1; then
  echo "[INFO] 创建网络 $NETWORK_NAME..."
  docker network create --driver bridge --subnet "$SUBNET" --gateway "$GATEWAY" "$NETWORK_NAME"
else
  echo "[INFO] 网络已存在：$NETWORK_NAME"
fi

# ===== 项目侧挂载目录 =====
mkdir -p \
  "$PROJECT_DIR/watermark" \
  "$PROJECT_DIR/rev/tars" \
  "$PROJECT_DIR/rev/archive"

# ===== infra 下需要的目录 =====
mkdir -p \
  "$INFRA_DIR/mysql/conf.d" \
  "$INFRA_DIR/mysql/datadir" \
  "$INFRA_DIR/mysql/initdb.d" \
  "$INFRA_DIR/redis/conf" \
  "$INFRA_DIR/redis/data" \
  "$INFRA_DIR/caddy"

touch $INFRA_DIR/redis/conf/redis.conf

# ===== 创建 Caddyfile 配置 =====
cat > "$INFRA_DIR/caddy/Caddyfile" <<CADDY
{
    auto_https off
}

${DOMAIN}:443 {
  reverse_proxy hk_box:80

  tls {
    get_certificate http https://acme.datasecchk.net/getcert
  }
}
CADDY

# ===== 启动 infra =====
info "启动 infra: $INFRA_DIR/$INFRA_COMPOSE（--pull always）"
(
  cd "$INFRA_DIR"
  USER_UID="$USER_UID" USER_GID="$USER_GID" \
  $COMPOSE -f "$INFRA_COMPOSE" up -d --pull always
)

# 给 MySQL 预留时间初始化（todo: healthcheck replace 60s waiting）
info "等待以确保 MySQL 就绪..."
sleep 90

# ===== 启动项目 =====
if [ -f "$PROJECT_COMPOSE" ]; then
  info "启动 project: $PROJECT_COMPOSE（--pull always）"
  (
    cd "$PROJECT_DIR"
    USER_UID="$USER_UID" USER_GID="$USER_GID" \
    $COMPOSE -f "$PROJECT_COMPOSE" up -d --pull always
  )
else
  warn "未找到项目 compose（$PROJECT_COMPOSE），仅启动了 infra"
fi

info "部署完成"
