services:
  hk_box:
    container_name: hk_box
    user: "${USER_UID}:${USER_GID}"
    image: registry.datasecchk.net/library/hk-box-be:feature-v1.3-a1c5f9bc
    volumes:
      - ./conf.yaml:/app/conf.yaml
      - /appl/ridgba/data/rev/watermark:/app/watermark
      - /appl/ridgba/data/rev/tars:/app/rev/tars
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "10"
    extra_hosts:
      - "host.docker.internal:host-gateway"
    networks:
      cnix:
        ipv4_address: ***********

networks:
  cnix:
    external: true