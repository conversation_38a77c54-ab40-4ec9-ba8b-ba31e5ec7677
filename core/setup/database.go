package setup

import (
	"code.ixdev.cn/cnix/cbdv/hk-box-be/core/config/pkg"
	"context"
	"errors"
	"fmt"
	"go.uber.org/zap"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"strings"
	"time"
)

var (
	ErrDatabaseNameEmpty         = errors.New("database name cannot be empty")
	ErrDatabaseDriverUnsupported = errors.New("database driver unsupported")
)

type ZapGormLogger struct {
	ZapLogger *zap.SugaredLogger
}

func (l *ZapGormLogger) Printf(msg string, data ...interface{}) {
	l.ZapLogger.Debugf(msg, data...)
}

func InitDatabase(_ context.Context, database *pkg.Database, zapGormLogger *ZapGormLogger) (*gorm.DB, error) {
	database.Name = strings.TrimSpace(database.Name)
	if database.Name == "" {
		return nil, ErrDatabaseNameEmpty
	}

	var _logger logger.Interface
	if zapGormLogger != nil {
		_logger = logger.New(zapGormLogger, logger.Config{
			SlowThreshold:             200 * time.Millisecond,
			LogLevel:                  logger.Warn,
			IgnoreRecordNotFoundError: false,
			Colorful:                  false,
		})
	}

	switch database.Driver {
	case "mysql":
		dsn := database.Dsn
		if database.Dsn == "" {
			dsn = fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=%t&loc=%s",
				database.User,
				database.Pwd,
				database.Host,
				database.Port,
				database.DbName,
				database.Charset,
				true,
				"Local",
			)
		}
		orm, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
			SkipDefaultTransaction:                   false,
			NamingStrategy:                           nil,
			FullSaveAssociations:                     false,
			Logger:                                   _logger,
			NowFunc:                                  nil,
			DryRun:                                   false,
			PrepareStmt:                              false,
			DisableAutomaticPing:                     false,
			DisableForeignKeyConstraintWhenMigrating: false,
			IgnoreRelationshipsWhenMigrating:         false,
			DisableNestedTransaction:                 false,
			AllowGlobalUpdate:                        false,
			QueryFields:                              false,
			CreateBatchSize:                          0,
			TranslateError:                           false,
			ClauseBuilders:                           nil,
			ConnPool:                                 nil,
			Dialector:                                nil,
			Plugins:                                  nil,
		})
		if err != nil {
			return nil, err
		}
		return orm, nil
	default:
		return nil, ErrDatabaseDriverUnsupported
	}
}
