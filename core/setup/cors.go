package setup

import (
	"code.ixdev.cn/cnix/cbdv/hk-box-be/core/config/pkg"
	"context"
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"time"
)

func InitCors(_ context.Context, cs *pkg.Cors) (gin.<PERSON>un<PERSON>, error) {
	cfg := cors.DefaultConfig()
	cfg.AllowAllOrigins = true
	if cs != nil {
		if cs.AllowedOrigins != nil {
			cfg.AllowAllOrigins = false
			cfg.AllowOrigins = cs.AllowedOrigins
		}
		if cs.AllowedMethods != nil {
			cfg.AllowMethods = cs.AllowedMethods
		}
		if cs.AllowedHeaders != nil {
			cfg.AllowHeaders = cs.AllowedHeaders
		}
		if cs.AllowCredentials != nil {
			cfg.AllowCredentials = *cs.AllowCredentials
		}
		if cs.ExposeHeaders != nil {
			cfg.ExposeHeaders = cs.ExposeHeaders
		}
		if cs.MaxAge != nil {
			cfg.MaxAge = *cs.MaxAge * time.Second
		}
		if cs.AllowWebsockets != nil {
			cfg.AllowWebSockets = *cs.AllowWebsockets
		}
	}
	return cors.New(cfg), nil
}
