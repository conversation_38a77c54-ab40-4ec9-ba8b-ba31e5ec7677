package setup

import (
	"code.ixdev.cn/cnix/cbdv/hk-box-be/core/config/pkg"
	"context"
	"github.com/natefinch/lumberjack"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"os"
	"path/filepath"
)

func InitLogger(_ context.Context, development bool, debug bool, config *pkg.Logger) (*zap.Logger, error) {
	level := zapcore.InfoLevel
	if debug {
		level = zapcore.DebugLevel
	}

	cores := make([]zapcore.Core, 0)

	consoleEncoderConfig := zapcore.EncoderConfig{
		MessageKey:     "M",
		LevelKey:       "L",
		TimeKey:        "T",
		NameKey:        "N",
		CallerKey:      "C",
		FunctionKey:    zapcore.OmitKey,
		StacktraceKey:  "S",
		SkipLineEnding: false,
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.LowercaseColorLevelEncoder,
		EncodeTime:     zapcore.RFC3339TimeEncoder,
		EncodeDuration: zapcore.SecondsDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}
	consoleEncoder := zapcore.NewConsoleEncoder(consoleEncoderConfig)

	cores = append(cores, zapcore.NewCore(consoleEncoder, zapcore.AddSync(os.Stdout), level))

	if config.FileLog != nil {
		cfg := zap.NewProductionEncoderConfig()
		if development {
			cfg = zap.NewDevelopmentEncoderConfig()
		}
		JSONEncoder := zapcore.NewJSONEncoder(cfg)

		logFilepath := "/app/logs/app.log"
		if config.FileLog.Filepath != "" {
			logFilepath = config.FileLog.Filepath
		}
		_ = os.MkdirAll(filepath.Dir(logFilepath), os.ModePerm)

		logFileMaxSize := config.FileLog.MaxSize
		if logFileMaxSize <= 0 {
			logFileMaxSize = 10
		}
		logFileMaxAge := config.FileLog.MaxAge
		if logFileMaxAge <= 0 {
			logFileMaxAge = 30
		}
		logFileMaxBackups := config.FileLog.MaxBackups
		if logFileMaxBackups <= 0 {
			logFileMaxBackups = 30
		}

		writer := zapcore.AddSync(&lumberjack.Logger{
			Filename:   logFilepath,
			MaxSize:    logFileMaxSize,
			MaxAge:     logFileMaxAge,
			MaxBackups: logFileMaxBackups,
			LocalTime:  config.FileLog.LocalTime,
			Compress:   config.FileLog.Compress,
		})

		cores = append(cores, zapcore.NewCore(JSONEncoder, writer, level))
	}

	core := zapcore.NewTee(cores...)

	logger := zap.New(core, zap.AddCaller(), zap.AddStacktrace(zap.WarnLevel))

	return logger, nil
}
