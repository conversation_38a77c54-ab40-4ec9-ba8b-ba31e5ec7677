package setup

import (
	"code.ixdev.cn/cnix/cbdv/hk-box-be/core/config/app"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/global/metadata"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/src/http/validator/auth"
	"code.ixdev.cn/cnix/cbdv/hk-box-be/utils/authutil"
	"code.ixdev.cn/liush/x/message"
	errors2 "code.ixdev.cn/liush/xpb/errors"
	"context"
	"errors"
	"github.com/gin-gonic/gin"
	"regexp"
	"strings"
	"time"
)

func InitAuth(_ context.Context, routerCfg *app.Router) (gin.HandlerFunc, error) {
	return func(c *gin.Context) {
		// 验证是否需要登录
		if !inWhiteListRoutes(c.Request.URL.Path, routerCfg) {
			token := ""
			if tokenCookie, _ := c.Request.Cookie("m_auth_token"); tokenCookie != nil {
				token = tokenCookie.Value
			}

			token = strings.TrimSpace(token)
			if token == "" {
				message.Error(c, auth.ErrCodeAuthTokenExpired, errors.New("请登录"))
				return
			}
			authUid, err := authutil.ParseAppToken(c, token)
			if err != nil {
				message.Error(c, auth.ErrCodeAuthTokenExpired, errors.New("登录已过期"))
				return
			}

			c.Set(metadata.UidKey, authUid)
			c.Set(metadata.TokenKey, token)

			if c.Request.URL.Path != "/api/v1/auth/logout" {
				now := time.Now()
				expiresAt := now.AddDate(0, 0, 1)
				err = authutil.SetTokenTtl(c, token, expiresAt.Sub(now))
				if err != nil {
					verr := errors2.Parse(err.Error())
					message.Error(c, int(verr.Code), errors.New(verr.Detail))
					return
				}
				cookieMaxAge := int(expiresAt.Sub(now).Seconds())
				authutil.SetCookie(c.Writer, "m_auth_token", token, cookieMaxAge)
				authutil.SetCookie(c.Writer, "m_auth_uid", authUid, cookieMaxAge)
			}
		}
		c.Next()
	}, nil
}

func inWhiteListRoutes(p string, routerCfg *app.Router) bool {
	if routerCfg == nil {
		routerCfg = new(app.Router)
	}
	whiteList := routerCfg.WhiteList
	if whiteList == nil {
		whiteList = make([]string, 0)
	}
	ok := false
	for _, s := range whiteList {
		if regexp.MustCompile(s).MatchString(p) {
			ok = true
			break
		}
	}
	return ok
}
