package pkg

type Config struct {
	Database []*Database `json:"database" xml:"database" yaml:"database"`
	Logger   []*<PERSON>gger   `json:"logger" xml:"logger" yaml:"logger"`
	Redis    []*Redis    `json:"redis" xml:"redis" yaml:"redis"`
	Cors     *Cors       `json:"cors" xml:"cors" yaml:"cors"`
	// Kafka         *Kafka           `json:"kafka" xml:"kafka" yaml:"kafka"`
	// Elasticsearch []*Elasticsearch `json:"elasticsearch" xml:"elasticsearch" yaml:"elasticsearch"`
}
