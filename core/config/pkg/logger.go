package pkg

type LoggerFileConfig struct {
	Filepath   string `json:"filepath" xml:"filepath" yaml:"filepath"`
	MaxSize    int    `json:"max_size" xml:"max_size" yaml:"max_size"`
	MaxAge     int    `json:"max_age" xml:"max_age" yaml:"max_age"`
	MaxBackups int    `json:"max_backups" xml:"max_backups" yaml:"max_backups"`
	Compress   bool   `json:"compress" xml:"compress" yaml:"compress"`
	LocalTime  bool   `json:"local_time" xml:"local_time" yaml:"local_time"`
}

type Logger struct {
	Name    string            `json:"name" xml:"name" yaml:"name"`
	FileLog *LoggerFileConfig `json:"file_log" xml:"file_log" yaml:"file_log"`
}
