package pkg

import "time"

type Cors struct {
	AllowedOrigins   []string       `json:"allowed_origins" xml:"allowed_origins" yaml:"allowed_origins"`
	AllowedMethods   []string       `json:"allowed_methods" xml:"allowed_methods" yaml:"allowed_methods"`
	AllowedHeaders   []string       `json:"allowed_headers" xml:"allowed_headers" yaml:"allowed_headers"`
	AllowCredentials *bool          `json:"allow_credentials" xml:"allow_credentials" yaml:"allow_credentials"`
	ExposeHeaders    []string       `json:"expose_headers" xml:"expose_headers" yaml:"expose_headers"`
	MaxAge           *time.Duration `json:"max_age" xml:"max_age" yaml:"max_age"`
	AllowWebsockets  *bool          `json:"allow_websockets" xml:"allow_websockets" yaml:"allow_websockets"`
}
