package pkg

// import (
// 	"github.com/confluentinc/confluent-kafka-go/v2/kafka"
// )

// type Kafka struct {
// 	Producer []struct {
// 		Name      string           `json:"name" xml:"name" yaml:"name"`
// 		ConfigMap *kafka.ConfigMap `json:"config_map" xml:"config_map" yaml:"config_map"`
// 	} `json:"producer" xml:"producer" yaml:"producer"`
// 	Consumer []struct {
// 		Name      string           `json:"name" xml:"name" yaml:"name"`
// 		ConfigMap *kafka.ConfigMap `json:"config_map" xml:"config_map" yaml:"config_map"`
// 		//SubscribeTopics    []string         `json:"subscribe_topics" xml:"subscribe_topics" yaml:"subscribe_topics"`
// 		//ReadMessageTimeout time.Duration `json:"read_message_timeout" xml:"read_message_timeout" yaml:"read_message_timeout"`
// 	} `json:"consumer" xml:"consumer" yaml:"consumer"`
// }
