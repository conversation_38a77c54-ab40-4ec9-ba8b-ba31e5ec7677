package app

type Cookie struct {
	Path     string `json:"path" xml:"path" yaml:"path"`
	Domain   string `json:"domain" xml:"domain" yaml:"domain"`
	MaxAge   int    `json:"max_age" xml:"max_age" yaml:"max_age"`
	Secure   bool   `json:"secure" xml:"secure" yaml:"secure"`
	HttpOnly bool   `json:"http_only" xml:"http_only" yaml:"http_only"`
	SameSite int    `json:"same_site" xml:"same_site" yaml:"same_site"`
}
