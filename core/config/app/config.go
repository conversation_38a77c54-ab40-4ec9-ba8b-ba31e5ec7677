package app

// Config app配置
type Config struct {
	Env              string            `json:"env" xml:"env" yaml:"env"`                            // 环境
	Debug            bool              `json:"debug" xml:"debug" yaml:"debug"`                      // debug模式
	Domain           string            `json:"domain" xml:"domain" yaml:"domain"`                   // 域名
	Cookie           *Cookie           `json:"cookie" xml:"cookie" yaml:"cookie"`                   // cookie配置
	Locker           *Locker           `json:"locker" xml:"locker" yaml:"locker"`                   // 分布式锁
	Server           *Server           `json:"server" xml:"server" yaml:"server"`                   // 服务自身配置
	IdGenerator      *IdGenerator      `json:"id_generator" xml:"id_generator" yaml:"id_generator"` // Id生成器配置
	Router           *Router           `json:"router" xml:"router" yaml:"router"`                   // 路由
	Sender           *Sender           `json:"sender" xml:"sender" yaml:"sender"`                   // 发送
	Rev              *Rev              `json:"rev" xml:"rev" yaml:"rev"`                            // 接收
	Platform         *Platform         `json:"platform" xml:"platform" yaml:"platform"`             // 运营平台
	TransportChannel *TransportChannel `json:"transport_channel" xml:"transport_channel" yaml:"transport_channel"`
}
