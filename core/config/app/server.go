package app

type ServerAssets struct {
	Path string `json:"path" xml:"path" yaml:"path"`
	Root string `json:"root" xml:"root" yaml:"root"`
}

type Server struct {
	SocketAddress string          `json:"socket_address" xml:"socket_address" yaml:"socket_address"`
	SocketPort    uint16          `json:"socket_port" xml:"socket_port" yaml:"socket_port"`
	Assets        []*ServerAssets `json:"assets" xml:"assets" yaml:"assets"`
}
