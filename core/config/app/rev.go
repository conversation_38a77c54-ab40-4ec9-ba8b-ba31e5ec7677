package app

type RevFiscoTars struct {
	Src     string `json:"src" xml:"src" yaml:"src"`
	Archive string `json:"archive" xml:"archive" yaml:"archive"` // 归档目录
}

type RevFiscoWebhook struct {
	Addr    string `json:"addr" xml:"addr" yaml:"addr"`
	Enabled bool   `json:"enabled" xml:"enabled" yaml:"enabled"`
}

type RevFiscoWatermarkApi struct {
	TempDir string `json:"temp_dir" xml:"temp_dir" yaml:"temp_dir"`
	Addr    string `json:"addr" xml:"addr" yaml:"addr"`
	Enabled bool   `json:"enabled" xml:"enabled" yaml:"enabled"`
}

type RevFisco struct {
	Tars               *RevFiscoTars         `json:"tars" xml:"tars" yaml:"tars"`
	SignServerUrl      string                `json:"sign_server_url" xml:"sign_server_url" yaml:"sign_server_url"`
	SignServerWeid     string                `json:"sign_server_weid" xml:"sign_server_weid" yaml:"sign_server_weid"`
	SignServerPassword string                `json:"sign_server_password" xml:"sign_server_password" yaml:"sign_server_password"`
	Webhook            []*RevFiscoWebhook    `json:"webhook" xml:"webhook" yaml:"webhook"`
	WatermarkApi       *RevFiscoWatermarkApi `json:"watermark_api" xml:"watermark_api" yaml:"watermark_api"`
}

type Rev struct {
	Fisco *RevFisco `json:"fisco" xml:"fisco" yaml:"fisco"`
}
